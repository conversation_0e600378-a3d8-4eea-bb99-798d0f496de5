{"celery-beat": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color beat --pidfile=/tmp/celerybeat.pid --schedule=/tmp/celerybeat-schedule"}, "tag": "celery_01"}, "celery-default": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q default --concurrency=3 -n default"}, "tag": "celery_01"}, "celery-handle-push-message": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q handle_push_message --concurrency=3 -n handle_push_message"}, "tag": "celery_01"}, "celery-handle-push-messages": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q handle_push_messages --concurrency=3 -n handle_push_messages"}, "tag": "celery_01"}, "celery-update-withdraw-request": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q update_withdraw_request --concurrency=3 -n update_withdraw_request"}, "tag": "celery_01"}, "celery-update-withdraw-requests": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q update_withdraw_requests --concurrency=3 -n update_withdraw_requests"}, "tag": "celery_01"}, "celery-trading": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q trading --concurrency=3 -n trading"}, "tag": "celery_01"}, "celery-import-coins": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q import_coins --concurrency=3 -n import_coins"}, "tag": "celery_01"}, "celery-snapshot-custody-balance": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q snapshot_custody_balance --concurrency=3 -n snapshot_custody_balance"}, "tag": "celery_01"}, "celery-update-withdraw-request-v2": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q update_withdraw_request_v2 --concurrency=15 -n update_withdraw_request_v2"}, "tag": "celery_01"}, "celery-metrics": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q metrics --concurrency=3 -n metrics"}, "tag": "celery_01"}, "celery-wallet-balance-monitor": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q wallet_balance_monitor --concurrency=3 -n wallet_balance_monitor"}, "tag": "celery_01"}, "celery-too-many-messages-alert": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q too_many_messages_alert --concurrency=3 -n too_many_messages_alert"}, "tag": "celery_01"}, "celery-financial-queue": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q financial_queue --concurrency=3 -n financial_queue"}, "tag": "celery_01"}, "celery-legend-kyc-queue": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q legend_kyc_queue --concurrency=3 -n legend_kyc_queue"}, "tag": "celery_01"}, "celery-send-to-blockchain": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q send_to_blockchain --concurrency=3 -n send_to_blockchain"}, "tag": "celery_01"}, "celery-transaction-query": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q transaction_query --concurrency=3 -n transaction_query"}, "tag": "celery_01"}, "celery-waas2-exchange-queue": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q waas2_exchange_wallet_queue --concurrency=3 -n waas2_exchange_wallet_queue"}, "tag": "celery_01"}, "celery-update-session-queue": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q update_session_queue --concurrency=3 -n update_session_queue"}, "tag": "celery_01"}, "celery-tracker-transaction-queue": {"template": "program", "settings": {"command": "celery -A custody.cobo --no-color worker -Q tracker_transaction_queue --concurrency=3 -n tracker_transaction_queue"}, "tag": "celery_01"}, "risk-event": {"template": "program", "settings": {"command": "python3 manage.py risk_event_sync_service"}, "tag": "service_01"}, "sync-coins": {"template": "program", "settings": {"command": "python3 manage.py sync_coins -t custody"}, "tag": "service_01"}, "sync-support-coins": {"template": "program", "settings": {"command": "python3 manage.py sync_support_coins -v2"}, "tag": "service_01"}, "sync-eoa-coins": {"template": "program", "settings": {"command": "python3 manage.py sync_coins -t eoa"}, "tag": "service_01"}, "sync-chains": {"template": "program", "settings": {"command": "python3 manage.py sync_chains"}, "tag": "service_01"}, "event-collection": {"template": "program", "settings": {"command": "python3 manage.py event_collection"}, "tag": "service_01"}, "auth-service-requester": {"template": "program", "settings": {"command": "python3 manage.py auth_service -ha auth_requester"}, "tag": "service_01"}, "auth-service-checker": {"template": "program", "settings": {"command": "python3 manage.py auth_service -ha auth_checker"}, "tag": "service_01"}, "auth-chain-syncer": {"template": "program", "settings": {"command": "python3 manage.py auth_chain -w syncer"}, "tag": "service_01"}, "auth-chain-monitor": {"template": "program", "settings": {"command": "python3 manage.py auth_chain -w monitor"}, "tag": "service_01"}, "blockchain-request-service": {"template": "program", "settings": {"command": "python3 manage.py blockchain_request_service -a -et web3,mpc,mpc_superloop,exchange"}, "tag": "service_01"}, "blockchain-request-service-mpc-superloop": {"template": "program", "settings": {"command": "python3 manage.py blockchain_request_service -a -it mpc_superloop -eo 104"}, "tag": "service_03"}, "custody-request-service-double-check": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -s 0 -et web3,mpc,mpc_superloop,exchange,smart_contract"}, "tag": "service_01"}, "custody-request-service-cobo-check": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -s -2 -et web3,mpc,mpc_superloop,exchange,smart_contract"}, "tag": "service_01"}, "custody-request-service-pending": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -s 1 -et web3,mpc,mpc_superloop,exchange,smart_contract"}, "tag": "service_01"}, "custody-request-service-transfer": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -s -3 -et web3,mpc,mpc_superloop,exchange,smart_contract"}, "tag": "service_01"}, "custody-request-service-waiting-gas-station-main-tx": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -s -7 -et web3,mpc,mpc_superloop,exchange,smart_contract"}, "tag": "service_01"}, "custody-request-service-mpc-superloop": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -it mpc_superloop -eo 104"}, "tag": "service_01"}, "custody-request-service-compliance-send-v1": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -s -9 -et web3,mpc,mpc_superloop,exchange,smart_contract"}, "tag": "service_01"}, "custody-request-service-compliance-checking-v1": {"template": "program", "settings": {"command": "python3 manage.py custody_request_service -s -8 -et web3,mpc,mpc_superloop,exchange,smart_contract"}, "tag": "service_01"}, "custody-request-service-web3-v2-default": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it web3 -es=-2,1"}, "tag": "service_01"}, "custody-request-service-web3-v2-cobo-check": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it web3 -s -2"}, "tag": "service_01"}, "custody-request-service-web3-v2-pending": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it web3 -s 1"}, "tag": "service_01"}, "custody-request-service-mpc-v2-default": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it mpc -es=-2,1"}, "tag": "service_01"}, "custody-request-service-mpc-v2-cobo-check": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it mpc -s -2"}, "tag": "service_01"}, "custody-request-service-mpc-v2-pending": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it mpc -s 1"}, "tag": "service_01"}, "custody-request-service-mpc-superloop-v2": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it mpc_superloop -io 104"}, "tag": "service_01"}, "custody-request-service-exchange-v2": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it exchange"}, "tag": "service_01"}, "custody-request-service-compliance-send-v2": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -s -9 -et custodial_asset"}, "tag": "service_01"}, "custody-request-service-compliance-checking-v2": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -s -8 -et custodial_asset"}, "tag": "service_01"}, "waas2-compliance-request-service": {"template": "program", "settings": {"command": "python3 manage.py compliance_request -i 1"}, "tag": "service_01"}, "waas2-app-check-service": {"template": "program", "settings": {"command": "python3 manage.py app_check_service -i 1"}, "tag": "service_01"}, "waas2-compliance-black-address-service": {"template": "program", "settings": {"command": "python3 manage.py sync_blackaddress -i 5"}, "tag": "service_03"}, "waas2-compliance-increment-calc-service": {"template": "program", "settings": {"command": "python3 manage.py increment_calc -i 10"}, "tag": "service_03"}, "transaction-request-service": {"template": "program", "settings": {"command": "python3 manage.py transaction_request_service"}, "tag": "service_01"}, "financial-term-service": {"template": "program", "settings": {"command": "python3 manage.py term_service -i 1"}, "tag": "service_01"}, "financial-demand-service": {"template": "program", "settings": {"command": "python3 manage.py demand_service -i 1"}, "tag": "service_01"}, "financial-defi-service": {"template": "program", "settings": {"command": "python3 manage.py defi_service -i 1"}, "tag": "service_01"}, "mmi-service": {"template": "program", "settings": {"command": "python3 manage.py mmi_service"}, "tag": "service_01"}, "tss-request-service-check": {"template": "program", "settings": {"command": "python3 manage.py tss_request_service -s 10,20"}, "tag": "service_01"}, "tss-request-service-send": {"template": "program", "settings": {"command": "python3 manage.py tss_request_service -s 21"}, "tag": "service_01"}, "tss-request-service-v2-check": {"template": "program", "settings": {"command": "python3 manage.py tss_request_service_v2 -s 200"}, "tag": "service_01"}, "tss-request-service-v2-send": {"template": "program", "settings": {"command": "python3 manage.py tss_request_service_v2 -s 300"}, "tag": "service_01"}, "tss-request-service-v2-confirm": {"template": "program", "settings": {"command": "python3 manage.py tss_request_service_v2 -s 103"}, "tag": "service_01"}, "metrics": {"template": "program", "settings": {"command": "python3 manage.py metrics -b 50 -i 10"}, "tag": "service_02"}, "market-price-cmc": {"template": "program", "settings": {"command": "python3 manage.py market_price -t cmc -u usd -u cny"}, "tag": "service_02"}, "fetch-ticker-data": {"template": "program", "settings": {"command": "python3 manage.py fetch_ticker_data"}, "tag": "service_02"}, "cache-refresher": {"template": "program", "settings": {"command": "python3 manage.py cache_refresher"}, "tag": "service_02"}, "notification-email": {"template": "program", "settings": {"command": "python3 manage.py notification -t email -i 5"}, "tag": "service_02"}, "notification-sms": {"template": "program", "settings": {"command": "python3 manage.py notification -t sms -i 5"}, "tag": "service_02"}, "notification-pagerduty": {"template": "program", "settings": {"command": "python3 manage.py notification -t pagerduty -i 60"}, "tag": "service_02"}, "notification-slack": {"template": "program", "settings": {"command": "python3 manage.py notification -t slack -i 5"}, "tag": "service_02"}, "submit-bookkeeping-record-asc": {"template": "program", "settings": {"command": "python3 manage.py reconciliation_submit_service -i 5 --database_time_buffer 1800 --force_rewrite"}, "tag": "service_02"}, "monitor-celery-queue": {"template": "program", "settings": {"command": "python3 manage.py monitor_celery_queue -i 120"}, "tag": "service_02"}, "package-process-service": {"template": "program", "settings": {"command": "python3 manage.py package_process -i 5"}, "tag": "service_02"}, "portal-notification-service": {"template": "program", "settings": {"command": "python3 manage.py portal_notification -i 5"}, "tag": "service_02"}, "blockchain-message-service-custody-async": {"template": "program", "settings": {"command": "python3 manage.py blockchain_message_service_async -m custody --all-coins"}, "tag": "service_03"}, "blockchain-message-service-eoa-async": {"template": "program", "settings": {"command": "python3 manage.py blockchain_message_service_async -m eoa --all-coins"}, "tag": "service_03"}, "blockchain-message-service-custody-retry-async": {"template": "program", "settings": {"command": "python3 manage.py blockchain_message_service_async -m custody --all-coins --retry -i 3"}, "tag": "service_03"}, "blockchain-message-service-eoa-retry-async": {"template": "program", "settings": {"command": "python3 manage.py blockchain_message_service_async -m eoa --all-coins --retry -i 3"}, "tag": "service_03"}, "waas2-exchange-refresh-assets-service": {"template": "program", "settings": {"command": "python3 manage.py refresh_assets_service -i 60 -l 20"}, "tag": "service_01"}, "waas2-exchange-refresh-txs-service": {"template": "program", "settings": {"command": "python3 manage.py refresh_txs_service -i 20 -l 20"}, "tag": "service_01"}, "waas2-exchange-txs-handle-service": {"template": "program", "settings": {"command": "python3 manage.py handle_exchange_tx_request -i 1"}, "tag": "service_01"}, "webhook-notifier-service": {"template": "program", "settings": {"command": "python3 manage.py webhook_notifier_service -i 1 -s 100"}, "tag": "service_03"}, "transaction-query-service-03": {"template": "program", "settings": {"command": "python3 manage.py transaction_query_service --enable-local-metrics"}, "tag": "service_03"}, "transaction-query-service-02": {"template": "program", "settings": {"command": "python3 manage.py transaction_query_service --enable-local-metrics"}, "tag": "service_02"}, "transaction-export-service": {"template": "program", "settings": {"command": "python3 manage.py transaction_export_service"}, "tag": "service_03"}, "channel": {"template": "fcgi-program", "settings": {"command": "daphne -u /tmp/daphne%(process_num)d.sock --fd 0 --access-log - custody.cobo.asgi:application"}, "tag": "channel"}, "risk-sync-safe-multisig-tx-service": {"template": "program", "settings": {"command": "python3 manage.py sync_safe_multisig_tx -i 10 -c BSC_BNB"}, "tag": "service_03"}, "refresh-smart-contract-wallet-balance-service": {"template": "program", "settings": {"command": "python3 manage.py refresh_smart_contract_wallet_balance -i 10 -c BSC_BNB"}, "tag": "service_03"}, "safe-wallet-refresh-info-service": {"template": "program", "settings": {"command": "python3 manage.py refresh_safe_wallet_info -i 12"}, "tag": "service_03"}, "risk-control-request-service": {"template": "program", "settings": {"command": "python3 manage.py risk_control_result -i 1"}, "tag": "service_03"}, "custody-request-service-smart-contract-v2": {"template": "program", "settings": {"command": "python3 manage.py custody_withdraw_request_service -it smart_contract"}, "tag": "service_03"}, "travel-rule-service": {"template": "program", "settings": {"command": "python3 manage.py travel_rule_service"}, "tag": "service_03"}, "travel-rule-vendor-service": {"template": "program", "settings": {"command": "python3 manage.py travel_rule_vendor_service"}, "tag": "service_03"}, "babylon-request-service": {"template": "program", "settings": {"command": "python3 manage.py babylon_request_service -i 20"}, "tag": "service_01"}, "babylon-staking-service": {"template": "program", "settings": {"command": "python3 manage.py babylon_staking_service"}, "tag": "service_03"}, "bithive-staking-service": {"template": "program", "settings": {"command": "python3 manage.py bithive_staking_service -i 60"}, "tag": "service_03"}, "adjust-balance-service": {"template": "program", "settings": {"command": "python3 manage.py adjust_balance"}, "tag": "service_01"}, "evm-infra-service": {"template": "program", "settings": {"command": "python3 manage.py evm_infra_service"}, "tag": "service_01"}, "key-share-verification-service": {"template": "program", "settings": {"command": "python3 manage.py key_share_verification_service -i 60"}, "tag": "service_01"}, "swap-activity-service": {"template": "program", "settings": {"command": "python3 manage.py swap_activity_service -i 10"}, "tag": "service_01"}, "eth-staking-service": {"template": "program", "settings": {"command": "python3 manage.py eth_staking_service"}, "tag": "service_01"}, "core-staking-service": {"template": "program", "settings": {"command": "python3 manage.py core_staking_service -i 60"}, "tag": "service_01"}, "balance-auto-sweep-policy-service": {"template": "program", "settings": {"command": "python3 manage.py auto_sweep_policy_service -it TokenBalanceThreshold -i 3600"}, "tag": "service_01"}, "address-chain-balance-auto-sweep-policy-service": {"template": "program", "settings": {"command": "python3 manage.py auto_sweep_policy_service -it AddressChainBalanceThreshold -i 600"}, "tag": "service_01"}, "timing-auto-sweep-policy-service": {"template": "program", "settings": {"command": "python3 manage.py auto_sweep_policy_service -it TimingSweep -i 30"}, "tag": "service_01"}, "official-safe-multisig-tx-sync-service": {"template": "program", "settings": {"command": "python3 manage.py sync_safe_multisig_transactions -i 10 -s OfficialService,OfficialClient"}, "tag": "service_03"}, "custom-safe-multisig-tx-sync-service": {"template": "program", "settings": {"command": "python3 manage.py sync_safe_multisig_transactions -i 10 -s CustomService"}, "tag": "service_03"}, "token_chain_comparison": {"template": "program", "settings": {"command": "python3 manage.py token_chain_comparison -i 600"}, "tag": "service_03"}, "fee-station-service": {"template": "program", "settings": {"command": "python3 manage.py fee_station_service -i 60"}, "tag": "service_03"}, "fee-engine-service": {"template": "program", "settings": {"command": "python3 manage.py fee_engine_service -i 60 -s 1"}, "tag": "service_03"}, "babylon-create-staking-eligibility": {"template": "program", "settings": {"command": "python3 manage.py babylon_create_staking_eligibility -i 10"}, "tag": "service_03"}, "waas2_cache_refresher": {"template": "program", "settings": {"command": "python3 manage.py waas2_cache_refresher"}, "tag": "service_03"}, "payment_order_monitor": {"template": "program", "settings": {"command": "python3 manage.py payment_order_monitor -i 10"}, "tag": "service_03"}, "payment_tx_event": {"template": "program", "settings": {"command": "python3 manage.py payment_tx_event -i 10"}, "tag": "service_03"}, "payment_tx_generation": {"template": "program", "settings": {"command": "python3 manage.py payment_tx_generation -i 10"}, "tag": "service_03"}, "payment_force_sweep": {"template": "program", "settings": {"command": "python3 manage.py payment_force_sweep -i 10"}, "tag": "service_03"}, "tron-resource-service": {"template": "program", "settings": {"command": "python3 manage.py tron_resource_service -i 2"}, "tag": "service_03"}, "tokenization-token-sync-service": {"template": "program", "settings": {"command": "python3 manage.py sync_token_status -i 60"}, "tag": "service_03"}, "tokenization-gas-sponsor-tx-sync-service": {"template": "program", "settings": {"command": "python3 manage.py sync_gas_sponsor_tx_status -i 60"}, "tag": "service_03"}, "scheduled-task-service": {"template": "program", "settings": {"command": "python3 manage.py scheduled_task_service -i 5"}, "tag": "service_03"}, "async-signals-processor-all-topics-1": {"template": "program", "settings": {"command": "python3 manage.py async_signals_processor --main-worker-count 3 --retry-worker-count 2"}, "tag": "service_01"}, "async-signals-processor-all-topics-2": {"template": "program", "settings": {"command": "python3 manage.py async_signals_processor --main-worker-count 3 --retry-worker-count 2"}, "tag": "service_02"}, "async-signals-processor-all-topics-3": {"template": "program", "settings": {"command": "python3 manage.py async_signals_processor --main-worker-count 3 --retry-worker-count 2"}, "tag": "service_03"}, "async-signals-monitor": {"template": "program", "settings": {"command": "python3 manage.py async_signals_monitor"}, "tag": "service_03"}, "async-signals-delayed-message-processor": {"template": "program", "settings": {"command": "python3 manage.py delayed_message_processor"}, "tag": "service_03"}}