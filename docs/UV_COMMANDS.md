# UV 包管理器命令

本文档提供了在项目中使用 UV 包管理器的全面指南。

## 1.安装 uv

如果还没有安装 uv，请按照以下步骤安装：

```bash
# 使用 brew
brew install uv

# 或者使用 pip
pip install uv

# 或者使用官方提供的脚本
curl -LsSf https://astral.sh/uv/install.sh | sh
```

## 2. 初始化虚拟环境

### 从现有项目创建和初始化虚拟环境

```bash
# 创建新的虚拟环境（UV 将创建 .venv 目录）
uv venv

# 允许预发布版本（部分 private repo 发布的包需要设置这个环境变量）
export UV_PRERELEASE=allow

# 使用 frozen 参数安装 uv.lock 指定的精确版本的依赖
uv sync --all-groups --all-extras --frozen

# 验证安装
uv pip list
```

### 激活虚拟环境

手动激活 Python 虚拟环境：

```bash
source .venv/bin/activate
```

或者直接使用 `uv run` 运行命令：

```bash
uv run python manage.py runserver
```

## 3. 将 requirements.txt 更新同步到 UV

当 `requirements.txt` 更新了新包或版本变化时，按照以下步骤将更改同步到 UV 配置：

```bash
# 从 requirements.txt 同步所有依赖
uv add -r requirements.txt

# 从 requirements-dev.txt 同步所有依赖
uv add --group dev -r requirements-dev.txt

# 从 requirements-ut.txt 同步所有依赖
uv add --group test -r requirements-ut.txt

# 同步后，重新生成锁文件
uv lock
```

## 4. 手动管理依赖包

```bash
# 添加依赖
uv add cobo_package_example

# 添加依赖到 dev group
uv add --group dev cobo_package_example

# 删除依赖
uv remove cobo_package_example

# 从 dev group 删除依赖
uv remove --group dev cobo_package_example
```

## 5. 部署环境依赖安装

在生产或部署环境中，使用 `--frozen` 参数确保安装与锁文件完全一致的版本：

```bash
# 1. 仅安装生产依赖（不包括开发和测试依赖）
uv sync --frozen --no-dev

# 2. 可选：导出依赖列表用于审计
uv pip freeze > deployed-requirements.txt
```
