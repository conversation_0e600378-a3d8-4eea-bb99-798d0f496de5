import datetime
import json
import logging
import time
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from itertools import groupby
from typing import Dict, List, Optional, Set, Tuple

from cobo_libs.api.utils.helper import get_remote_ip_with_meta
from cobo_libs.bookkeeping.exceptions import NegativeBalance
from cobo_libs.utils.caches import cache_instance, redis_instance_default
from cobo_libs.utils.codec import force_text
from cobo_libs.utils.lock import lock_record
from cobo_libs.utils.logger import timing_logger, tls_logger_tag
from cobo_libs.utils.time import now_ms, ts_to_dt
from cobo_libs.utils.time.time import now_dt
from django.conf import settings
from django.db import transaction
from django.utils.translation import gettext as _

from common.notifier.notifier import Notifier
from common.notifier.pagerduty import PagerDutyConfigs
from custody.api.exception import CustodyException
from custody.cobo.utils import divide_decimal_to_string
from custody.cobo.utils.precondition import check
from custody.coin.data.objects import AssetCoinInfo
from custody.coin.managers import CustodyCoinManager
from custody.custody.dao.transaction import PendingTransactionDao
from custody.custody.dao.withdraw_request import (
    WithdrawRequestDao,
    WithdrawRequestStatusDao,
)
from custody.custody.data.enums import (
    RiskControlActionEnum,
    SystemType,
    WithdrawRequestType,
)
from custody.custody.data.filters import TxFilter
from custody.custody.exceptions import CustodyApiException
from custody.custody.managers.address import CustodyWalletAddressBookManager
from custody.custody.managers.balance import CustodyWalletBalanceManager
from custody.custody.managers.custody_wallet import CustodyWalletManager
from custody.custody.managers.full_transaction import FullTransactionManager
from custody.custody.managers.gas_station import GasStationManager
from custody.custody.managers.notifier import CustodyNotifierManager
from custody.custody.managers.organization import OrganizationManager
from custody.custody.models.custody_user import CustodyUser
from custody.custody.models.custody_wallet import CustodyWallet
from custody.custody.models.transaction import PendingTransaction, WithdrawRequest
from custody.custody.services.risk_control import CustodyRiskControlService
from custody.custody.templates.templates import TemplatesManger
from custody.custody.utils.common import get_readonly_db, reverse_dict
from custody.custody.utils.feature_switch import (
    SwitchNameEnum,
    SwitchTypeEnum,
    bool_feature_switch,
    get_switch,
    hit_switch,
)
from custody.event_collection.data.constants import EventName
from custody.event_collection.data.enums import EventRelatedType
from custody.event_collection.data.objects import Event
from custody.marketdata.controllers import CoinCurrencyRateManager
from custody.mmi.dao import MmiMessageDao, MmiTransactionDao
from custody.mmi.managers import MmiMessageManager, MmiTransactionManager
from custody.wallet.dao.outbound import OutboundTxDao
from custody.wallet.data.enums import GasStationWalletType, OutboundTxStatusEnum
from custody.wallet.managers.outbound import OutboundManager
from custody.wallet.models.inoutbound import OutboundTx
from custody.web3.managers.transaction import (
    Web3ContractTransactionManager,
    Web3WithdrawTransactionManager,
)
from custody.web3.managers.transaction_request import TransactionRequestManager
from waas2.coins.managers_v2 import WaaSTokenManager
from waas2.compliance.compliance.managers.request import ComplianceRequestManager
from waas2.transaction_query.enums import QueryTransactionSourceType

logger = logging.getLogger("app.custody")


class WithdrawRequestManager(object):
    WR_FAIL_CALLBACK_WHITELIST_KEY = "wr_fail_callback_whitelist_key"

    @classmethod
    @transaction.atomic()
    def external_send(
        cls,
        withdraw_request: WithdrawRequest,
        real_fee_coin: str,
        real_fee: int,
        is_async: bool = False,
    ) -> WithdrawRequest:
        # 检查是否开通了全托管Gas Station
        full_gas_station_enabled = (
            GasStationManager.get_full_gas_station_status_by_org_id(
                org_id=withdraw_request.org.id
            )
        )

        try:
            if real_fee:
                if (
                    not full_gas_station_enabled
                    or withdraw_request.custody_wallet.config_type
                    == CustodyWallet.WT_GAS_STATION
                ):
                    if not is_async:
                        CustodyWalletBalanceManager.deduce_tx_fee(
                            from_custody_wallet=withdraw_request.custody_wallet,
                            coin=real_fee_coin,
                            amount=real_fee,
                            withdraw_request=withdraw_request,
                        )
                    else:
                        CustodyWalletBalanceManager.deduce_tx_fee_async(
                            from_custody_wallet=withdraw_request.custody_wallet,
                            coin=real_fee_coin,
                            amount=real_fee,
                            withdraw_request=withdraw_request,
                        )
                else:
                    from custody.custody.managers.requests.internal_fund.manager import (
                        InternalFundManger,
                    )

                    # TODO deprecated 等org迁移到portal后删除

                    req = InternalFundManger.new_withdraw_request(
                        from_custody_wallet=GasStationManager.get_gas_station_wallet_by_org_id(
                            org_id=withdraw_request.org.id
                        ),
                        to_custody_wallet=CustodyWalletManager.get_custody_wallet_for_system_type(
                            system_type=SystemType.FEE,
                            remote_wallet_id=withdraw_request.custody_wallet.remote_wallet_id,
                        ),
                        request_id=GasStationManager.get_child_request_id(
                            main_request_id=withdraw_request.request_id
                        ),
                        request_type=WithdrawRequestType.TYPE_REQUEST_GAS_STATION,
                        asset_coin=real_fee_coin,
                        amount=real_fee,
                        remark=withdraw_request.request_id,
                    )
                    from custody.custody.services.custody import CustodyService

                    CustodyService.run_withdraw_request(req)

        except NegativeBalance:
            raise CustodyApiException(
                CustodyException.ERROR_INSUFFICIENT_BALANCE,
                _("Insufficient %(coin)s balance") % {"coin": real_fee_coin},
            )

        try:
            if withdraw_request.amount:
                if not is_async:
                    CustodyWalletBalanceManager.withdraw_to_blockchain(
                        from_custody_wallet=withdraw_request.custody_wallet,
                        coin=withdraw_request.coin,
                        amount=int(withdraw_request.amount),
                        withdraw_request=withdraw_request,
                    )
                else:
                    CustodyWalletBalanceManager.withdraw_to_blockchain_async(
                        from_custody_wallet=withdraw_request.custody_wallet,
                        coin=withdraw_request.coin,
                        amount=int(withdraw_request.amount),
                        withdraw_request=withdraw_request,
                    )
        except NegativeBalance:
            raise CustodyApiException(
                CustodyException.ERROR_INSUFFICIENT_BALANCE,
                _("Insufficient %(coin)s balance") % {"coin": withdraw_request.coin},
            )

        return withdraw_request

    @classmethod
    def internal_send(
        cls,
        withdraw_request: WithdrawRequest,
        is_async: bool = False,
        use_credit: bool = False,
    ):
        try:
            from_custody_wallet = withdraw_request.custody_wallet
            to_custody_wallet = withdraw_request.to_custody_wallet
            if (
                from_custody_wallet.remote_wallet_id
                != to_custody_wallet.remote_wallet_id
            ):
                # 不同资金池不能走loop
                raise CustodyApiException(
                    CustodyApiException.ERROR_UNEXPECTED_REQUEST,
                    "Source or destination address not on Cobo Loop.",
                )

            record_amount = (
                True
                if from_custody_wallet.org.id != to_custody_wallet.org.id
                else False
            )
            credit_amount = (
                OrganizationManager.calculate_credit_amount(
                    custody_wallet=from_custody_wallet, asset_coin=withdraw_request.coin
                )
                if use_credit
                else 0
            )

            if not is_async:
                CustodyWalletBalanceManager.send_to_internal_transfer(
                    from_custody_wallet=withdraw_request.custody_wallet,
                    coin=withdraw_request.coin,
                    amount=int(withdraw_request.amount),
                    withdraw_request=withdraw_request,
                    record_amount=record_amount,
                    credit_amount=credit_amount,
                )
            else:
                CustodyWalletBalanceManager.send_to_internal_transfer_async(
                    from_custody_wallet=withdraw_request.custody_wallet,
                    coin=withdraw_request.coin,
                    amount=int(withdraw_request.amount),
                    withdraw_request=withdraw_request,
                    record_amount=record_amount,
                    credit_amount=credit_amount,
                )
        except NegativeBalance:
            extra_info = (
                " for GasStation operation"
                if withdraw_request.request_type
                == WithdrawRequestType.TYPE_REQUEST_GAS_STATION
                else ""
            )
            raise CustodyApiException(
                CustodyException.ERROR_INSUFFICIENT_BALANCE,
                _(f"Insufficient %(coin)s balance{extra_info}")
                % {"coin": withdraw_request.coin},
            )

    @classmethod
    def add_wr_fail_callback_whitelist(cls, org_id: int):
        redis_instance_default.sadd(cls.WR_FAIL_CALLBACK_WHITELIST_KEY, org_id)

    @classmethod
    def remove_wr_fail_callback_whitelist(cls, org_id: int):
        redis_instance_default.srem(cls.WR_FAIL_CALLBACK_WHITELIST_KEY, org_id)

    @classmethod
    def get_wr_fail_callback_whitelist(cls) -> Set[str]:
        return redis_instance_default.smembers(cls.WR_FAIL_CALLBACK_WHITELIST_KEY)

    @classmethod
    def exist_wr_fail_callback_whitelist(cls, org_id: int) -> bool:
        return redis_instance_default.sismember(
            cls.WR_FAIL_CALLBACK_WHITELIST_KEY, org_id
        )

    @classmethod
    @transaction.atomic()
    def undo_fail_withdraw_request(
        cls,
        withdraw_request_id: int,
        new_status: int,
        add_funds_back: bool,
        add_fee_back: bool,
        risk_message: str = None,
        fail_reason: str = None,
    ) -> bool:
        with tls_logger_tag(
            dict(
                function="undo_fail_withdraw_request",
                withdraw_request_id=withdraw_request_id,
            )
        ):
            logger.info(
                f"Trying to undo fail withdraw request, "
                f"withdraw_request_id: {withdraw_request_id}, "
                f"new_status: {new_status}, "
                f"add_funds_back: {add_funds_back}, "
                f"add_fee_back: {add_fee_back}"
            )
            withdraw_request = WithdrawRequestDao.get_by_id_or_raise(
                _id=withdraw_request_id
            )
            withdraw_request: WithdrawRequest = lock_record(withdraw_request)
            if withdraw_request.status not in [
                WithdrawRequest.STATUS_DECLINED,
                WithdrawRequest.STATUS_FAILED,
            ]:
                logger.warning(
                    f"can't undo fail withdraw request, "
                    f"withdraw_request_id: {withdraw_request_id}, "
                    f"withdraw_request_status: {withdraw_request.get_status_display()}"
                )
                return False
            if new_status not in [WithdrawRequest.STATUS_PENDING]:
                logger.warning(
                    f"can't undo fail withdraw request, "
                    f"withdraw_request_id: {withdraw_request_id}, "
                    f"withdraw_request_status: {withdraw_request.get_status_display()}, "
                    f"new_status: {new_status}"
                )
                return False

            outbound = withdraw_request.outbound
            if outbound:
                outbound = lock_record(outbound)
                if outbound.status not in [OutboundTx.ST_FAILED]:
                    logger.warning(
                        f"can't undo fail withdraw request, "
                        f"withdraw_request_id: {withdraw_request_id}, "
                        f"withdraw_request_status: {withdraw_request.get_status_display()}, "
                        f"outbound_id: {outbound.id}, "
                        f"outbound_status: {outbound.get_status_display()}, "
                        f"outbound_confirmed_number: {outbound.confirmed_num}"
                    )
                    return False
            if withdraw_request.custody_wallet.config_type in [
                CustodyWallet.WT_API,
                CustodyWallet.WT_GAS_STATION,
                CustodyWallet.WT_NORMAL,
                CustodyWallet.WT_WATCHONLY,
            ]:
                from custody.custody.managers.blockchain import BlockchainClientManager

                bc_wr_info = BlockchainClientManager.get_withdraw_request_info(
                    withdraw_request.custody_wallet.remote_wallet_id,
                    withdraw_request.org.pk,
                    withdraw_request.request_id,
                )
                if bc_wr_info and bc_wr_info["status"] != 2:  # failed
                    logger.warning(
                        f"can't undo fail withdraw request, "
                        f"withdraw_request_id: {withdraw_request_id}, "
                        f"withdraw_request_status: {withdraw_request.get_status_display()}, "
                        f"bc_withdraw_request_status: {bc_wr_info['status']}. "
                    )
                    return False
                elif bc_wr_info:
                    assert str(bc_wr_info["asset_transaction_id"]) == str(
                        withdraw_request.remote_tx_id
                    )
                    assert bc_wr_info["request_id"] == withdraw_request.request_id
            old_status = withdraw_request.status
            update_dict = {"status": new_status}
            if risk_message:
                update_dict["risk_message"] = risk_message
            withdraw_request = WithdrawRequestDao.update_by_id(
                _id=withdraw_request_id, **update_dict
            )

            from waas2.transaction_query.managers.transaction_query import (
                TransactionQueryManager,
            )

            transaction.on_commit(
                lambda: TransactionQueryManager.set_need_update_transaction(
                    source=QueryTransactionSourceType.WithdrawRequest.value,
                    ref_ids=[withdraw_request_id],
                )
            )

            WithdrawRequestStatusDao.create(
                withdraw_request=withdraw_request,
                from_status=old_status,
                to_status=new_status,
            )
            cls._withdraw_to_blockchain(
                withdraw_request=withdraw_request,
                add_funds_back=add_funds_back,
                add_fee_back=add_fee_back,
            )
            return True

    @classmethod
    @transaction.atomic()
    def fail_withdraw_request(
        cls,
        withdraw_request_id: int,
        new_status: int,
        add_funds_back: bool,
        add_fee_back: bool,
        risk_message: str = None,
        fail_reason: str = None,
        action: str = None,
    ) -> bool:
        with tls_logger_tag(
            dict(
                function="fail_withdraw_request",
                withdraw_request_id=withdraw_request_id,
            )
        ):
            logger.info(
                f"Trying to fail withdraw request, "
                f"withdraw_request_id: {withdraw_request_id}, "
                f"new_status: {new_status}, "
                f"add_funds_back: {add_funds_back}, "
                f"add_fee_back: {add_fee_back} risk_message: {risk_message} fail_reason: {fail_reason}"
            )

            withdraw_request = WithdrawRequestDao.get_by_id_or_raise(
                _id=withdraw_request_id
            )
            withdraw_request: WithdrawRequest = lock_record(withdraw_request)
            if withdraw_request.status in (
                WithdrawRequest.STATUS_FINISH,
                WithdrawRequest.STATUS_FAILED,
                WithdrawRequest.STATUS_DECLINED,
            ):
                logger.warning(
                    f"can't fail withdraw request, "
                    f"withdraw_request_id: {withdraw_request_id}, "
                    f"withdraw_request_status: {withdraw_request.get_status_display()}"
                )
                return False
            if new_status not in (
                WithdrawRequest.STATUS_FAILED,
                WithdrawRequest.STATUS_DECLINED,
            ):
                logger.warning(
                    f"can't fail withdraw request, "
                    f"withdraw_request_id: {withdraw_request_id}, "
                    f"withdraw_request_status: {withdraw_request.get_status_display()}, "
                    f"new_status: {new_status}"
                )
                return False

            outbound = withdraw_request.outbound
            if outbound:
                outbound = lock_record(outbound)
                if (
                    outbound.status in (OutboundTx.ST_CONFIRMED, OutboundTx.ST_FAILED)
                ) or (
                    outbound.status == OutboundTx.ST_PENDING
                    and outbound.confirmed_num > 0
                ):
                    logger.warning(
                        f"can't fail withdraw request, "
                        f"withdraw_request_id: {withdraw_request_id}, "
                        f"withdraw_request_status: {withdraw_request.get_status_display()}, "
                        f"outbound_id: {outbound.id}, "
                        f"outbound_status: {outbound.get_status_display()}, "
                        f"outbound_confirmed_number: {outbound.confirmed_num}"
                    )
                    return False

            if withdraw_request.status == WithdrawRequest.STATUS_PENDING:
                if withdraw_request.custody_wallet.config_type in [
                    CustodyWallet.WT_API,
                    CustodyWallet.WT_GAS_STATION,
                    CustodyWallet.WT_NORMAL,
                    CustodyWallet.WT_WATCHONLY,
                ]:
                    from custody.custody.managers.blockchain import (
                        BlockchainClientManager,
                    )

                    bc_wr_info = BlockchainClientManager.get_withdraw_request_info(
                        withdraw_request.custody_wallet.remote_wallet_id,
                        withdraw_request.org.pk,
                        withdraw_request.request_id,
                    )
                    if bc_wr_info and bc_wr_info["status"] != 2:  # failed
                        logger.warning(
                            f"can't fail withdraw request, "
                            f"withdraw_request_id: {withdraw_request_id}, "
                            f"withdraw_request_status: {withdraw_request.get_status_display()}, "
                            f"bc_withdraw_request_status: {bc_wr_info['status']}. "
                        )
                        return False
                    elif bc_wr_info:
                        assert str(bc_wr_info["asset_transaction_id"]) == str(
                            withdraw_request.remote_tx_id
                        )
                        assert bc_wr_info["request_id"] == withdraw_request.request_id

            old_status = withdraw_request.status

            if old_status == WithdrawRequest.STATUS_COMPLIANCE_CHECKING:
                ComplianceRequestManager.stop_request(
                    org_id=withdraw_request.org.id,
                    request_id=withdraw_request.request_id,
                    remark="fail_withdraw_request",
                )

            update_dict = {"status": new_status}
            if risk_message:
                update_dict["risk_message"] = risk_message
            if action:
                update_dict["status_info"] = action
            withdraw_request = WithdrawRequestDao.update_by_id(
                _id=withdraw_request_id, **update_dict
            )

            withdraw_request_status = WithdrawRequestStatusDao.create(
                withdraw_request=withdraw_request,
                from_status=old_status,
                to_status=new_status,
            )
            logger.info(f"Create withdraw_request_status: {withdraw_request_status}")
            if withdraw_request.outbound:
                OutboundTxDao.update_by_id_directly(
                    _id=withdraw_request.outbound.id, status=OutboundTx.ST_FAILED
                )
            pt = PendingTransactionDao.get_by_custody_wallet_and_request_id(
                custody_wallet_id=withdraw_request.custody_wallet.id,
                request_id=withdraw_request.request_id,
            )
            if pt:
                PendingTransactionDao.update_by_id_directly(
                    _id=pt.id, status=PendingTransaction.STATUS_FINISH
                )
            FullTransactionManager.fail_transaction(
                withdraw_request.org.pk, withdraw_request.request_id
            )

            if withdraw_request.request_type == WithdrawRequestType.TYPE_REQUEST_MMI_TX:
                MmiTransactionManager.transaction_rejected(
                    request_id=withdraw_request.request_id
                )
            elif (
                withdraw_request.request_type
                == WithdrawRequestType.TYPE_REQUEST_MMI_MSG
            ):
                MmiMessageManager.message_rejected(
                    request_id=withdraw_request.request_id
                )
            elif (
                withdraw_request.request_type
                == WithdrawRequestType.TYPE_REQUEST_WEB3_WITHDRAW
            ):
                Web3WithdrawTransactionManager.transaction_rejected(
                    request_id=withdraw_request.request_id
                )
            elif (
                withdraw_request.request_type
                == WithdrawRequestType.TYPE_REQUEST_WEB3_CONTRACT
            ):
                Web3ContractTransactionManager.transaction_rejected(
                    request_id=withdraw_request.request_id
                )
            elif withdraw_request.request_type in [
                WithdrawRequestType.TYPE_REQUEST_MPC_WEB,
                WithdrawRequestType.TYPE_REQUEST_MPC_API,
                WithdrawRequestType.TYPE_REQUEST_MPC_WEB3_WEB,
                WithdrawRequestType.TYPE_REQUEST_MPC_PRE_SIGN,
                WithdrawRequestType.TYPE_REQUEST_MPC_SETTLE_CREDIT,
                WithdrawRequestType.TYPE_REQUEST_MPC_PRE_SIGN_APPEND,
                WithdrawRequestType.TYPE_REQUEST_MPC_PRE_SIGN_REDUCE,
                WithdrawRequestType.TYPE_REQUEST_MPC_WEB3_API_TRANSACTION,
                WithdrawRequestType.TYPE_REQUEST_MPC_WEB3_API_SIGN_MESSAGE,
                WithdrawRequestType.TYPE_REQUEST_MPC_BABYLON_STAKE,
                WithdrawRequestType.TYPE_REQUEST_MPC_BABYLON_UNBONDING,
                WithdrawRequestType.TYPE_REQUEST_MPC_BABYLON_SLASHING,
                WithdrawRequestType.TYPE_REQUEST_MPC_BABYLON_WITHDRAW,
                WithdrawRequestType.TYPE_REQUEST_MPC_KEY_SHARE_VERIFICATION,
                WithdrawRequestType.TYPE_REQUEST_MPC_AUTO_SWEEP,
                WithdrawRequestType.TYPE_REQUEST_EOA_WEB3_AUTO_SWEEP,
            ]:
                if new_status == WithdrawRequest.STATUS_DECLINED:
                    TransactionRequestManager.transaction_rejected(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        reason=fail_reason,
                    )
                elif new_status == WithdrawRequest.STATUS_FAILED:
                    TransactionRequestManager.transaction_failed(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        failed_reason=fail_reason,
                    )
            elif (
                withdraw_request.request_type
                in WithdrawRequestType.get_eoa_web3_request_types()
            ):
                if new_status == WithdrawRequest.STATUS_DECLINED:
                    TransactionRequestManager.transaction_rejected(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        reason=fail_reason,
                    )
                    mmi_tx = MmiTransactionDao.get_by_request_id(
                        withdraw_request.request_id
                    )
                    if mmi_tx:
                        MmiTransactionManager.transaction_rejected(
                            withdraw_request.request_id
                        )
                    mmi_msg = MmiMessageDao.get_by_request_id(
                        withdraw_request.request_id
                    )
                    if mmi_msg:
                        MmiMessageManager.message_rejected(withdraw_request.request_id)
                elif new_status == WithdrawRequest.STATUS_FAILED:
                    TransactionRequestManager.transaction_failed(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        failed_reason=fail_reason,
                    )
                    mmi_tx = MmiTransactionDao.get_by_request_id(
                        withdraw_request.request_id
                    )
                    if mmi_tx:
                        MmiTransactionManager.transaction_failed(
                            withdraw_request.request_id
                        )
                    mmi_msg = MmiMessageDao.get_by_request_id(
                        withdraw_request.request_id
                    )
                    if mmi_msg:
                        MmiMessageManager.message_failed(withdraw_request.request_id)
            elif (
                withdraw_request.request_type
                in WithdrawRequestType.get_mpc_rbf_request_type()
            ):
                if new_status == WithdrawRequest.STATUS_DECLINED:
                    TransactionRequestManager.transaction_rejected(
                        org_id=withdraw_request.org.id,
                        request_id=withdraw_request.request_id,
                        reason=risk_message,
                    )
                elif new_status == WithdrawRequest.STATUS_FAILED:
                    TransactionRequestManager.transaction_failed(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        failed_reason=fail_reason,
                    )
            elif (
                withdraw_request.request_type
                == WithdrawRequestType.TYPE_REQUEST_MPC_WEB3_MMI_TX
            ):
                if new_status == WithdrawRequest.STATUS_DECLINED:
                    TransactionRequestManager.transaction_rejected(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        reason=fail_reason,
                    )
                    MmiTransactionManager.transaction_rejected(
                        request_id=withdraw_request.request_id,
                    )
                elif new_status == WithdrawRequest.STATUS_FAILED:
                    TransactionRequestManager.transaction_failed(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        failed_reason=fail_reason,
                    )
                    MmiTransactionManager.transaction_failed(
                        request_id=withdraw_request.request_id,
                    )
            elif (
                withdraw_request.request_type
                == WithdrawRequestType.TYPE_REQUEST_MPC_WEB3_MMI_MSG
            ):
                if new_status == WithdrawRequest.STATUS_DECLINED:
                    TransactionRequestManager.transaction_rejected(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        reason=fail_reason,
                    )
                    MmiMessageManager.message_rejected(
                        request_id=withdraw_request.request_id,
                    )
                elif new_status == WithdrawRequest.STATUS_FAILED:
                    TransactionRequestManager.transaction_failed(
                        org_id=withdraw_request.org.pk,
                        request_id=withdraw_request.request_id,
                        failed_reason=fail_reason,
                    )
                    MmiMessageManager.message_failed(
                        request_id=withdraw_request.request_id,
                    )
            elif (
                withdraw_request.request_type
                in WithdrawRequestType.get_exchange_request_types()
            ):
                from waas2.exchange.managers.tx.tx import (
                    ExchangeWalletTransactionManager,
                )

                ExchangeWalletTransactionManager.fail_by_withdraw_request(
                    withdraw_request_id, new_status, fail_reason
                )
            elif (
                withdraw_request.request_type
                in WithdrawRequestType.get_smart_contract_request_type()
            ):
                from waas2.smart_contract.managers.transaction import (
                    SafeWalletTransactionManager,
                )

                delegate_request_id = (
                    SafeWalletTransactionManager.get_delegate_request_id(
                        withdraw_request.request_id
                    )
                )
                delegate_wr = WithdrawRequestDao.get_by_org_and_request_id(
                    withdraw_request.org.pk, delegate_request_id
                )
                # fail delegate wr
                cls.fail_withdraw_request(
                    withdraw_request_id=delegate_wr.id,
                    new_status=new_status,
                    add_funds_back=add_funds_back,
                    add_fee_back=add_fee_back,
                    risk_message=risk_message,
                    fail_reason=fail_reason,
                    action=action,
                )
            else:
                cls._refund_withdraw_request(
                    withdraw_request=withdraw_request,
                    add_funds_back=add_funds_back,
                    add_fee_back=add_fee_back,
                )

            CustodyRiskControlService.fail_withdraw_request(
                org_id=withdraw_request.org.id,
                request_id=withdraw_request.request_id,
            )
            if withdraw_request.request_type in [
                WithdrawRequestType.TYPE_REQUEST_WEB,
                WithdrawRequestType.TYPE_REQUEST_API,
                WithdrawRequestType.TYPE_REQUEST_APP,
            ] and cls.exist_wr_fail_callback_whitelist(withdraw_request.org.pk):
                # waas 提币失败回调用户
                from custody.custody.services.custody import CustodyService

                CustodyService.send_transaction_message(
                    withdraw_request.custody_wallet, withdraw_request
                )

            from waas2.transaction_query.managers.transaction_query import (
                TransactionQueryManager,
            )

            transaction.on_commit(
                lambda: TransactionQueryManager.set_need_update_transaction(
                    source=QueryTransactionSourceType.WithdrawRequest.value,
                    ref_ids=[withdraw_request.id],
                )
            )
            return True

    @classmethod
    def fail_withdraw_request_by_timeout(cls):
        timeout = 2 * 7 * 24  # two week
        timeout_statuses = [
            WithdrawRequest.STATUS_WAITING_DOUBLE_CHECK,
            WithdrawRequest.STATUS_PENDING_COBO_APPROVAL,
            WithdrawRequest.STATUS_PENDING_COBO_CHECK,
            WithdrawRequest.STATUS_WAITING_TRANSFER,
            WithdrawRequest.STATUS_WAITING_TR_CHECK,
            WithdrawRequest.STATUS_WAITING_GAS_STATION_MAIN_TX,
            WithdrawRequest.STATUS_PENDING_COBO_HUGE_APPROVAL,
            WithdrawRequest.STATUS_WAITING_LEGEND_RECEIVE,
        ]

        withdraw_requests = WithdrawRequestDao.query_by_status_and_time(
            status=timeout_statuses,
            end_time=now_dt() - timedelta(hours=timeout),
        )

        logger.info(
            f"Trying to fail_withdraw_request_by_timeout with timeout {timeout}, timeout_statuses {timeout_statuses}, "
            f"get withdraw_requests len {len(withdraw_requests)}, "
            f"request_ids {[x.request_id for x in withdraw_requests]}. "
        )
        for withdraw_request in withdraw_requests:
            with transaction.atomic():
                withdraw_request = lock_record(withdraw_request)
                if withdraw_request.status not in timeout_statuses:
                    logger.info(
                        f"Ignore fail_withdraw_request_by_timeout request {withdraw_request}, "
                        f"Because status not in timeout_statuses {timeout_statuses}. "
                    )
                    continue

                if now_dt() - timedelta(hours=timeout) < withdraw_request.modified_time:
                    logger.info(
                        f"Ignore fail_withdraw_request_by_timeout request {withdraw_request}, "
                        f"Because no timeout with timeout {timeout}. "
                    )
                    continue

                cls.fail_withdraw_request(
                    withdraw_request.id,
                    WithdrawRequest.STATUS_DECLINED,
                    add_funds_back=True,
                    add_fee_back=True,
                    risk_message="timeout with 14 * 24 hours",
                )

    @classmethod
    @transaction.atomic
    def unlock_superloop_withdraw_request(
        cls, withdraw_request_id: int, unlock_amount: int
    ):
        withdraw_request: WithdrawRequest = WithdrawRequestDao.get_by_id_or_raise(
            withdraw_request_id
        )
        withdraw_request = lock_record(withdraw_request)
        check(
            withdraw_request.request_type
            in [
                WithdrawRequestType.TYPE_REQUEST_SUPERLOOP_CREATE_CREDIT,
                WithdrawRequestType.TYPE_REQUEST_SUPERLOOP_APPEND,
            ]
        )
        check(withdraw_request.status in [WithdrawRequest.STATUS_PENDING])
        check(withdraw_request.custody_wallet.config_type in [CustodyWallet.WT_API])
        check(withdraw_request.amount > unlock_amount > 0)
        outbound = withdraw_request.outbound
        if outbound:
            outbound = lock_record(outbound)
            check(
                outbound.status not in [OutboundTx.ST_CONFIRMED, OutboundTx.ST_FAILED]
            )
            check(
                not (
                    outbound.status == OutboundTx.ST_PENDING
                    and outbound.confirmed_num > 0
                )
            )
        pending_transaction = (
            PendingTransactionDao.get_by_custody_wallet_and_request_id(
                custody_wallet_id=withdraw_request.custody_wallet_id,
                request_id=withdraw_request.request_id,
            )
        )
        check(not pending_transaction)

        from custody.custody.managers.blockchain import BlockchainClientManager

        bc_info = BlockchainClientManager.get_withdraw_request_info(
            remote_wallet_id=withdraw_request.custody_wallet.remote_wallet_id,
            org_id=withdraw_request.org.pk,
            request_id=withdraw_request.request_id,
        )
        check(not bc_info)

        new_amount = withdraw_request.amount - unlock_amount
        WithdrawRequestDao.update_by_id_directly(withdraw_request.id, amount=new_amount)
        if outbound:
            OutboundTxDao.update_by_id_directly(outbound.id, value=new_amount)

        if withdraw_request.through_remote:
            CustodyWalletBalanceManager.refund_from_blockchain(
                to_custody_wallet=withdraw_request.custody_wallet,
                coin=outbound.coin,
                amount=int(unlock_amount),
                withdraw_request=withdraw_request,
            )
        elif withdraw_request.to_addr:
            CustodyWalletBalanceManager.refund_from_internal_transfer(
                to_custody_wallet=withdraw_request.custody_wallet,
                coin=withdraw_request.coin,
                amount=int(unlock_amount),
                withdraw_request=withdraw_request,
            )

    @classmethod
    @transaction.atomic
    def lock_superloop_withdraw_request(
        cls, withdraw_request_id: int, lock_amount: int
    ):
        withdraw_request: WithdrawRequest = WithdrawRequestDao.get_by_id_or_raise(
            withdraw_request_id
        )
        withdraw_request = lock_record(withdraw_request)
        check(
            withdraw_request.request_type
            in [
                WithdrawRequestType.TYPE_REQUEST_SUPERLOOP_CREATE_CREDIT,
                WithdrawRequestType.TYPE_REQUEST_SUPERLOOP_APPEND,
            ]
        )
        check(withdraw_request.status in [WithdrawRequest.STATUS_PENDING])
        check(withdraw_request.custody_wallet.config_type in [CustodyWallet.WT_API])
        check(lock_amount > 0)
        outbound = withdraw_request.outbound
        if outbound:
            outbound = lock_record(outbound)
            check(
                outbound.status not in [OutboundTx.ST_CONFIRMED, OutboundTx.ST_FAILED]
            )
            check(
                not (
                    outbound.status == OutboundTx.ST_PENDING
                    and outbound.confirmed_num > 0
                )
            )
        pending_transaction = (
            PendingTransactionDao.get_by_custody_wallet_and_request_id(
                custody_wallet_id=withdraw_request.custody_wallet_id,
                request_id=withdraw_request.request_id,
            )
        )
        check(not pending_transaction)

        from custody.custody.managers.blockchain import BlockchainClientManager

        bc_info = BlockchainClientManager.get_withdraw_request_info(
            remote_wallet_id=withdraw_request.custody_wallet.remote_wallet_id,
            org_id=withdraw_request.org.pk,
            request_id=withdraw_request.request_id,
        )
        check(not bc_info)

        new_amount = withdraw_request.amount + lock_amount
        WithdrawRequestDao.update_by_id_directly(withdraw_request.id, amount=new_amount)
        if outbound:
            OutboundTxDao.update_by_id_directly(outbound.id, value=new_amount)

        if withdraw_request.through_remote:
            CustodyWalletBalanceManager.withdraw_to_blockchain(
                from_custody_wallet=withdraw_request.custody_wallet,
                coin=outbound.coin,
                amount=int(lock_amount),
                withdraw_request=withdraw_request,
            )
        elif withdraw_request.to_addr:
            CustodyWalletBalanceManager.send_to_internal_transfer(
                from_custody_wallet=withdraw_request.custody_wallet,
                coin=withdraw_request.coin,
                amount=int(lock_amount),
                withdraw_request=withdraw_request,
            )

    @classmethod
    @transaction.atomic()
    def _withdraw_to_blockchain(
        cls,
        withdraw_request: WithdrawRequest,
        add_funds_back: bool,
        add_fee_back: bool,
    ):
        if withdraw_request.through_remote and withdraw_request.outbound:
            # external transfer
            outbound = withdraw_request.outbound

            if add_funds_back and outbound.value > 0:
                CustodyWalletBalanceManager.withdraw_to_blockchain(
                    from_custody_wallet=withdraw_request.custody_wallet,
                    coin=outbound.coin,
                    amount=int(outbound.value),
                    withdraw_request=withdraw_request,
                )

            if add_fee_back and outbound.real_fee > 0:
                CustodyWalletBalanceManager.deduce_tx_fee(
                    from_custody_wallet=withdraw_request.custody_wallet,
                    coin=outbound.real_fee_coin,
                    amount=int(outbound.real_fee),
                    withdraw_request=withdraw_request,
                )

            OutboundManager.update_status(
                outbound.id, status=OutboundTx.ST_NEED_OFFLINE_CHECK
            )
        else:
            # internal transfer
            if withdraw_request.to_addr and add_funds_back:
                WithdrawRequestManager.internal_send(
                    withdraw_request=withdraw_request,
                )

    @classmethod
    @transaction.atomic()
    def _refund_withdraw_request(
        cls,
        withdraw_request: WithdrawRequest,
        add_funds_back: bool,
        add_fee_back: bool,
    ):
        if withdraw_request.through_remote and withdraw_request.outbound:
            # external transfer
            outbound = withdraw_request.outbound

            if add_funds_back and outbound.value > 0:
                CustodyWalletBalanceManager.refund_from_blockchain(
                    to_custody_wallet=withdraw_request.custody_wallet,
                    coin=outbound.coin,
                    amount=int(outbound.value),
                    withdraw_request=withdraw_request,
                )

            if add_fee_back and outbound.real_fee > 0:
                gas_withdraw_request_id = GasStationManager.get_child_request_id(
                    main_request_id=withdraw_request.request_id
                )
                wr = WithdrawRequestDao.get_by_org_and_request_id_or_null(
                    org_id=withdraw_request.org.id, request_id=gas_withdraw_request_id
                )
                if wr:
                    from custody.custody.managers.requests.internal_fund.manager import (
                        InternalFundManger,
                    )

                    req = InternalFundManger.new_withdraw_request(
                        from_custody_wallet=wr.to_custody_wallet,
                        to_custody_wallet=wr.custody_wallet,
                        request_id=wr.request_id,
                        asset_coin=wr.coin,
                        amount=wr.amount,
                        request_type=WithdrawRequestType.TYPE_REQUEST_GAS_STATION,
                        remark="gas station refund",
                    )
                    from custody.custody.services.custody import CustodyService

                    CustodyService.run_withdraw_request(req)

                else:
                    CustodyWalletBalanceManager.refund_tx_fee(
                        to_custody_wallet=withdraw_request.custody_wallet,
                        coin=outbound.real_fee_coin,
                        amount=int(outbound.real_fee),
                        withdraw_request=withdraw_request,
                    )
                    # 退还出入账手续费中的部分入账手续费
                    from custody.partition_fee.managers.inoutbound_fee import (
                        OutboundFeeManager,
                    )

                    OutboundFeeManager.refund_total_inbound_fee(
                        withdraw_request=withdraw_request,
                    )

                OutboundManager.update_status(outbound.id, status=OutboundTx.ST_FAILED)
        else:
            # internal transfer
            if withdraw_request.to_addr and add_funds_back:
                _from = withdraw_request.custody_wallet
                _to = withdraw_request.to_custody_wallet
                record_amount = True
                if _from and _to:
                    record_amount = True if _from.org.id != _to.org.id else False
                CustodyWalletBalanceManager.refund_from_internal_transfer(
                    to_custody_wallet=withdraw_request.custody_wallet,
                    coin=withdraw_request.coin,
                    amount=int(withdraw_request.amount),
                    withdraw_request=withdraw_request,
                    record_amount=record_amount,
                )

    # supporting functions
    @classmethod
    def query_withdrawal_by_filters(
        cls,
        user: CustodyUser,
        tx_filter: TxFilter,
        status: str,
        is_failed_part: bool,
        page_offset: int = 0,
        coin_infos: Dict[str, AssetCoinInfo] = None,
    ) -> Tuple[int, int, list]:
        # TODO(refactor)
        from custody.custody.services.custody import CustodyService

        count = page_offset
        page_count = ((count - 1) // tx_filter.page_length + 1) if count > 0 else 1
        page_content = []
        if not is_failed_part:
            wr_status_map = {
                "pending_confirmation": [
                    WithdrawRequest.STATUS_PENDING,
                    WithdrawRequest.STATUS_PENDING_COBO_APPROVAL,
                    WithdrawRequest.STATUS_PENDING_COBO_HUGE_APPROVAL,
                    WithdrawRequest.STATUS_WAITING_TRANSFER,
                ],
                "pending_approval": [WithdrawRequest.STATUS_WAITING_APPROVAL],
                "pending_doublecheck": [WithdrawRequest.STATUS_WAITING_DOUBLE_CHECK],
            }
            if CustodyService.is_auth_enabled(tx_filter.org):
                wr_status_map["pending_approval"].append(
                    WithdrawRequest.STATUS_PENDING_COBO_CHECK
                )
            else:
                wr_status_map["pending_confirmation"].append(
                    WithdrawRequest.STATUS_PENDING_COBO_CHECK
                )

        else:
            wr_status_map = {
                "failed": [WithdrawRequest.STATUS_DECLINED],
            }

        if (not tx_filter.type or tx_filter.type == "send") and (
            not status or status in wr_status_map
        ):
            wr_status_map_reverse = reverse_dict(wr_status_map)
            if status:
                tx_filter.status = wr_status_map[status]
            else:
                tx_filter.status = wr_status_map_reverse.keys()

            using_db = None
            if hit_switch(
                switch_type=SwitchTypeEnum.SWITCH_TYPE_LIST,
                switch_name=SwitchNameEnum.USE_READONLY_DB,
                param_value=str(user.org_id),
            ):
                using_db = get_readonly_db()

            count, page_count, wrs = WithdrawRequestDao.query_by_tx_filter(
                tx_filter, page_offset, using_db=using_db
            )
            page_content = cls.render_tx_list_by_withdrawals(
                wrs, user, wr_status_map_reverse, coin_infos=coin_infos
            )

        return (
            count,
            page_count,
            page_content,
        )

    @classmethod
    def render_tx_list_by_withdrawals(
        cls,
        withdrawal_requests: List[WithdrawRequest],
        user: CustodyUser,
        status_mapping: Dict[int, str],
        coin_infos: Dict[str, AssetCoinInfo] = None,
    ) -> List[dict]:
        # TODO(refactor)
        from custody.custody.services.custody import CustodyService
        from custody.custody.services.web import CustodyWebService

        page_content = []
        can_approve = CustodyWebService.can_approve(user)
        user_names = WithdrawRequestDao.get_user_names_from_requests(
            withdrawal_requests
        )
        pending_txs: Dict[int, PendingTransaction] = {}
        outbound_ids: List[int] = []
        if coin_infos is None:
            coin_infos = {}
        for wr in withdrawal_requests:
            if wr.coin in coin_infos:
                continue
            coin_info = CustodyCoinManager.get_asset_coin(wr.coin, nullable=True)
            coin_infos[wr.coin] = coin_info
            if coin_info.fee_coin in coin_infos:
                continue
            fee_coin_info = CustodyCoinManager.get_asset_coin(
                coin_info.fee_coin, nullable=True
            )
            coin_infos[coin_info.fee_coin] = fee_coin_info
            if wr.outbound_id:
                outbound_ids.append(wr.outbound_id)
        if outbound_ids:
            pending_txs = {
                x.outbound_id: x
                for x in PendingTransaction.objects.filter(outbound_id__in=outbound_ids)
            }

        coin_address_remarks = {}
        if withdrawal_requests:
            coin_addresses = [[x.coin, x.to_addr] for x in withdrawal_requests]
            coin_address_remarks = (
                CustodyWalletAddressBookManager.get_address_remark_new(
                    custody_wallet_id=withdrawal_requests[0].custody_wallet.id,
                    coin_addresses=coin_addresses,
                )
            )

        for wr in withdrawal_requests:
            coin_info = coin_infos.get(wr.coin)
            if not coin_info:
                continue
            fee_coin_info = coin_infos.get(coin_info.fee_coin)
            if not fee_coin_info:
                continue

            wr_fee = wr.get_outbound_real_fee() or ""
            if wr_fee != "":
                wr_fee = divide_decimal_to_string(wr_fee, fee_coin_info.decimal)

            wr_tx_id = "wr_%s" % wr.request_id
            if wr.outbound:
                wr_pending_tx = pending_txs.get(wr.outbound_id)
                if wr_pending_tx:
                    wr_tx_id = wr_pending_tx.unique_id

            new_approval_process = dict()
            if wr.approval_process:
                new_approval_process = (
                    WithdrawRequestDao.add_username_to_approval_process(
                        wr.approval_process, user_names
                    )
                )

            # 对于加油交易，获取主交易的相关信息
            main_wr_wallet = wr.custody_wallet
            if wr.remark and wr.request_type == 1500:
                gas_child_request_id = ""
                main_wr = WithdrawRequestDao.get_by_org_and_request_id_or_null(
                    org_id=user.org_id, request_id=wr.remark
                )
                if not main_wr:
                    logger.warning(
                        f"gas station main request not found, request_id: {wr.remark}"
                    )
                else:
                    main_wr_wallet = main_wr.custody_wallet
            else:
                gas_child_request_id = GasStationManager.get_child_request_id(
                    wr.request_id
                )
                if not WithdrawRequestDao.get_by_org_and_request_id_or_null(
                    org_id=wr.org.id, request_id=gas_child_request_id
                ):
                    gas_child_request_id = ""

            page_content.append(
                {
                    "tx_id": wr_tx_id,
                    "tx_code": wr.outbound.txid if wr.outbound else "",
                    "tx_explorer_url": ""
                    if wr.to_custody_wallet_id
                    else coin_info.explorer_url.format(txn_id=wr.outbound.txid)
                    if wr.outbound and wr.outbound.txid
                    else "",
                    "tx_request_id": wr.request_id,
                    "tx_coin_code": wr.coin,
                    "tx_from_address": "",
                    "tx_to_address": wr.to_addr,
                    "tx_amount": {
                        "RAW": divide_decimal_to_string(wr.amount, coin_info.decimal)
                    },
                    "tx_type": "send",
                    "tx_status": status_mapping[wr.status],
                    "tx_status_detail": wr.get_transaction_status_detail(),
                    "tx_operations": "approve"
                    if (
                        can_approve
                        and not CustodyService.has_action_on_withdraw_request(
                            user, wr.request_id
                        )
                    )
                    else "",
                    "tx_time": wr.created_time,
                    "tx_fee": wr_fee,
                    "tx_fee_coin_code": coin_info.fee_coin,
                    "tx_cobo_fee": "",
                    "tx_memo": wr.memo,
                    "tx_is_internal": bool(wr.to_custody_wallet_id),
                    "tx_remark": wr.remark,
                    "tx_tags": json.loads(wr.tags) if wr.tags else [],
                    "tx_double_check_response": wr.status_info,
                    "tx_approval_process": new_approval_process,
                    "to_address_remark": coin_address_remarks.get(
                        f"{wr.coin}_{wr.to_addr}", ""
                    ),
                    "tx_request_type": wr.request_type if wr else "",
                    "wallet_name": main_wr_wallet.name if main_wr_wallet else "",
                    "wallet_type": GasStationWalletType.TYPE_FULL_CUSTODY.value
                    if main_wr_wallet.config_type == CustodyWallet.WT_API
                    else GasStationWalletType.TYPE_MPC.value,
                    "gas_child_request_id": gas_child_request_id,
                }
            )
        if not CustodyService.is_auth_enabled(user.org):
            sort_map = {
                "pending_doublecheck": 5,
                "pending_approval": 4,
                "pending_confirmation": 3,
                "failed": 2,
            }
            page_content = sorted(
                page_content,
                key=lambda item: f'{sort_map.get(item["tx_status"], 0)}{wr.created_time}',
                reverse=True,
            )

        return page_content

    @classmethod
    def on_event(cls, sender, event: Event, *args, **kwargs):
        if not getattr(settings, "HIGH_RISK_NOTIFY_ENABLED", False):
            return

        if event.name == EventName.CustodyApi.WITHDRAW:
            cls._on_api_withdraw_event(event)
        elif event.name == EventName.CustodyApi.IP_WHITELIST_MISMATCH:
            cls._on_admins_ip_whitelist_mismatch_event(event)
        elif event.name == EventName.CustodyApi.DUPLICATED_API_SIGNATURE:
            cls._on_admins_duplicated_api_signature_event(event)

    @classmethod
    def check_aggregated_abnormal_withdraw_requests(cls, dry_run=True):
        now = now_ms()
        withdraw_requests = WithdrawRequestDao.query_by_status_and_time(
            status=[
                WithdrawRequest.STATUS_WAITING_DOUBLE_CHECK,
                WithdrawRequest.STATUS_PENDING_COBO_CHECK,
                WithdrawRequest.STATUS_PENDING,
            ],
            exclude_org_ids=settings.ABNORMAL_WITHDRAW_REQUEST_CHECK_EXCLUDE_ORG_IDS,
        )

        abnormal_withdraw_requests = []
        default_timeout = 30 * 60 * 1000  # 30 min, in ms
        default_threshold = 50
        expired_time = now - default_timeout

        expired_withdraw_requests = [
            wr for wr in withdraw_requests if wr.created_time <= expired_time
        ]
        for status, group in groupby(expired_withdraw_requests, lambda i: i.status):
            status_abnormal_withdraw_requests = []

            for wr in group:
                if wr.outbound and wr.outbound.status in (
                    OutboundTxStatusEnum.ST_NEED_OFFLINE_CHECK,
                    OutboundTxStatusEnum.ST_TO_BE_SIGNED,
                    OutboundTxStatusEnum.ST_IN_MEMPOOL,
                    OutboundTxStatusEnum.ST_PENDING,
                    OutboundTxStatusEnum.ST_CONFIRMED,
                ):
                    continue
                if wr.status_info in RiskControlActionEnum.list_wait_approve_type():
                    continue
                else:
                    status_abnormal_withdraw_requests.append(wr)

            abnormal_withdraw_requests.extend(status_abnormal_withdraw_requests)

            if status == WithdrawRequest.STATUS_PENDING:
                cls._re_aggregated_pending_abnormal_withdraw_requests(
                    status_abnormal_withdraw_requests,
                    default_threshold=default_threshold,
                    dry_run=dry_run,
                )
            elif (
                len(status_abnormal_withdraw_requests) >= default_threshold
                and not dry_run
            ):
                cls._notify_abnormal_withdraw_requests(
                    status_abnormal_withdraw_requests, status
                )

        return abnormal_withdraw_requests

    @classmethod
    def _re_aggregated_pending_abnormal_withdraw_requests(
        cls,
        abnormal_withdraw_requests: List[WithdrawRequest],
        default_threshold: int = 50,
        dry_run: bool = True,
    ):
        filter_requests = []
        eoa_requests = []

        for request in abnormal_withdraw_requests:
            if (
                request.request_type <= WithdrawRequestType.TYPE_REQUEST_APP
                or request.request_type
                >= WithdrawRequestType.TYPE_REQUEST_SUPERLOOP_CREATE_CREDIT
            ):
                filter_requests.append(request)
            else:
                eoa_requests.append(request)

        if len(filter_requests) >= default_threshold and not dry_run:
            cls._notify_abnormal_withdraw_requests(
                filter_requests, WithdrawRequest.STATUS_PENDING
            )

        # EOA pending住的交易通知客服
        if len(eoa_requests) >= default_threshold and not dry_run:
            cls._notify_abnormal_withdraw_requests(
                abnormal_withdraw_requests=eoa_requests,
                status=WithdrawRequest.STATUS_PENDING,
                escalation_policy=PagerDutyConfigs.ESCALATION_POLICY.CUSTOMER_SERVICE,
            )

    @classmethod
    def _on_api_withdraw_event(cls, event: Event):
        from custody.custody.managers.org_key import OrgKeyManager

        if (
            not event.related_id
            or event.related_type != EventRelatedType.API_KEY
            or not event.ref_id
            or not event.session
            or not event.session.ip_address
        ):
            logger.info(f"Illegal withdraw event. event: {event}")
            return

        apikey = event.related_id
        ip_address = event.session.ip_address
        withdraw_request_id = event.ref_id

        _, key = OrgKeyManager.get_org_by_api_key(apikey)

        if not key:
            logger.warning(f"Not OrgKey model found from apikey. apikey: {apikey}")
            return

        ip_white_lists = key.get_valid_ips()
        if ip_white_lists != "*" and ip_address in ip_white_lists:
            logger.info(f"Ip {repr(ip_address)} in ip white list.")
            return

        cache_name = "APIKEY_IPS_CACHE"
        cached_ips = set()
        cached_ips_bytes = redis_instance_default.hget(cache_name, apikey)
        if cached_ips_bytes:
            try:
                cached_ips.update(json.loads(force_text(cached_ips_bytes)))
            except Exception as e:
                logger.warning(
                    f"Got error in parse cached_ips_bytes. "
                    f"cached_ips_bytes: {cached_ips_bytes}, "
                    f"error: {e}",
                    exc_info=True,
                )

        if ip_address in cached_ips:
            logger.info(f"ip_address has been seen. ip_address: {ip_address}")
            return

        logger.info(f"On new ip withdraw event detected. event: {event}")
        cls._notify_admins_on_new_ip_detected(
            apikey, withdraw_request_id, ip_address, event.triggered_timestamp
        )

        cached_ips.add(ip_address)
        redis_instance_default.hset(cache_name, apikey, json.dumps(list(cached_ips)))

    @classmethod
    def _notify_admins_on_new_ip_detected(
        cls,
        apikey: str,
        withdraw_request_id: int,
        ip_address: str,
        triggered_timestamp: int,
    ):
        withdraw_request: WithdrawRequest = WithdrawRequestDao.get_by_id_or_raise(
            withdraw_request_id
        )
        wallet_name = withdraw_request.custody_wallet.name
        org_id = withdraw_request.org_id
        admins = CustodyNotifierManager.list_admins(org_id)
        timestamp = ts_to_dt(triggered_timestamp / 1e3).strftime("%Y-%m-%d %H:%M:%S %z")

        data = {
            "wallet_name": wallet_name,
            "time": timestamp,
            "apikey_prefix": apikey[:4],
            "apikey_suffix": apikey[-4:],
            "ip_address": ip_address,
            "request_id": withdraw_request.request_id,
        }
        for language, sub_admins in groupby(admins, lambda i: i.language):
            subject, message, _ = TemplatesManger.build_email_message(
                email_type="ip_not_in_history", email_data=data, language=language
            )
            Notifier.create_general_custody_email(
                [i.email for i in sub_admins if i.email],
                subject=subject,
                message=message,
                template_language=language,
            )

    @classmethod
    def _on_admins_ip_whitelist_mismatch_event(cls, event: Event):
        logger.info(f"On ip whitelist mismatch event detected. event: {event}")

        from custody.custody.managers.org_key import OrgKeyManager

        if (
            not event.related_id
            or event.related_type != EventRelatedType.API_KEY
            or not event.session
            or not event.session.ip_address
        ):
            logger.info(f"Illegal ip_whitelist_mismatch_event event. event: {event}")
            return

        apikey = event.related_id
        ip_address = event.session.ip_address
        request_path = event.session.request_path

        cache_key = f"IP_MISMATCH_WHITELIST_{apikey}_{ip_address}_{request_path}"
        if cache_instance.get(cache_key, None):
            logger.info(
                f"_on_admins_ip_whitelist_mismatch_event ignored, because cache_key {cache_key} exist"
            )
            return
        else:
            cache_instance.set(cache_key, True, timeout=5 * 60)  # 1 minute

        org, key = OrgKeyManager.get_org_by_api_key(apikey)
        wallet_name = key.wallet.name
        admins = CustodyNotifierManager.list_admins(org.id)
        timestamp = ts_to_dt(event.triggered_timestamp / 1e3).strftime(
            "%Y-%m-%d %H:%M:%S %z"
        )

        data = {
            "wallet_name": wallet_name,
            "time": timestamp,
            "request_path": request_path,
            "apikey_prefix": apikey[:4],
            "apikey_suffix": apikey[-4:],
            "ip_address": ip_address,
        }
        for language, sub_admins in groupby(admins, lambda i: i.language):
            subject, message, _ = TemplatesManger.build_email_message(
                email_type="ip_not_in_whitelist", email_data=data, language=language
            )
            Notifier.create_general_custody_email(
                [i.email for i in sub_admins if i.email],
                subject=subject,
                message=message,
                template_language=language,
            )

    @classmethod
    def _on_admins_duplicated_api_signature_event(cls, event: Event):
        logger.info(f"On api signature duplicated event detected. event: {event}")

        from custody.custody.managers.org_key import OrgKeyManager

        if (
            not event.related_id
            or event.related_type != EventRelatedType.API_KEY
            or not event.session
            or not event.payload
        ):
            logger.info(f"Illegal duplicated_api_signature event. event: {event}")
            return

        apikey = event.related_id
        request_path = event.session.request_path
        api_signature = event.payload["api_signature"]

        cache_key = f"DUPLICATED_API_SIGNATURE_{apikey}_{request_path}"
        if cache_instance.get(cache_key, None):
            logger.info(
                f"_on_admins_duplicated_api_signature_event ignored, because cache_key {cache_key} exist"
            )
            return
        else:
            cache_instance.set(cache_key, True, timeout=5 * 60)  # 5 minute

        org, key = OrgKeyManager.get_org_by_api_key(apikey)
        wallet_name = key.wallet.name
        admins = CustodyNotifierManager.list_admins(org.id)
        timestamp = ts_to_dt(event.triggered_timestamp / 1e3).strftime(
            "%Y-%m-%d %H:%M:%S %z"
        )

        data = {
            "wallet_name": wallet_name,
            "time": timestamp,
            "request_path": request_path,
            "apikey_prefix": apikey[:4],
            "apikey_suffix": apikey[-4:],
            "api_signature": api_signature,
        }
        for language, sub_admins in groupby(admins, lambda i: i.language):
            subject, message, _ = TemplatesManger.build_email_message(
                email_type="duplicated_api_signature",
                email_data=data,
                language=language,
            )
            Notifier.create_general_custody_email(
                [i.email for i in sub_admins if i.email],
                subject=subject,
                message=message,
                template_language=language,
            )

    @classmethod
    def _notify_abnormal_withdraw_requests(
        cls,
        abnormal_withdraw_requests: List[WithdrawRequest],
        status: int,
        escalation_policy: str = None,
    ):
        display_status = dict(WithdrawRequest.STATUS_CHOICES).get(status, status)
        number = len(abnormal_withdraw_requests)
        title = f"状态为 {display_status} 的未完成提币请求数量过多 {number} 笔"

        # 构建分组统计字典
        from collections import defaultdict

        group_counter = defaultdict(int)
        for req in abnormal_withdraw_requests:
            group_counter[(req.request_type, req.status)] += 1

        # 构建统计信息字符串
        group_summary = ", ".join(
            [
                f"{req_type}/{status}: {count}笔"
                for (req_type, status), count in group_counter.items()
            ]
        )

        details = (
            f"状态为 {display_status} 的未完成提币请求数量过多 {number} 笔, "
            f"按类型和状态统计如下: {group_summary}"
            # f"提币列表: {[str(x.org_id) + '_' + x.request_id for x in abnormal_withdraw_requests]}"
        )

        Notifier.create_pagerduty(
            title=title,
            details=details,
            incident_key=f"{settings.ENV}_AWR_{status}_{time.strftime('%Y-%m-%d')}",
            service_name=PagerDutyConfigs.SERVICE.CUSTODY_API,
            urgency=PagerDutyConfigs.LIBS.PAGERDUTY_URGENCY_LOW,
            priority=PagerDutyConfigs.LIBS.P2,
            escalation_policy=escalation_policy,
        )

    @classmethod
    def calculate_waas_usd_amount(
        cls, org_id: int, start_date: datetime, end_date: datetime
    ) -> Decimal:
        wrs = WithdrawRequestDao.list_by_date_status_and_type(
            org_id=org_id,
            start_date=start_date,
            end_date=end_date,
            statuses=[WithdrawRequest.STATUS_FINISH],
            types=[
                WithdrawRequestType.TYPE_REQUEST_WEB,
                WithdrawRequestType.TYPE_REQUEST_API,
                WithdrawRequestType.TYPE_REQUEST_APP,
            ],
        )

        month_usd_amount = Decimal(0)
        coin_usd_rates = {}
        for wr in wrs:
            if wr.status == WithdrawRequest.STATUS_FINISH:
                coin_info = CustodyCoinManager.get_asset_coin(wr.coin)

                usd_rate = coin_usd_rates.get(wr.coin)
                if usd_rate is None:
                    usd_rate = Decimal(CoinCurrencyRateManager.get_rate(wr.coin, "USD"))
                    coin_usd_rates[wr.coin] = usd_rate

                month_usd_amount += usd_rate * wr.amount / (10**coin_info.decimal)

        return month_usd_amount

    @classmethod
    @transaction.atomic()
    def update_withdraw_request_status(
        cls,
        withdraw_request: WithdrawRequest,
        to_status: int,
        risk_message: str = None,
    ) -> WithdrawRequest:
        from_status = withdraw_request.status

        update_info = {"status": to_status}
        if risk_message is not None:
            update_info.update({"risk_message": risk_message})

        withdraw_request = WithdrawRequestDao.update_by_id_directly(
            _id=withdraw_request.pk, **update_info
        )

        WithdrawRequestStatusDao.create(
            withdraw_request=withdraw_request,
            from_status=from_status,
            to_status=to_status,
        )

        from waas2.transaction_query.managers.transaction_query import (
            TransactionQueryManager,
        )

        transaction.on_commit(
            lambda: TransactionQueryManager.set_need_update_transaction(
                source=QueryTransactionSourceType.WithdrawRequest.value,
                ref_ids=[withdraw_request.id],
            )
        )

        return withdraw_request

    @classmethod
    def get_latest_request_ip(cls, org_id: int):
        wr = WithdrawRequestDao.get_latest_api_request_by_org_id(org_id=org_id)
        if not wr:
            return ""

        api_request_info = json.loads(wr.api_request_info)

        return get_remote_ip_with_meta(api_request_info.get("meta"))

    @classmethod
    def query_by_withdraw_id(cls, withdraw_request_id: int) -> WithdrawRequest:
        return WithdrawRequestDao.get_by_id_or_null(withdraw_request_id)

    @classmethod
    def query_by_withdraw_ids(
        cls, withdraw_request_ids: List[int]
    ) -> List[WithdrawRequest]:
        return WithdrawRequestDao.list_by_ids(withdraw_request_ids)

    @classmethod
    def query_by_request_id(
        cls, org_id: int, request_id: str
    ) -> Optional[WithdrawRequest]:
        return WithdrawRequestDao.get_by_org_and_request_id(org_id, request_id)

    @classmethod
    @timing_logger("WithdrawRequestManager.is_add_new_to_addr_tag")
    @bool_feature_switch(
        SwitchNameEnum.NEW_TO_ADDR_TAG, func_default_return_value=False
    )
    def is_add_new_to_addr_tag(cls, request: WithdrawRequest) -> bool:
        # 判断是否为asset钱包和web3钱包
        if not request.custody_wallet or (
            request.custody_wallet.config_type != CustodyWallet.WT_API
            and request.custody_wallet.config_type != CustodyWallet.WT_PORTAL_WEB3
        ):
            return False
        # 判断是否有to_address
        if not request.to_addr:
            return False
        # 内部计算rate单位为usd
        token = WaaSTokenManager.get_token(
            token_id=request.coin, ignore_token_rate=False
        )
        amount = divide_decimal_to_string(request.amount, token.decimal)
        usd_amount = float(amount) * token.usd_rate
        amount_limits = get_switch(
            SwitchTypeEnum.SWITCH_TYPE_LIST, SwitchNameEnum.NEW_TO_ADDR_AMOUNT_LIMIT
        )["value"]
        amount_limit = None
        if amount_limits:
            for x in amount_limits:
                x_split = x.split("_")
                if int(x_split[0]) == request.org.id:
                    amount_limit = int(x_split[1])
                    break
        if not amount_limit:
            amount_limit = getattr(settings, "NEW_TO_ADDR_TAG_AMOUNT_LIMIT", 2000000)
        # 金额小于200w u则不加标签
        if usd_amount <= amount_limit:
            return False
        success_withdraw = WithdrawRequestDao.get_org_first_to_addr_by_status(
            org_id=request.org.id,
            to_addr=request.to_addr,
            status=WithdrawRequest.STATUS_FINISH,
        )
        if success_withdraw:
            return False
        return True
