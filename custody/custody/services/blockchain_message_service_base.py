import json
import logging
from typing import Any, List

from cobo_libs.metrics import metrics_counter
from cobo_libs.utils.logger import tls_logger_tag
from django.db.models import QuerySet
from django.utils import timezone

from custody.custody.dao.message import BlockchainFullMessageDao
from custody.custody.data.enums import BlockchainMessageStatus, BlockchainMessageType
from custody.custody.managers.message import BlockchainFullMessageManager
from custody.custody.models.message import BlockchainFullMessage
from custody.custody.services.blockchain import ChainMsgHandler
from custody.custody.utils.switch import is_enabled_bc_msg_base_svc
from waas2.base_service.base_service import BaseService
from waas2.base_service.objects import (
    BaseMetrics,
    IsolationFieldInfo,
    IsolationKeys,
    RetryPolicy,
    TaskResult,
    TaskRouter,
)

logger = logging.getLogger("app.custody.bc_callback_msg_handler_base")


class BlockchainMessageRouter(TaskRouter):
    def supported_fields(self) -> set:
        return {"chain_coin"}

    def __init__(
        self,
        chain_coins: List[str] = None,
        route_configs: str = None,
        default_workers: int = 1,
    ):
        super().__init__(route_configs, default_workers)
        self.chain_coins = chain_coins or []

    def route_task(self, task: Any) -> str | None:
        if self.chain_coins and task.chain_coin not in self.chain_coins:
            return None
        return super().route_task(task)


def generate_service_group_name(
    message_types: List[BlockchainMessageType], retry: bool
):
    if retry:
        return "retry"
    if (
        len(message_types) == 1
        and message_types[0] == BlockchainMessageType.TYPE_CUSTODY
    ):
        return "custody"
    if len(message_types) == 1 and message_types[0] == BlockchainMessageType.TYPE_EOA:
        return "eoa"
    return "retry"


class BlockchainMessageServiceBase(BaseService):
    metrics_interval = 60
    dispatch_batch_size = 100

    def __init__(
        self,
        message_types: List[BlockchainMessageType],
        retry: bool,
        isolation_values: List[int] = None,
        route_configs: str = None,
        concurrency: int = 1,
    ):
        super().__init__(
            isolation_values=isolation_values,
            task_router=BlockchainMessageRouter(
                isolation_values, route_configs, concurrency
            )
            if route_configs
            else None,
            concurrency=concurrency,
            dispatch_loop_interval=0.5,
            worker_loop_interval=3 if retry else 0.5,
            service_group=generate_service_group_name(message_types, retry),
        )
        self._message_types = message_types
        self._retry = retry

    def retry_policy(self, task: any = None) -> RetryPolicy | None:
        return None

    def on_max_retries_reached(self, task_id: int):
        pass

    def supported_isolation_field_info(self) -> IsolationFieldInfo:
        return IsolationFieldInfo(
            isolation_key=IsolationKeys.CHAIN_ID, db_column_name="chain_coin"
        )

    def should_handle_task(self, task_ids: int | List[int]) -> bool | List[bool]:
        return is_enabled_bc_msg_base_svc()

    def fetch_tasks(self) -> QuerySet[BlockchainFullMessage]:
        if not is_enabled_bc_msg_base_svc():
            return BlockchainFullMessageDao.get_empty_queryset()
        return BlockchainFullMessageDao.get_queryset_to_handle(
            msg_types=self._message_types,
            status=BlockchainMessageStatus.ST_RETRYING
            if self._retry
            else BlockchainMessageStatus.ST_PENDING,
        )

    def get_metrics(self) -> List[BaseMetrics]:
        if generate_service_group_name(self._message_types, self._retry) != "retry":
            return []

        # aggregate pending msg info in retry process only
        metrics_list = []
        info_list = BlockchainFullMessageDao.get_aggregated_msg_info(
            timezone.now(), BlockchainMessageStatus.ST_PENDING
        )
        for info in info_list:
            metrics_list.append(
                BaseMetrics(
                    name="bc_callback_msg_base_cnt",
                    value=info.msg_cnt,
                    tags={
                        "msg_type": info.msg_type,
                        "status": BlockchainMessageStatus.ST_PENDING,
                        "chain_coin": info.chain_coin,
                        "enabled": True,
                    },
                )
            )
            metrics_list.append(
                BaseMetrics(
                    name="bc_callback_msg_base_delay",
                    value=info.msg_delay,
                    tags={
                        "msg_type": info.msg_type,
                        "status": BlockchainMessageStatus.ST_PENDING,
                        "chain_coin": info.chain_coin,
                        "enabled": True,
                    },
                )
            )
        return metrics_list

    def handle_tasks(self, task_ids: List[int]) -> TaskResult | List[TaskResult]:
        msgs = BlockchainFullMessageDao.get_msgs_by_ids(task_ids)
        results = []
        for msg in msgs:
            with tls_logger_tag({"bc_msg_id": str(msg.message_id)}):
                try:
                    ChainMsgHandler.handle(
                        json.loads(msg.content), callback=True, from_db=True
                    )
                    metrics_counter(
                        "bc_callback_msg_handle",
                        msg_type=msg.message_type,
                        chain_coin=msg.chain_coin,
                    )
                except Exception as e:
                    logger.warning(
                        f"handle blockchain message error. "
                        f"message: {msg}, "
                        f"error: {e}",
                        exc_info=True,
                    )
                    # try to set msg status to RETRYING
                    BlockchainFullMessageManager.update_message_status(
                        message_id=msg.message_id,
                        target_status=BlockchainMessageStatus.ST_RETRYING,
                    )
                    metrics_counter(
                        "bc_callback_msg_retry",
                        msg_type=msg.message_type,
                        chain_coin=msg.chain_coin,
                    )
            results.append(TaskResult.OK)
        return results

    def get_task_by_ids(self, task_ids: List[int]) -> List[BlockchainFullMessage]:
        return BlockchainFullMessageDao.get_msgs_by_ids(task_ids)
