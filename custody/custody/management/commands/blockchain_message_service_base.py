import logging
import signal
import sys

import gevent
from django.core.management.base import BaseCommand

from custody.custody.data.enums import BlockchainMessageType
from custody.custody.services.blockchain_message_service_base import (
    BlockchainMessageServiceBase,
)

logger = logging.getLogger("app.custody")


class Command(BaseCommand):
    help = "bc message handling service"

    def __init__(self, *args, **kwargs):
        self._service_list = None
        super(Command, self).__init__(*args, **kwargs)

    def add_arguments(self, parser):
        parser.add_argument(
            "-m",
            "--message-type",
            required=True,
            help="[custody|eoa] The type of blockchain messages need to be handled. ",
        )
        parser.add_argument(
            "-r",
            "--retry",
            required=False,
            action="store_true",
            help="Handle retrying messages. ",
        )
        parser.add_argument(
            "-c",
            "--chain_coin",
            required=False,
            action="store",
            help="chain_coin",
        )
        parser.add_argument(
            "-q",
            "--queue",
            required=False,
            action="store",
            help="queue config",
        )

    def _on_exit(self):
        logger.warning("\n\tStopped by CTRL-C, killing all services..")
        if self._service_list:
            gevent.killall(self._service_list)

    def handle(self, *args, **options):
        gevent.signal_handler(signal.SIGTERM, self._on_exit)
        gevent.signal_handler(signal.SIGINT, self._on_exit)

        params = {
            "concurrency": 1,
            "retry": options["retry"],
        }

        message_types = []
        for t in str(options["message_type"]).split(","):
            if t == "custody":
                message_type = BlockchainMessageType.TYPE_CUSTODY
            elif t == "eoa":
                message_type = BlockchainMessageType.TYPE_EOA
            else:
                logger.warning("message type must set to [custody] or [eoa]")
                sys.exit(1)
            message_types.append(message_type)
        params["message_types"] = message_types

        if options["chain_coin"]:
            chain_coins = str(options["chain_coin"]).split(",")
            params["isolation_values"] = chain_coins

        if options["queue"]:
            queue = options["queue"]
            params["route_configs"] = queue

        logger.info(f"Start blockchain message services. params: {params}")
        service = BlockchainMessageServiceBase(**params)
        service.start()
        self._service_list = [service]
        gevent.joinall(self._service_list)
