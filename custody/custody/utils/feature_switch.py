import json
import logging
from enum import Enum, IntEnum, auto, unique
from functools import wraps
from typing import Any

from custody.aladdin.scripts.script_obj import ScriptObj
from custody.cobo.utils.cache import default_redis_instance as redis

logger = logging.getLogger("app.custody.feature_switch")


@unique
class SwitchTypeEnum(IntEnum):
    SWITCH_TYPE_BOOL = 1
    SWITCH_TYPE_LIST = 2


@unique
class SwitchNameEnum(auto, str, Enum):
    TRANSACTION_QUERY = "transaction_query"
    MPC_BROADCAST_FAIL_CALLBACK = "mpc_broadcast_fail_callback"
    EXTRA_FEE = "extra_fee"
    GAS_STATION = "gas_station"
    MPC_BALANCE_MODEL = "mpc_balance_model"
    USE_READONLY_DB = "use_readonly_db"
    ADJUST_BALANCE = "adjust_balance"
    PAY_SUBSCRIPTION = "pay_subscription"
    TEST_EMAIL_LIST = "test_email_list"
    PARTITION_FEE_LIST = "partition_fee_list"  # partition_fee org id纬度开关
    PARTITION_FEE_BOOL = "partition_fee_bool"  # partition_fee 全局开关
    PARTITION_FEE_BLACKLIST = "partition_fee_blacklist"  # partition_fee org id纬度黑名单开关
    MPC_EVM_NO_QUEUE_LIST = "mpc_evm_not_queue"  # mpc evm链不排队白名单
    APIKEY_NO_AUTO_ACTIVATED_LIST = "apikey_no_auto_activated_list"  # apikey不自动激活白名单
    SUPERLOOP_SEND_EMAIL_LIST = "superloop_send_email_list"
    DENY_WITHDRAW_ORG_LIST = "deny_withdraw_org_list"  # 不许提币org列表
    MPC_SIGN_MESSAGE_ORG_LIST = "mpc_sign_message_list"
    MPC_SIGN_RAW_MESSAGE_LIST = "mpc_sign_raw_message_list"
    IN_OUT_BOUND_FEE_LIST = "in_out_bound_fee_list"  # 出入账手续费 org_id + fee_coin 纬度开关
    IN_OUT_BOUND_FEE_BLOCK_LIST = (
        "in_out_bound_fee_block_list"  # 出入账手续费 org_id + fee_coin 纬度block开关
    )
    IN_OUT_BOUND_FEE_STOP_BOOL = (
        "in_out_bound_fee_stop_bool"  # 出入账手续费停止开关, true: 停止, false: 开启(默认)
    )
    IN_OUT_BOUND_BILL_FEE_LIST = (
        "in_out_bound_bill_fee_list"  # 出入账手续费-账单模式 org_id + fee_coin 名单
    )
    MPC_BRC20_ORG_LIST = "mpc_brc20_org_list"
    ENABLE_RISK_QUERY_APPROVAL_ACTION_LIST = "enable_risk_query_approval_action_list"
    REBASE_ALLOW_REWARD = "rebase_allow_reward"
    MPC_COBO_NODE_BACKUP = "mpc_cobo_node_backup"
    MPC_UTXO_NEED_QUEUE = "mpc_utxo_need_queue"
    PAY_RESULT_SYNC_BOOL = "pay_result_sync_bool"  # 轮训dmp支付链接结果开关
    SWITCH_TRANSACTION_STRUCTURE = "switch_transaction_structure"  # 交易结构开关
    WAAS2_TX_VIEW_SCOPE_BOOL = (
        "waas2_tx_view_scope_bool"  # waas2交易查看tx_view_scope_type开关
    )
    BABYLON_ONLY_API_KEY = "babylon_only_api_key"  # 仅能用来babylon staking的api key
    BABYLON_STAKING_ADDRESS_RISK = "babylon_staking_address"
    MPC_SET_DEFAULT_MAX_FEE_AMOUNT = "mpc_set_default_max_fee"
    SEND_TX_TO_RC_ONCE_TIME_SWITCH = "send_tx_to_rc_once_time_switch"
    SWITCH_CLOSE_PUSH_MESSAGE = "switch_close_push_message"
    MPC_PASS_RISK_ORG = "mpc_pass_risk_org"
    MPC_USING_REVERTING_STATUS = "mpc_using_reverting_status"
    MPC_VAULT_BACKED_UP_CHECK = "mpc_vault_backed_up_check"
    MPC_AUDIT_TX_TEMPLATE_CALLDATA = "mpc_audit_tx_template_calldata"
    MPC_NEW_REORG = "mpc_new_reorg"
    NEW_TX_MONGO_WRITE_DEGRADE = "new_tx_mongo_write_degrade_bool"  # 新集群写降级
    MPC_KEY_SHARE_VERIFICATION = "mpc_key_share_verification"
    BRIDGE_ORG_WHITELIST = "bridge_org_whitelist"
    OTC_NOT_TRUSTED_ACCOUNT_LIST = "otc_not_trusted_account_list"
    MPC_AUDIT_MESSAGE_OPT = "mpc_audit_message_opt"
    BRIDGE_SLACK_NOTIFY = "bridge_slack_notify"
    MFA_STATEMENT_UPGRADE_SWITCH = "mfa_statement_upgrade_switch"
    FEE_RESERVED_FOR_ALL = "fee_reserved_for_all"
    FEE_RESERVED_WHITELIST = "fee_reserved_whitelist"
    SAFE_API_SOURCE_LIST = "safe_api_source_list"
    EVENT_WALLET_CHAIN_ENABLED_EVENT = "event_wallet_chain_enabled"
    EVENT_WALLET_TOKEN_ENABLED_EVENT = "event_wallet_token_enabled"
    MPC_NEW_QUEUE_STATUS = "mpc_new_queue_status"
    USE_MFA_GUARD_EXTEND_SWITCH = "use_mfa_guard_extend_switch"
    SWAP_PERMISSION_WHITELIST = "swap_permission_whitelist"
    SWAP_UNI_FEE_RATES = "swap_uni_fee_rates"
    SWAP_UNI_SLIPPAGE = "swap_uni_slippage"
    SWAP_TEST_RETRY_TIMES = "swap_test_retry_times"
    SWAP_UNI_RETRY_PERIOD = "swap_uni_retry_period"
    BABYLON_STAKING_ELIGIBILITY_SWITCH = "babylon_staking_eligibility_switch"
    DISABLE_SWAP_ACTIVITY_LIMIT = "disable_swap_activity_limit"
    DISABLE_SWAP_DMP_FEE = "disable_swap_dmp_fee"
    DISABLE_BRIDGE_DMP_FEE = "disable_bridge_dmp_fee"
    SWAP_ONCALL_LIST = "swap_oncall_list"
    NOT_SWEEP_CHAIN_LIST = "not_sweep_chain_list"
    TRANSFER_BY_WALLET_ID_ORG_WHITELIST = "transfer_by_wallet_id_org_whitelist"
    STOP_AUTO_SWEEP_ORG = "stop_auto_sweep_org"
    MPC_BALANCE_WAIT_TO_USE_MODEL = "mpc_balance_wait_to_use_model"
    MPC_NEW_FEE_MODEL = "mpc_new_fee_model"
    RISKCONTROL_PORTAL_TRANSACTION_SWITCH = "riskcontrol_portal_transaction_switch"
    SIGNER_APPROVAL_SWITCH = "signer_approval_switch"  # 签名人审核消息审批开关
    MFA_MEMBER_NEW_TEMPLATE_SWITCH = "mfa_member_new_template_switch"
    TRIGGER_WAAS2_TX_SIGNAL = "trigger_waas2_tx_signal_switch"  # 触发waas2交易信号开关
    TRANSACTION_QUERY_PRIORITY_ORGS = "transaction_query_priority_orgs"
    TRIGGER_TX_WEBHOOK_BY_SIGNAL = (
        "trigger_tx_webhook_by_signal"  # 通过async signal 触发 webhook
    )
    EVENT_WALLET_CHAIN_DISABLED_EVENT = "event_wallet_chain_disabled"
    EVENT_WALLET_TOKEN_DISABLED_EVENT = "event_wallet_token_disabled"
    EVENT_TOKEN_SUSPENDED = "event_token_suspended"
    EOA_AUTO_CONVERT_ADDRESS = "eoa_auto_convert_address"
    TRACKER_PUSH_STATISTICS_METRICS = "tracker_push_statistics_metrics_switch"
    CUSTODIAL_ASSET_WALLET_ADDRESS_CHAIN_IDENTIFY = (
        "custodial_asset_wallet_address_chain_identify"  # 全托管钱包多链同地址
    )

    NEW_TO_ADDR_TAG = "new_to_addr_tag"  # asset 钱包to_addr新地址标签开关
    NEW_TO_ADDR_AMOUNT_LIMIT = "new_to_addr_amount_limit"  # asset 钱包to_addr新地址金额限制开关
    INTERNAL_HIGH_RISK_API_SWITCH = "internal_high_risk_api_switch"  # 内部高风险API开关
    INTERNAL_LOW_RISK_API_SWITCH = "internal_low_risk_api_switch"  # 内部低风险API开关


def _list_cache_keys():
    return "LIST_FEATURE_SWITCH:KEYS"


def _list_cache_key(switch_name: str):
    return f"LIST_FEATURE_SWITCH:{switch_name}"


def _bool_cache_keys():
    return "BOOL_FEATURE_SWITCH:KEYS"


def _bool_cache_key(switch_name: str):
    return f"BOOL_FEATURE_SWITCH:{switch_name}"


def _settings_cache_key(switch_type: int, switch_name: str):
    return f"SETTINGS_CACHE_KEY:{switch_type}:{switch_name}"


def _register_switch_name(switch_type: int, switch_name: str, func_name: str, **kwargs):
    pipe = redis.pipeline()
    if switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        cache_keys = _bool_cache_keys()
    else:
        cache_keys = _list_cache_keys()

    # 检查开关是否注册
    if not redis.sismember(cache_keys, switch_name):
        pipe.sadd(cache_keys, switch_name)

    # 保存配置信息
    settings_cache_key = _settings_cache_key(
        switch_type=switch_type, switch_name=switch_name
    )
    kwargs["func_name"] = func_name
    if "fn" in kwargs:
        kwargs.pop("fn")
    settings_value = json.dumps(kwargs)
    pipe.sadd(settings_cache_key, settings_value)
    pipe.execute()


def list_feature_switch(
    switch_name: str,
    param_name: str = None,
    param_index: int = None,
    extra_key: str = None,
    check_in: bool = True,
    all_flag: str = "-1",
    func_default_return_value=None,
):
    """
    名单开关
    :param switch_name: 开关名称
    :param param_name: 入参名称，该入参的值将作为是否命中的依据，与param_index需要指向同一个参数，不要使用带默认值参数
    :param param_index: 入参序号，该入参的值将作为是否命中的依据，与param_name需要指向同一个参数，不要使用带默认值参数
    :param extra_key: 如果不以方法的入参作为开关条件，那么需要指定一个额外的输入参数，同时这个输入参数在switch使用后将被丢弃
    :param check_in: 是否在列表中
    :param all_flag: 列表注入特殊值，表示全量
    :param func_default_return_value: 不执行业务逻辑的情况下，默认的返回值
    :return:
    """

    def wrapper(fn):
        # 此处代码只要在进程启动时import了对应的class即会执行
        # 如果作用到全局函数上，则也会在进程启动时执行
        # 注册开关
        if (param_name is None or param_index is None) and extra_key is None:
            raise Exception("list feature switch must be set param or extra")

        _register_switch_name(
            switch_type=SwitchTypeEnum.SWITCH_TYPE_LIST,
            func_name=fn.__name__,
            **locals(),
        )

        @wraps(fn)
        def inner(*args, **kwargs):
            logger.info(
                f"list_feature_switch, switch_name: {switch_name}, param_name: {param_name}, param_index: {param_index}, extra_key: {extra_key}, check_in: {check_in}, all_flag: {all_flag}"
            )

            if param_name is not None and param_index is not None:
                param_value = kwargs.get(param_name)
                if not param_value:
                    param_value = args[param_index]
            else:
                param_value = kwargs.get(extra_key)
                kwargs.pop(extra_key)

            if param_value is None:
                raise Exception("list feature switch must be set param or extra")

            hit = hit_switch(
                switch_type=SwitchTypeEnum.SWITCH_TYPE_LIST,
                switch_name=switch_name,
                param_value=param_value,
                check_in=check_in,
                all_flag=all_flag,
            )

            logger.info(f"list_feature_switch, switch_name: {switch_name},  hit: {hit}")

            if hit:
                return fn(*args, **kwargs)
            else:
                return func_default_return_value

        return inner

    return wrapper


def bool_feature_switch(
    switch_name: str, switch_value: int = 1, func_default_return_value=None
):
    """
    布尔开关
    :param switch_name: 开关名称
    :param switch_value: 开关使用条件，1. 如果用于控制单个功能的开启 or 关闭，则默认 1 即可； 2. 如果用于控制新老功能的切换，则需要在新老功能入口处配置相反的值（0 or 1）
    :param func_default_return_value: 不执行业务逻辑的情况下，默认的返回值
    :return:
    """

    def wrapper(fn):
        # 此处代码只要在进程启动时import了对应的class即会执行
        # 如果作用到全局函数上，则也会在进程启动时执行

        # 注册开关
        _register_switch_name(
            switch_type=SwitchTypeEnum.SWITCH_TYPE_BOOL,
            func_name=fn.__name__,
            **locals(),
        )

        @wraps(fn)
        def inner(*args, **kwargs):
            logger.info(
                f"bool_feature_switch, switch_name: {switch_name}, switch_value: {switch_value}"
            )

            hit = hit_switch(
                switch_type=SwitchTypeEnum.SWITCH_TYPE_BOOL,
                switch_name=switch_name,
                param_value=switch_value,
            )

            logger.info(f"bool_feature_switch, switch_name: {switch_name},  hit: {hit}")

            if hit:
                return fn(*args, **kwargs)
            else:
                return func_default_return_value

        return inner

    return wrapper


# 获取开关信息
def get_switch_list(switch_type: int = None, switch_name: str = None):
    result = []

    if not switch_type or switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        if switch_name:
            return get_switch(SwitchTypeEnum.SWITCH_TYPE_BOOL, switch_name=switch_name)

        bool_cache_keys = _bool_cache_keys()
        members = redis.smembers(bool_cache_keys)

        for item_name in members:
            item_name = item_name.decode("utf8")
            result.append(
                get_switch(SwitchTypeEnum.SWITCH_TYPE_BOOL, switch_name=item_name)
            )

    if not switch_type or switch_type == SwitchTypeEnum.SWITCH_TYPE_LIST:
        if switch_name:
            return get_switch(SwitchTypeEnum.SWITCH_TYPE_LIST, switch_name=switch_name)

        list_cache_keys = _list_cache_keys()
        members = redis.smembers(list_cache_keys)

        for item_name in members:
            item_name = item_name.decode("utf8")

            result.append(
                get_switch(SwitchTypeEnum.SWITCH_TYPE_LIST, switch_name=item_name)
            )

    return result


# 获取指定开关配置信息
def get_switch(switch_type: int, switch_name: str):
    if switch_type not in SwitchTypeEnum.__members__.values():
        return f"invalid switch_type {switch_type}"

    if switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        if not redis.sismember(_bool_cache_keys(), switch_name):
            return f"invalid switch_name {switch_name}"
    else:
        if not redis.sismember(_list_cache_keys(), switch_name):
            return f"invalid switch_name {switch_name}"

    if switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        value = _get_bool_switch_value(switch_name=switch_name)
    else:
        values = redis.smembers(_list_cache_key(switch_name=switch_name))
        value = [x.decode("utf8") for x in values]

    return {
        "switch_name": switch_name,
        "switch_type": switch_type,
        "value": value,
        "settings": [
            x.decode("utf8")
            for x in redis.smembers(
                _settings_cache_key(switch_type=switch_type, switch_name=switch_name)
            )
        ],
    }


def _get_bool_switch_value(switch_name: str):
    cache_key = _bool_cache_key(switch_name=switch_name)
    value = redis.get(cache_key)
    if value:
        value = value.decode("utf8")
    else:
        value = "0"

    return value


# 判断开关是否命中
def hit_switch(
    switch_type: int,
    switch_name: str,
    param_value: Any,
    check_in: bool = True,
    all_flag: str = "-1",
) -> bool:
    if switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        cache_keys = _bool_cache_keys()
    elif switch_type == SwitchTypeEnum.SWITCH_TYPE_LIST:
        cache_keys = _list_cache_keys()
    else:
        raise Exception(f"invalid switch_type {switch_type}")

    if not redis.sismember(cache_keys, switch_name):
        logger.info(f"hit_switch: switch_name {switch_name} never register")
        return False

    if switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        switch_value = _get_bool_switch_value(switch_name=switch_name)

        return str(switch_value) == str(param_value)
    elif switch_type == SwitchTypeEnum.SWITCH_TYPE_LIST:
        # 如果集合中有注入的特殊字符，则默认为全量
        cache_key = _list_cache_key(switch_name=switch_name)
        if redis.sismember(cache_key, str(all_flag)):
            return check_in

        if check_in:
            return redis.sismember(cache_key, str(param_value))
        else:
            return not redis.sismember(cache_key, str(param_value))


# 配置指定开关信息
def set_switch(
    switch_type: int, switch_name: str, switch_value: str, add_or_del: str = "add"
):
    if switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        bool_cache_keys = _bool_cache_keys()
        if not redis.sismember(bool_cache_keys, switch_name):
            _register_switch_name(
                switch_type=switch_type,
                switch_name=switch_name,
                func_name="",
            )

        cache_key = _bool_cache_key(switch_name=switch_name)

        if switch_value not in ["0", "1"]:
            return f"invalid switch_value {switch_value}"

        redis.set(cache_key, switch_value)
    elif switch_type == SwitchTypeEnum.SWITCH_TYPE_LIST:
        list_cache_keys = _list_cache_keys()
        if not redis.sismember(list_cache_keys, switch_name):
            _register_switch_name(
                switch_type=switch_type,
                switch_name=switch_name,
                func_name="",
            )

        cache_key = _list_cache_key(switch_name=switch_name)

        switch_values = switch_value.split(",")
        if add_or_del == "add":
            for switch_value in switch_values:
                redis.sadd(cache_key, switch_value)
        elif add_or_del == "del":
            for switch_value in switch_values:
                redis.srem(cache_key, switch_value)
        else:
            return f"invalid add_or_delete {add_or_del}"
    else:
        return f"invalid switch_type {switch_type}"


# 清空特定开关
def clear_switch_by_name(switch_type: int, switch_name: str):
    if switch_type == SwitchTypeEnum.SWITCH_TYPE_BOOL:
        cache_key = _bool_cache_key(switch_name=switch_name)
        cache_keys = _bool_cache_keys()
    elif switch_type == SwitchTypeEnum.SWITCH_TYPE_LIST:
        cache_key = _list_cache_key(switch_name=switch_name)
        cache_keys = _list_cache_keys()
    else:
        return f"invalid switch_type {switch_type}"

    if not redis.sismember(cache_keys, switch_name):
        return f"invalid switch_name {switch_name}"

    pipe = redis.pipeline()
    pipe.delete(cache_key)
    pipe.delete(_settings_cache_key(switch_type=switch_type, switch_name=switch_name))
    pipe.srem(cache_keys, switch_name)
    pipe.execute()


def hit_high_risk_api_switch(org_id: int, api_name: str) -> bool:
    """
    内部高风险API开关
    :param org_id: 组织ID
    :param api_name: API名称
    :return:
    """
    return hit_switch(
        switch_type=SwitchTypeEnum.SWITCH_TYPE_LIST,
        switch_name=SwitchNameEnum.INTERNAL_HIGH_RISK_API_SWITCH,
        param_value=f"{org_id}:{api_name}",
        check_in=True,
        all_flag="-1",
    )


def hit_low_risk_api_switch(org_id: int, api_name: str) -> bool:
    """
    内部低风险API开关
    :param org_id: 组织ID
    :param api_name: API名称
    :return:
    """
    return hit_switch(
        switch_type=SwitchTypeEnum.SWITCH_TYPE_LIST,
        switch_name=SwitchNameEnum.INTERNAL_LOW_RISK_API_SWITCH,
        param_value=f"{org_id}:{api_name}",
        check_in=True,
        all_flag="-1",
    )


FEATURE_SCRIPT_SETTINGS = [
    ScriptObj(
        breif="拉取所有开关信息",
        description="拉取所有开关信息，包括开关详细的配置",
        func_name="get_switch_list",
        call_func=get_switch_list,
        params=[
            {
                "key": "switch_type",
                "desc": "开关类型(1:布尔开关, 2:名单开关)",
                "required": True,
                "type": "int",
            },
            {
                "key": "switch_name",
                "desc": "开关名称",
                "required": False,
                "type": "str",
            },
        ],
    ),
    ScriptObj(
        breif="设置开关",
        description="设置开关，包括布尔开关和名单开关",
        func_name="set_switch",
        call_func=set_switch,
        params=[
            {
                "key": "switch_type",
                "desc": "开关类型(1:布尔开关, 2:名单开关)",
                "required": True,
                "type": "int",
            },
            {
                "key": "switch_name",
                "desc": "开关名称",
                "required": True,
                "type": "str",
            },
            {
                "key": "switch_value",
                "desc": "设置的值<bool值用0和1代替>，名单开关多个值用逗号分隔",
                "required": True,
                "type": "str",
            },
            {
                "key": "add_or_del",
                "desc": "add or del 指定值，名单开关有效，默认add",
                "required": False,
                "type": "str",
            },
        ],
    ),
    ScriptObj(
        breif="清除指定开关",
        description="清除指定开关，包括开关详细的配置",
        func_name="clear_switch_by_name",
        call_func=clear_switch_by_name,
        params=[
            {
                "key": "switch_type",
                "desc": "开关类型(1:布尔开关, 2:名单开关)",
                "required": True,
                "type": "int",
            },
            {
                "key": "switch_name",
                "desc": "开关名称",
                "required": True,
                "type": "str",
            },
        ],
    ),
]
