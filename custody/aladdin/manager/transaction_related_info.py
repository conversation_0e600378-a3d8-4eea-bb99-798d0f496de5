import logging
from typing import Dict

from custody.custody.dao.custody_wallet import <PERSON>ustody<PERSON><PERSON><PERSON><PERSON><PERSON>
from custody.custody.dao.organization import OrganizationDao
from custody.custody.dao.transaction import EOATransactionDetailDao
from custody.custody.data.enums import EOAEventDirection, TransactionSideEnum
from custody.custody.managers.transaction import TransactionManager

logger = logging.getLogger("aladdin.AladdinTransactionRelatedInfoManager")


class AladdinTransactionRelatedInfoManager(object):
    @classmethod
    def query_deposit_related_info(cls, txid: str) -> Dict:
        resp = {
            "txid": txid,
            "tx_info": [],
        }
        if not txid:
            return resp

        tx_info_list = []

        # 检查全托管
        tx_list = TransactionManager.get_transactions_by_txid_only(txid)
        for tx in tx_list:
            if tx.side != TransactionSideEnum.SIDE_DEPOSIT:
                continue
            info = {
                "address": tx.address,
                "coin": tx.coin,
                "amount": tx.amount,
                "org": {
                    "id": tx.org.id,
                    "uuid": tx.org.uuid,
                    "name": tx.org.name,
                },
                "wallet": {
                    "uuid": tx.custody_wallet.uuid if tx.custody_wallet else "",
                    "type": tx.custody_wallet.config_type if tx.custody_wallet else -1,
                },
            }
            tx_info_list.append(info)

        # 检查 eoa
        tx_list = EOATransactionDetailDao.get_by_tx_hash(tx_hash=txid)
        for tx in tx_list:
            if tx.direction != EOAEventDirection.DEPOSIT:
                continue
            org = OrganizationDao.get_by_id_or_null(tx.org_id)
            custody_wallet = CustodyWalletDao.get_custody_wallet_by_id_or_none(
                tx.custody_wallet_id
            )
            info = {
                "address": tx.to_address,
                "coin": tx.chain_coin,
                "amount": tx.amount,
                "org": {
                    "id": org.id if org else -1,
                    "uuid": org.uuid if org else "",
                    "name": org.name if org else "",
                },
                "wallet": {
                    "uuid": custody_wallet.uuid if custody_wallet else "",
                    "type": custody_wallet.config_type if custody_wallet else -1,
                },
            }
            tx_info_list.append(info)

        resp["tx_info"] = tx_info_list
        return resp
