from django.conf import settings

from custody.custody.dao.custody_wallet import SystemCustodyWalletDao
from custody.custody.dao.organization import OrganizationDao, SystemOrganizationDao
from custody.custody.data.enums import SystemType
from custody.custody.managers.config import CustodyWalletConfigManager
from custody.custody.managers.custody_wallet import CustodyWalletManager
from waas2.base.enums.wallet import WalletSubtype
from waas2.base.enums.wallet import WalletType as PortalWalletType
from waas2.base.managers.wallet_source import WalletSourceManager


def convert_to_system_org(
    org_id: int,
    system_type: SystemType,
    remote_wallet_id: int = settings.BC_DEFAULT_REMOTE_WALLET_ID,
):
    org = OrganizationDao.get_by_id_or_raise(org_id)
    system_type_name = dict(SystemType.to_choices()).get(system_type)
    OrganizationDao.update_org(
        org.id,
        name=f"SYSTEM_ORG_{system_type_name.upper()}",
        default_remote_wallet_id=settings.BC_DEFAULT_REMOTE_WALLET_ID,
        business_type="cobo_org",
        product_type="cobo_org",
        level="none",
        manager="none",
    )
    SystemOrganizationDao.create_system_org(system_type=system_type, org_id=org.id)

    custody_wallet = CustodyWalletManager.create_custody_wallet(
        org_id=org.id,
        name=f"REMOTE_WALLET_{remote_wallet_id}",
        remote_wallet_id=remote_wallet_id,
        add_default_coins=False,
    )
    WalletSourceManager.create_ref(
        org_id=custody_wallet.org.uuid,
        wallet_type=PortalWalletType.Custodial,
        wallet_id=custody_wallet.uuid,
        wallet_subtype=WalletSubtype.Asset,
    )
    CustodyWalletConfigManager.update_config(
        custody_wallet_id=custody_wallet.id, st_all_coins=True
    )

    # Create SystemCustodyWallet
    SystemCustodyWalletDao.create_system_custody_wallet(
        system_type=system_type,
        remote_wallet_id=remote_wallet_id,
        custody_wallet_id=custody_wallet.id,
    )
