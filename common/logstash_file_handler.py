"""
Logstash 文件Handler - 兼容PatchedTCPLogstashHandler的数据格式
"""

import json
import logging.handlers
import os
import socket
from datetime import datetime


class LogstashJSONFormatter(logging.Formatter):
    """
    兼容PatchedTCPLogstashHandler的JSON格式化器
    支持多进程服务名称
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def format(self, record):
        supervisor_program_name = os.getenv("SUPERVISOR_PROGRAM_NAME", "custody")

        # 基础字段
        log_record = {
            "@timestamp": datetime.utcfromtimestamp(record.created).isoformat() + "Z",
            "@version": "1",
            "host": socket.gethostname(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "path": record.pathname,
            "tags": ["django.request"],
            "type": "custody",
            "supervisor_program_name": supervisor_program_name,
        }

        # 添加额外字段
        if hasattr(record, "lineno"):
            log_record["lineno"] = record.lineno

        if hasattr(record, "funcName"):
            log_record["function"] = record.funcName

        if hasattr(record, "thread"):
            log_record["thread"] = record.thread

        if hasattr(record, "threadName"):
            log_record["threadName"] = record.threadName

        if hasattr(record, "processName"):
            log_record["processName"] = record.processName

        # 处理异常信息
        if record.exc_info:
            log_record["exception"] = self.formatException(record.exc_info)

        # 处理stack_info
        if hasattr(record, "stack_info") and record.stack_info:
            log_record["stack_info"] = record.stack_info

        return json.dumps(log_record, ensure_ascii=False, default=str)


class LogstashFileHandler(logging.handlers.RotatingFileHandler):
    def __init__(
        self,
        filename,
        mode="a",
        max_bytes=0,
        backup_count=5,
        encoding=None,
        delay=False,
    ):
        log_dir = os.path.dirname(filename)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

        super().__init__(
            filename=filename,
            mode=mode,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding=encoding,
            delay=delay,
        )

    def emit(self, record):
        try:
            super().emit(record)
        except Exception:
            self.handleError(record)
