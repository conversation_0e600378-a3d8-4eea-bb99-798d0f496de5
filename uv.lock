version = 1
revision = 2
requires-python = "==3.10.*"

[[package]]
name = "aiodns"
version = "3.5.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pycares" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiodns/3.5.0/aiodns-3.5.0.tar.gz", hash = "sha256:11264edbab51896ecf546c18eb0dd56dff0428c6aa6d2cd87e643e07300eb310" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiodns/3.5.0/aiodns-3.5.0-py3-none-any.whl", hash = "sha256:6d0404f7d5215849233f6ee44854f2bb2481adf71b336b2279016ea5990ca5c5" },
]

[[package]]
name = "aiohappyeyeballs"
version = "2.6.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohappyeyeballs/2.6.1/aiohappyeyeballs-2.6.1.tar.gz", hash = "sha256:c3f9d0113123803ccadfdf3f0faa505bc78e6a72d1cc4806cbd719826e943558" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohappyeyeballs/2.6.1/aiohappyeyeballs-2.6.1-py3-none-any.whl", hash = "sha256:f349ba8f4b75cb25c99c5c2d84e997e485204d2902a9597802b0371f09331fb8" },
]

[[package]]
name = "aiohttp"
version = "3.10.5"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aiohappyeyeballs" },
    { name = "aiosignal" },
    { name = "async-timeout" },
    { name = "attrs" },
    { name = "frozenlist" },
    { name = "multidict" },
    { name = "yarl" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5.tar.gz", hash = "sha256:f071854b47d39591ce9a17981c46790acb30518e2f83dfca8db2dfa091178691" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:18a01eba2574fb9edd5f6e5fb25f66e6ce061da5dab5db75e13fe1558142e0a3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:94fac7c6e77ccb1ca91e9eb4cb0ac0270b9fb9b289738654120ba8cebb1189c6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:2f1f1c75c395991ce9c94d3e4aa96e5c59c8356a15b1c9231e783865e2772699" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4f7acae3cf1a2a2361ec4c8e787eaaa86a94171d2417aae53c0cca6ca3118ff6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:94c4381ffba9cc508b37d2e536b418d5ea9cfdc2848b9a7fea6aebad4ec6aac1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:c31ad0c0c507894e3eaa843415841995bf8de4d6b2d24c6e33099f4bc9fc0d4f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0912b8a8fadeb32ff67a3ed44249448c20148397c1ed905d5dac185b4ca547bb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:0d93400c18596b7dc4794d48a63fb361b01a0d8eb39f28800dc900c8fbdaca91" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:d00f3c5e0d764a5c9aa5a62d99728c56d455310bcc288a79cab10157b3af426f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:d742c36ed44f2798c8d3f4bc511f479b9ceef2b93f348671184139e7d708042c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:814375093edae5f1cb31e3407997cf3eacefb9010f96df10d64829362ae2df69" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:8224f98be68a84b19f48e0bdc14224b5a71339aff3a27df69989fa47d01296f3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:d9a487ef090aea982d748b1b0d74fe7c3950b109df967630a20584f9a99c0683" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-win32.whl", hash = "sha256:d9ef084e3dc690ad50137cc05831c52b6ca428096e6deb3c43e95827f531d5ef" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp/3.10.5/aiohttp-3.10.5-cp310-cp310-win_amd64.whl", hash = "sha256:66bf9234e08fe561dccd62083bf67400bdbf1c67ba9efdc3dac03650e97c6088" },
]

[[package]]
name = "aiohttp-sse-client"
version = "0.2.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aiohttp" },
    { name = "attrs" },
    { name = "multidict" },
    { name = "yarl" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp-sse-client/0.2.1/aiohttp-sse-client-0.2.1.tar.gz", hash = "sha256:5004e29271624af586158dc7166cb0687a7a5997aab5b808f4b53400e1b72e3b" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiohttp-sse-client/0.2.1/aiohttp_sse_client-0.2.1-py2.py3-none-any.whl", hash = "sha256:42c81ee9213e9fc8bc412b063bac3a813e02e75250c4c8049222234d41c9b024" },
]

[[package]]
name = "aioredis"
version = "1.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "async-timeout" },
    { name = "hiredis" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/aioredis/1.3.1/aioredis-1.3.1.tar.gz", hash = "sha256:15f8af30b044c771aee6787e5ec24694c048184c7b9e54c3b60c750a4b93273a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aioredis/1.3.1/aioredis-1.3.1-py3-none-any.whl", hash = "sha256:b61808d7e97b7cd5a92ed574937a079c9387fdadd22bfbfa7ad2fd319ecc26e3" },
]

[[package]]
name = "aiosignal"
version = "1.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "frozenlist" },
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiosignal/1.4.0/aiosignal-1.4.0.tar.gz", hash = "sha256:f47eecd9468083c2029cc99945502cb7708b082c232f9aca65da147157b251c7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aiosignal/1.4.0/aiosignal-1.4.0-py3-none-any.whl", hash = "sha256:053243f8b92b990551949e63930a839ff0cf0b0ebbe0597b0f3fb19e1a0fe82e" },
]

[[package]]
name = "allure-pytest"
version = "2.13.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "allure-python-commons" },
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/allure-pytest/2.13.2/allure-pytest-2.13.2.tar.gz", hash = "sha256:22243159e8ec81ce2b5254b4013802198821b1b42f118f69d4a289396607c7b3" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/allure-pytest/2.13.2/allure_pytest-2.13.2-py3-none-any.whl", hash = "sha256:17de9dbee7f61c8e66a5b5e818b00e419dbcea44cb55c24319401ba813220690" },
]

[[package]]
name = "allure-python-commons"
version = "2.13.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "attrs" },
    { name = "pluggy" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/allure-python-commons/2.13.2/allure-python-commons-2.13.2.tar.gz", hash = "sha256:8a03681330231b1deadd86b97ff68841c6591320114ae638570f1ed60d7a2033" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/allure-python-commons/2.13.2/allure_python_commons-2.13.2-py3-none-any.whl", hash = "sha256:2bb3646ec3fbf5b36d178a5e735002bc130ae9f9ba80f080af97d368ba375051" },
]

[[package]]
name = "amqp"
version = "5.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "vine" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/amqp/5.3.1/amqp-5.3.1.tar.gz", hash = "sha256:cddc00c725449522023bad949f70fff7b48f0b1ade74d170a6f10ab044739432" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/amqp/5.3.1/amqp-5.3.1-py3-none-any.whl", hash = "sha256:43b3319e1b4e7d1251833a93d672b4af1e40f3d632d479b98661a95f117880a2" },
]

[[package]]
name = "aniso8601"
version = "9.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/aniso8601/9.0.1/aniso8601-9.0.1.tar.gz", hash = "sha256:72e3117667eedf66951bb2d93f4296a56b94b078a8a95905a052611fb3f1b973" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/aniso8601/9.0.1/aniso8601-9.0.1-py2.py3-none-any.whl", hash = "sha256:1d2b7ef82963909e93c4f24ce48d4de9e66009a21bf1c1e1c85bdd0812fe412f" },
]

[[package]]
name = "annotated-types"
version = "0.7.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/annotated-types/0.7.0/annotated_types-0.7.0.tar.gz", hash = "sha256:aff07c09a53a08bc8cfccb9c85b05f1aa9a2a6f23728d790723543408344ce89" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/annotated-types/0.7.0/annotated_types-0.7.0-py3-none-any.whl", hash = "sha256:1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53" },
]

[[package]]
name = "anyio"
version = "4.9.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "exceptiongroup" },
    { name = "idna" },
    { name = "sniffio" },
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/anyio/4.9.0/anyio-4.9.0.tar.gz", hash = "sha256:673c0c244e15788651a4ff38710fea9675823028a6f08a5eda409e0c9840a028" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/anyio/4.9.0/anyio-4.9.0-py3-none-any.whl", hash = "sha256:9f76d541cad6e36af7beb62e978876f3b41e3e04f2c1fbf0884604c0a9c4d93c" },
]

[[package]]
name = "appdirs"
version = "1.4.4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/appdirs/1.4.4/appdirs-1.4.4.tar.gz", hash = "sha256:7d5d0167b2b1ba821647616af46a749d1c653740dd0d2415100fe26e27afdf41" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/appdirs/1.4.4/appdirs-1.4.4-py2.py3-none-any.whl", hash = "sha256:a841dacd6b99318a741b166adb07e19ee71a274450e68237b4650ca1055ab128" },
]

[[package]]
name = "arabic-reshaper"
version = "3.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/arabic-reshaper/3.0.0/arabic_reshaper-3.0.0.tar.gz", hash = "sha256:ffcd13ba5ec007db71c072f5b23f420da92ac7f268512065d49e790e62237099" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/arabic-reshaper/3.0.0/arabic_reshaper-3.0.0-py3-none-any.whl", hash = "sha256:3f71d5034bb694204a239a6f1ebcf323ac3c5b059de02259235e2016a1a5e2dc" },
]

[[package]]
name = "asgiref"
version = "3.6.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/asgiref/3.6.0/asgiref-3.6.0.tar.gz", hash = "sha256:9567dfe7bd8d3c8c892227827c41cce860b368104c3431da67a0c5a65a949506" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/asgiref/3.6.0/asgiref-3.6.0-py3-none-any.whl", hash = "sha256:71e68008da809b957b7ee4b43dbccff33d1b23519fb8344e33f049897077afac" },
]

[[package]]
name = "asn1crypto"
version = "1.5.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/asn1crypto/1.5.1/asn1crypto-1.5.1.tar.gz", hash = "sha256:13ae38502be632115abf8a24cbe5f4da52e3b5231990aff31123c805306ccb9c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/asn1crypto/1.5.1/asn1crypto-1.5.1-py2.py3-none-any.whl", hash = "sha256:db4e40728b728508912cbb3d44f19ce188f218e9eba635821bb4b68564f8fd67" },
]

[[package]]
name = "asttokens"
version = "3.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/asttokens/3.0.0/asttokens-3.0.0.tar.gz", hash = "sha256:0dcd8baa8d62b0c1d118b399b2ddba3c4aff271d0d7a9e0d4c1681c79035bbc7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/asttokens/3.0.0/asttokens-3.0.0-py3-none-any.whl", hash = "sha256:e3078351a059199dd5138cb1c706e6430c05eff2ff136af5eb4790f9d28932e2" },
]

[[package]]
name = "async-timeout"
version = "4.0.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/async-timeout/4.0.3/async-timeout-4.0.3.tar.gz", hash = "sha256:4640d96be84d82d02ed59ea2b7105a0f7b33abe8703703cd0ab0bf87c427522f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/async-timeout/4.0.3/async_timeout-4.0.3-py3-none-any.whl", hash = "sha256:7405140ff1230c310e51dc27b3145b9092d659ce68ff733fb0cefe3ee42be028" },
]

[[package]]
name = "attrs"
version = "25.3.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/attrs/25.3.0/attrs-25.3.0.tar.gz", hash = "sha256:75d7cefc7fb576747b2c81b4442d4d4a1ce0900973527c011d1030fd3bf4af1b" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/attrs/25.3.0/attrs-25.3.0-py3-none-any.whl", hash = "sha256:427318ce031701fea540783410126f03899a97ffc6f61596ad581ac2e40e3bc3" },
]

[[package]]
name = "authlib"
version = "1.2.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cryptography" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/authlib/1.2.1/Authlib-1.2.1.tar.gz", hash = "sha256:421f7c6b468d907ca2d9afede256f068f87e34d23dd221c07d13d4c234726afb" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/authlib/1.2.1/Authlib-1.2.1-py2.py3-none-any.whl", hash = "sha256:c88984ea00149a90e3537c964327da930779afa4564e354edfd98410bea01911" },
]

[[package]]
name = "autobahn"
version = "24.4.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cryptography" },
    { name = "hyperlink" },
    { name = "setuptools" },
    { name = "txaio" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/autobahn/24.4.2/autobahn-24.4.2.tar.gz", hash = "sha256:a2d71ef1b0cf780b6d11f8b205fd2c7749765e65795f2ea7d823796642ee92c9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/autobahn/24.4.2/autobahn-24.4.2-py2.py3-none-any.whl", hash = "sha256:c56a2abe7ac78abbfb778c02892d673a4de58fd004d088cd7ab297db25918e81" },
]

[[package]]
name = "automat"
version = "25.4.16"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/automat/25.4.16/automat-25.4.16.tar.gz", hash = "sha256:0017591a5477066e90d26b0e696ddc143baafd87b588cfac8100bc6be9634de0" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/automat/25.4.16/automat-25.4.16-py3-none-any.whl", hash = "sha256:04e9bce696a8d5671ee698005af6e5a9fa15354140a87f4870744604dcdd3ba1" },
]

[[package]]
name = "backoff"
version = "2.1.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/backoff/2.1.2/backoff-2.1.2.tar.gz", hash = "sha256:407f1bc0f22723648a8880821b935ce5df8475cf04f7b6b5017ae264d30f6069" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/backoff/2.1.2/backoff-2.1.2-py3-none-any.whl", hash = "sha256:b135e6d7c7513ba2bfd6895bc32bc8c66c6f3b0279b4c6cd866053cfd7d3126b" },
]

[[package]]
name = "bandit"
version = "1.6.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "gitpython" },
    { name = "pyyaml" },
    { name = "six" },
    { name = "stevedore" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/bandit/1.6.2/bandit-1.6.2.tar.gz", hash = "sha256:41e75315853507aa145d62a78a2a6c5e3240fe14ee7c601459d0df9418196065" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bandit/1.6.2/bandit-1.6.2-py2.py3-none-any.whl", hash = "sha256:336620e220cf2d3115877685e264477ff9d9abaeb0afe3dc7264f55fa17a3952" },
]

[[package]]
name = "base58"
version = "2.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/base58/2.1.1/base58-2.1.1.tar.gz", hash = "sha256:c5d0cb3f5b6e81e8e35da5754388ddcc6d0d14b6c6a132cb93d69ed580a7278c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/base58/2.1.1/base58-2.1.1-py3-none-any.whl", hash = "sha256:11a36f4d3ce51dfc1043f3218591ac4eb1ceb172919cebe05b52a5bcc8d245c2" },
]

[[package]]
name = "bc-waas2"
version = "0.0.48"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cobo-libs" },
    { name = "pydantic" },
    { name = "python-dateutil" },
    { name = "setuptools" },
    { name = "typing-extensions" },
    { name = "urllib3" },
]
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bc-waas2/0.0.48/bc_waas2-0.0.48-py3-none-any.whl", hash = "sha256:757cb00c7ada9a699e8b8589e64b5eca710de9b180c9450d0714aac89f1cd418" },
]

[[package]]
name = "bech32"
version = "1.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/bech32/1.2.0/bech32-1.2.0.tar.gz", hash = "sha256:7d6db8214603bd7871fcfa6c0826ef68b85b0abd90fa21c285a9c5e21d2bd899" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bech32/1.2.0/bech32-1.2.0-py3-none-any.whl", hash = "sha256:990dc8e5a5e4feabbdf55207b5315fdd9b73db40be294a19b3752cde9e79d981" },
]

[[package]]
name = "bech32m"
version = "1.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/bech32m/1.0.0/bech32m-1.0.0.tar.gz", hash = "sha256:6198dbf267d7d5e1a7a24e0825d9143fa4aaa886ec44a11df95fa3d5e38cb3c2" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bech32m/1.0.0/bech32m-1.0.0-py3-none-any.whl", hash = "sha256:c44a2028b22e1d305a1803b3a811dce3e8ac1d8ec487b0c5e39447477dda70d4" },
]

[[package]]
name = "betterproto"
version = "2.0.0b4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "grpclib" },
    { name = "python-dateutil" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/betterproto/2.0.0b4/betterproto-2.0.0b4.tar.gz", hash = "sha256:99bc6f866fe9c30100fe438662439205f35bc0e65e4e736c46a6ebfea02c3e7b" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/betterproto/2.0.0b4/betterproto-2.0.0b4-py3-none-any.whl", hash = "sha256:6b807038df17a7896cc1f98b42f64eed24c2f350b6d10b2854501f8b9b7d3d1e" },
]

[[package]]
name = "billiard"
version = "3.6.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/billiard/3.6.4.0/billiard-3.6.4.0.tar.gz", hash = "sha256:299de5a8da28a783d51b197d496bef4f1595dd023a93a4f59dde1886ae905547" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/billiard/3.6.4.0/billiard-3.6.4.0-py3-none-any.whl", hash = "sha256:87103ea78fa6ab4d5c751c4909bcff74617d985de7fa8b672cf8618afd5a875b" },
]

[[package]]
name = "bitarray"
version = "3.5.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1.tar.gz", hash = "sha256:b03c49d1a2eb753cc6090053f1c675ada71e1c3ea02011f1996cf4c2b6e9d6d6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:d0af817f8440fd382414773fe75da0d6cac69d3e784bec106bae540b9de9b020" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:b1a383e25820643dcfaa5647e430ac990007f0163c525f98fc90e88d0a332662" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:202d769109fc81066eed80e936c1c1e2864547e81830c8578a4b90e530118c15" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:a9e5be9949ee1cf60382d976f25980d61421387655c28be1571b607bd33578bb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:9450d6579da0d8d087694623307cfbd6cfd13a49c221ead5ff1975309956ae28" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d11c79c1edd86ac4fc68bd14947d8f57746afbc326288930e8202d93906443af" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:9e789bfa751deec734fc7f9fabc82f03fc680fa26196a417b69a101a3d672dfa" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:6c7af1e0b02d54875060b15db85f9634bcd2f45d306508dbf8ceeb157f64fdac" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:1d82bdf87186adfb23336c7f2f6c4c54c1e3f26c1dc6ec0b5fbbb6cba3a46266" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:65cbee9dfc85ad88ceea21c669b48f5b90dd40181c12e5fc097cf9863d04bfb1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:6167f48ff31f8153090c2f75f60f6032adb7d2d3c0dfc61eb96f6e654f66c860" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:b230d1bd0b0fd4264165220a267479f97e18bb856734375e678ff77d3d63e25f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-win32.whl", hash = "sha256:224739107aa0e7590a5166c52866a2c69412546469f7a71bfb5b89d38cb7d0d5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-cp310-cp310-win_amd64.whl", hash = "sha256:f404a0f6f37ade9aba1ca18d0f0ca76c729bb36a7ddd42f62fb899f6b99e3b4b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:c4e862295ad3e3eb728138315dd3e2e2255a5f1a75d234a0990d073dc0905936" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:983be82cfde6c59204f7862ee71c87b999163f366a4ac649121c257f0b73442c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ae197e132d347096ce58c8dee3fa462ace2cc3261286fc63e5a36f843ef2074c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2d76b6a3c7a8e44607c9bdc3291a95086652068e60e1c9311993b3decad14e38" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:7dd8a6627b55d9ac9224890c678b1d57d4fefa87118717428fd8c4bd2761c20b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitarray/3.5.1/bitarray-3.5.1-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:33233ee4c4245b7da2c395468a44473c834eeea7cd53fe97dc419451bf8e6e9a" },
]

[[package]]
name = "bitcoinlib"
version = "********"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "ecdsa", marker = "sys_platform == 'win32'" },
    { name = "fastecdsa", marker = "sys_platform != 'win32'" },
    { name = "numpy" },
    { name = "pycryptodome" },
    { name = "requests" },
    { name = "sqlalchemy" },
]
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/bitcoinlib/********/bitcoinlib-********-py3-none-any.whl", hash = "sha256:ed4ba895021004f448103378202dba55d3c24b3fc04848e4135aa712bc1ea971" },
]

[[package]]
name = "black"
version = "22.3.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "click" },
    { name = "mypy-extensions" },
    { name = "pathspec" },
    { name = "platformdirs" },
    { name = "tomli" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/black/22.3.0/black-22.3.0.tar.gz", hash = "sha256:35020b8886c022ced9282b51b5a875b6d1ab0c387b31a065b84db7c33085ca79" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/black/22.3.0/black-22.3.0-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:2497f9c2386572e28921fa8bec7be3e51de6801f7459dffd6e62492531c47e09" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/black/22.3.0/black-22.3.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:5795a0375eb87bfe902e80e0c8cfaedf8af4d49694d69161e5bd3206c18618bb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/black/22.3.0/black-22.3.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:e3556168e2e5c49629f7b0f377070240bd5511e45e25a4497bb0073d9dda776a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/black/22.3.0/black-22.3.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:67c8301ec94e3bcc8906740fe071391bce40a862b7be0b86fb5382beefecd968" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/black/22.3.0/black-22.3.0-cp310-cp310-win_amd64.whl", hash = "sha256:fd57160949179ec517d32ac2ac898b5f20d68ed1a9c977346efbac9c2f1e779d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/black/22.3.0/black-22.3.0-py3-none-any.whl", hash = "sha256:bc58025940a896d7e5356952228b68f793cf5fcb342be703c3a2669a1488cb72" },
]

[[package]]
name = "blake256"
version = "0.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/blake256/0.1.1/blake256-0.1.1.tar.gz", hash = "sha256:802945f2f07454d726fd0b0ee5c54dd133d686cb6f05b3d85af2d55ac17075f5" }

[[package]]
name = "blspy"
version = "2.0.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "wheel" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/blspy/2.0.2/blspy-2.0.2.tar.gz", hash = "sha256:9b12d685f3c104d3fe0faf3618f6b824272d131b8ea3843190ad670618736775" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/blspy/2.0.2/blspy-2.0.2-cp310-cp310-macosx_10_14_x86_64.whl", hash = "sha256:c053733d674ff92fcb4059b36b75c29337d34afbd900a0bc2113ad86fc447b77" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/blspy/2.0.2/blspy-2.0.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:dbb053ccb1b48584cc4d357f29c770a2cf3e05faa8b184bd9294e8ed01a565b5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/blspy/2.0.2/blspy-2.0.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1a0b208b80fa76c9adf296a77aa805127840cf5ca8f1af6e8c223f803d1770c5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/blspy/2.0.2/blspy-2.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fc615db0f93911c24bb6512140e8f098f506d455c44fa1830fdb98e2590bc9f0" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/blspy/2.0.2/blspy-2.0.2-cp310-cp310-win_amd64.whl", hash = "sha256:c5581b44d0a4e01ba55183cadfc0139b9a97620c7910a5487023918f07b85271" },
]

[[package]]
name = "boto3"
version = "1.21.27"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "botocore" },
    { name = "jmespath" },
    { name = "s3transfer" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/boto3/1.21.27/boto3-1.21.27.tar.gz", hash = "sha256:ef41b9c7b6311d5152bdc78f7de56912c1ed265debf7da14133e1ad00246ad50" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/boto3/1.21.27/boto3-1.21.27-py3-none-any.whl", hash = "sha256:f165790439117e3fd40f8c06826845068852a70ca5ac62adb192405c97f117e1" },
]

[[package]]
name = "botocore"
version = "1.24.46"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "jmespath" },
    { name = "python-dateutil" },
    { name = "urllib3" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/botocore/1.24.46/botocore-1.24.46.tar.gz", hash = "sha256:89a203bba3c8f2299287e48a9e112e2dbe478cf67eaac26716f0e7f176446146" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/botocore/1.24.46/botocore-1.24.46-py3-none-any.whl", hash = "sha256:663d8f02b98641846eb959c54c840cc33264d5f2dee5b8fc09ee8adbef0f8dcf" },
]

[[package]]
name = "cached-property"
version = "2.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/cached-property/2.0.1/cached_property-2.0.1.tar.gz", hash = "sha256:484d617105e3ee0e4f1f58725e72a8ef9e93deee462222dbd51cd91230897641" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cached-property/2.0.1/cached_property-2.0.1-py3-none-any.whl", hash = "sha256:f617d70ab1100b7bcf6e42228f9ddcb78c676ffa167278d9f730d1c2fba69ccb" },
]

[[package]]
name = "ccxt"
version = "1.95.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aiodns" },
    { name = "aiohttp" },
    { name = "certifi" },
    { name = "cryptography" },
    { name = "requests" },
    { name = "setuptools" },
    { name = "yarl" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ccxt/1.95.1/ccxt-1.95.1.tar.gz", hash = "sha256:e0286141d2e987b1e3f6d3344d49dc61946c406554d745af6d9b4df770df13b9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ccxt/1.95.1/ccxt-1.95.1-py2.py3-none-any.whl", hash = "sha256:cc0bbba16f6b35722dca4fad28e3aa875668a715984fb9eacdcd4b5691f7d89f" },
]

[[package]]
name = "celery"
version = "5.2.7"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "billiard" },
    { name = "click" },
    { name = "click-didyoumean" },
    { name = "click-plugins" },
    { name = "click-repl" },
    { name = "kombu" },
    { name = "pytz" },
    { name = "vine" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/celery/5.2.7/celery-5.2.7.tar.gz", hash = "sha256:fafbd82934d30f8a004f81e8f7a062e31413a23d444be8ee3326553915958c6d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/celery/5.2.7/celery-5.2.7-py3-none-any.whl", hash = "sha256:138420c020cd58d6707e6257b6beda91fd39af7afde5d36c6334d175302c0e14" },
]

[[package]]
name = "certifi"
version = "2025.1.31"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/certifi/2025.1.31/certifi-2025.1.31.tar.gz", hash = "sha256:3d5da6925056f6f18f119200434a4780a94263f10d1c21d032a6f6b2baa20651" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/certifi/2025.1.31/certifi-2025.1.31-py3-none-any.whl", hash = "sha256:ca78db4565a652026a4db2bcdf68f2fb589ea80d0be70e03929ed730746b84fe" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:df8b1c11f177bc2313ec4b2d46baec87a5f3e71fc8b45dab2ee7cae86d9aba14" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:8f2cdc858323644ab277e9bb925ad72ae0e67f69e804f4898c070998d50b1a67" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:edae79245293e15384b51f88b00613ba9f7198016a5948b5dddf4917d4d26382" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:45398b671ac6d70e67da8e4224a065cec6a93541bb7aebe1b198a61b58c7b702" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:ad9413ccdeda48c5afdae7e4fa2192157e991ff761e7ab8fdd8926f40b160cc3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5da5719280082ac6bd9aa7becb3938dc9f9cbd57fac7d2871717b1feb0902ab6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2bb1a08b8008b281856e5971307cc386a8e9c5b625ac297e853d36da6efe9c17" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:045d61c734659cc045141be4bae381a41d89b741f795af1dd018bfb532fd0df8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-musllinux_1_1_i686.whl", hash = "sha256:6883e737d7d9e4899a8a695e00ec36bd4e5e4f18fabe0aca0efe0a4b44cdb13e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:6b8b4a92e1c65048ff98cfe1f735ef8f1ceb72e3d5f0c25fdb12087a23da22be" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-win32.whl", hash = "sha256:c9c3d058ebabb74db66e431095118094d06abf53284d9c81f27300d0e0d8bc7c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cffi/1.17.1/cffi-1.17.1-cp310-cp310-win_amd64.whl", hash = "sha256:0f048dcf80db46f0098ccac01132761580d28e28bc0f78ae0d58048063317e15" },
]

[[package]]
name = "cfgv"
version = "3.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/cfgv/3.4.0/cfgv-3.4.0.tar.gz", hash = "sha256:e52591d4c5f5dead8e0f673fb16db7949d2cfb3f7da4582893288f0ded8fe560" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cfgv/3.4.0/cfgv-3.4.0-py2.py3-none-any.whl", hash = "sha256:b7265b1f29fd3316bfcd2b330d63d024f2bfd8bcb8b0272f8e19a504856c48f9" },
]

[[package]]
name = "channels"
version = "3.0.5"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "asgiref" },
    { name = "daphne" },
    { name = "django" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/channels/3.0.5/channels-3.0.5.tar.gz", hash = "sha256:a3dc3339cc033e7c2afe083fb3dedf74fc5009815967e317e080e7bfdc92ea26" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/channels/3.0.5/channels-3.0.5-py3-none-any.whl", hash = "sha256:3813b8025bf85509769793aca720e6c3b1c5bde1cb253a961252bf0242b60a26" },
]

[[package]]
name = "channels-redis"
version = "3.4.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aioredis" },
    { name = "asgiref" },
    { name = "channels" },
    { name = "msgpack" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/channels-redis/3.4.1/channels_redis-3.4.1.tar.gz", hash = "sha256:78e4a2f2b2a744fe5a87848ec36b5ee49f522c6808cefe6c583663d0d531faa8" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/channels-redis/3.4.1/channels_redis-3.4.1-py3-none-any.whl", hash = "sha256:ba7e2ad170f273c372812dd32aaac102d68d4e508172abb1cfda3160b7333890" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:7c48ed483eb946e6c04ccbe02c6b4d1d48e51944b6db70f697e089c193404941" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:b2d318c11350e10662026ad0eb71bb51c7812fc8590825304ae0bdd4ac283acd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:9cbfacf36cb0ec2897ce0ebc5d08ca44213af24265bd56eca54bee7923c48fd6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:18dd2e350387c87dabe711b86f83c9c78af772c748904d372ade190b5c7c9d4d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8075c35cd58273fee266c58c0c9b670947c19df5fb98e7b66710e04ad4e9ff86" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:5bf4545e3b962767e5c06fe1738f951f77d27967cb2caa64c28be7c4563e162c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:7a6ab32f7210554a96cd9e33abe3ddd86732beeafc7a28e9955cdf22ffadbab0" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:b33de11b92e9f75a2b545d6e9b6f37e398d86c3e9e9653c4864eb7e89c5773ef" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:8755483f3c00d6c9a77f490c17e6ab0c8729e39e6390328e42521ef175380ae6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:68a328e5f55ec37c57f19ebb1fdc56a248db2e3e9ad769919a58672958e8f366" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:21b2899062867b0e1fde9b724f8aecb1af14f2778d69aacd1a5a1853a597a5db" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-win32.whl", hash = "sha256:e8082b26888e2f8b36a042a58307d5b917ef2b1cacab921ad3323ef91901c71a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-win_amd64.whl", hash = "sha256:f69a27e45c43520f5487f27627059b64aaf160415589230992cec34c5e18a509" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/charset-normalizer/3.4.2/charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0" },
]

[[package]]
name = "ckzg"
version = "2.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1.tar.gz", hash = "sha256:d6b306b7ec93a24e4346aa53d07f7f75053bc0afc7398e35fa649e5f9d48fcc4" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:4b9825a1458219e8b4b023012b8ef027ef1f47e903f9541cbca4615f80132730" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:e2a40a3ba65cca4b52825d26829e6f7eb464aa27a9e9efb6b8b2ce183442c741" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a1d753fbe85be7c21602eddc2d40e0915e25fce10329f4f801a0002a4f886cc7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9d76b50527f1d12430bf118aff6fa4051e9860eada43f29177258b8d399448ea" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:44c8603e43c021d100f355f50189183135d1df3cbbddb8881552d57fbf421dde" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:38707a638c9d715b3c30b29352b969f78d8fc10faed7db5faf517f04359895c0" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:52c4d257bdcbe822d20c5cd24c8154ec5aac33c49a8f5a19e716d9107a1c8785" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:1507f7bfb9bcf51d816db5d8d0f0ed53c8289605137820d437b69daea8333e16" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-cp310-cp310-win_amd64.whl", hash = "sha256:d02eaaf4f841910133552b3a051dea53bcfe60cd98199fc4cf80b27609d8baa2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:375918e25eafb9bafe5215ab91698504cba3fe51b4fe92f5896af6c5663f50c6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:38b3b7802c76d4ad015db2b7a79a49c193babae50ee5f77e9ac2865c9e9ddb09" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-pp310-pypy310_pp73-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:438a5009fd254ace0bc1ad974d524547f1a41e6aa5e778c5cd41f4ee3106bcd6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0ce11cc163a2e0dab3af7455aca7053f9d5bb8d157f231acc7665fd230565d48" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b53964c07f6a076e97eaa1ef35045e935d7040aff14f80bae7e9105717702d05" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ckzg/2.1.1/ckzg-2.1.1-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:cf085f15ae52ab2599c9b5a3d5842794bcf5613b7f58661fbfb0c5d9eac988b9" },
]

[[package]]
name = "click"
version = "8.2.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/click/8.2.1/click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/click/8.2.1/click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b" },
]

[[package]]
name = "click-didyoumean"
version = "0.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "click" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/click-didyoumean/0.3.1/click_didyoumean-0.3.1.tar.gz", hash = "sha256:4f82fdff0dbe64ef8ab2279bd6aa3f6a99c3b28c05aa09cbfc07c9d7fbb5a463" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/click-didyoumean/0.3.1/click_didyoumean-0.3.1-py3-none-any.whl", hash = "sha256:5c4bb6007cfea5f2fd6583a2fb6701a22a41eb98957e63d0fac41c10e7c3117c" },
]

[[package]]
name = "click-plugins"
version = "1.1.1.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "click" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/click-plugins/1.1.1.2/click_plugins-1.1.1.2.tar.gz", hash = "sha256:d7af3984a99d243c131aa1a828331e7630f4a88a9741fd05c927b204bcf92261" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/click-plugins/1.1.1.2/click_plugins-1.1.1.2-py2.py3-none-any.whl", hash = "sha256:008d65743833ffc1f5417bf0e78e8d2c23aab04d9745ba817bd3e71b0feb6aa6" },
]

[[package]]
name = "click-repl"
version = "0.3.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "click" },
    { name = "prompt-toolkit" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/click-repl/0.3.0/click-repl-0.3.0.tar.gz", hash = "sha256:17849c23dba3d667247dc4defe1757fff98694e90fe37474f3feebb69ced26a9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/click-repl/0.3.0/click_repl-0.3.0-py3-none-any.whl", hash = "sha256:fb7e06deb8da8de86180a33a9da97ac316751c094c6899382da7feeeeb51b812" },
]

[[package]]
name = "cobo-app-python-api"
version = "0.1.64"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pydantic" },
    { name = "python-dateutil" },
    { name = "typing-extensions" },
    { name = "urllib3" },
]
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cobo-app-python-api/0.1.64/cobo_app_python_api-0.1.64-py3-none-any.whl", hash = "sha256:86c89106ca340989116717382b1daed5554d0cb4feac8892826beca8749373e2" },
]

[[package]]
name = "cobo-custody"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "asgiref" },
    { name = "authlib" },
    { name = "backoff" },
    { name = "base58" },
    { name = "bc-waas2" },
    { name = "betterproto" },
    { name = "bitcoinlib" },
    { name = "boto3" },
    { name = "ccxt" },
    { name = "certifi" },
    { name = "channels" },
    { name = "channels-redis" },
    { name = "cobo-app-python-api" },
    { name = "cobo-libs" },
    { name = "cobo-waas2-python-api" },
    { name = "confluent-kafka" },
    { name = "cosmos-proto" },
    { name = "croniter" },
    { name = "daphne" },
    { name = "django" },
    { name = "django-cors-headers" },
    { name = "django-filter" },
    { name = "django-split-settings" },
    { name = "djangorestframework-dataclasses" },
    { name = "ecdsa" },
    { name = "eth-typing" },
    { name = "gate-api" },
    { name = "geoip2" },
    { name = "googleapis-common-protos" },
    { name = "graphene" },
    { name = "graphene-django" },
    { name = "graphql-core" },
    { name = "grpcio" },
    { name = "hashids" },
    { name = "httpx", extra = ["http2"] },
    { name = "ipython" },
    { name = "iso8601" },
    { name = "onfido-python" },
    { name = "openpyxl" },
    { name = "phonenumbers" },
    { name = "pillow" },
    { name = "protobuf" },
    { name = "py-solc-x" },
    { name = "pycryptodomex" },
    { name = "pyopenssl" },
    { name = "pysha3" },
    { name = "python-axolotl-curve25519" },
    { name = "python-bitcointx" },
    { name = "qrcode" },
    { name = "redis" },
    { name = "safe-eth-py" },
    { name = "sentry-sdk" },
    { name = "simpleeval" },
    { name = "solders" },
    { name = "user-agents" },
    { name = "web3" },
    { name = "whitenoise" },
    { name = "xhtml2pdf" },
    { name = "xlsxwriter" },
    { name = "yubico-client" },
]

[package.dev-dependencies]
dev = [
    { name = "bandit" },
    { name = "black" },
    { name = "coverage" },
    { name = "django-upgrade" },
    { name = "flake8" },
    { name = "ipython" },
    { name = "isort" },
    { name = "ossaudit" },
    { name = "pre-commit" },
    { name = "pysocks" },
]
test = [
    { name = "allure-pytest" },
    { name = "coverage" },
    { name = "factory-boy" },
    { name = "mock" },
    { name = "pytest" },
    { name = "pytest-cache" },
    { name = "pytest-cov" },
    { name = "pytest-django" },
    { name = "pytest-env" },
    { name = "pytest-faulthandler" },
    { name = "pytest-mock" },
    { name = "pytest-pep8" },
    { name = "pytest-timeout" },
    { name = "pytest-xdist" },
]

[package.metadata]
requires-dist = [
    { name = "asgiref", specifier = "==3.6.0" },
    { name = "authlib", specifier = "==1.2.1" },
    { name = "backoff", specifier = "==2.1.2" },
    { name = "base58", specifier = "==2.1.1" },
    { name = "bc-waas2", specifier = "==0.0.48" },
    { name = "betterproto", specifier = "==2.0.0b4" },
    { name = "bitcoinlib", specifier = "==********" },
    { name = "boto3", specifier = "==1.21.27" },
    { name = "ccxt", specifier = "==1.95.1" },
    { name = "certifi", specifier = "==2025.1.31" },
    { name = "channels", specifier = "==3.0.5" },
    { name = "channels-redis", specifier = "==3.4.1" },
    { name = "cobo-app-python-api", specifier = "==0.1.64" },
    { name = "cobo-libs", specifier = "==0.20.270" },
    { name = "cobo-waas2-python-api", specifier = "==0.6.134" },
    { name = "confluent-kafka", specifier = "==2.3.0" },
    { name = "cosmos-proto", specifier = "==0.0.0" },
    { name = "croniter", specifier = "==1.4.0" },
    { name = "daphne", specifier = "==3.0.2" },
    { name = "django", specifier = "==4.2.16" },
    { name = "django-cors-headers", specifier = "==3.13.0" },
    { name = "django-filter", specifier = "==22.1" },
    { name = "django-split-settings", specifier = "==1.1.0" },
    { name = "djangorestframework-dataclasses", specifier = "==1.3.1" },
    { name = "ecdsa", specifier = "==0.19.0" },
    { name = "eth-typing", specifier = "==4.4.0" },
    { name = "gate-api", specifier = "==4.70.0" },
    { name = "geoip2", specifier = "==4.6.0" },
    { name = "googleapis-common-protos", specifier = "==1.69.1" },
    { name = "graphene", specifier = "==3.3" },
    { name = "graphene-django", specifier = "==3.2.2" },
    { name = "graphql-core", specifier = "==3.2.3" },
    { name = "grpcio", specifier = "==1.71.0" },
    { name = "hashids", specifier = "==1.3.1" },
    { name = "httpx", extras = ["http2"], specifier = "==0.24.1" },
    { name = "ipython", specifier = ">=8.37.0" },
    { name = "iso8601", specifier = "==1.0.2" },
    { name = "onfido-python", specifier = "==2.1.0" },
    { name = "openpyxl", specifier = "==3.1.2" },
    { name = "phonenumbers", specifier = "==8.12.45" },
    { name = "pillow", specifier = "==9.3" },
    { name = "protobuf", specifier = "==5.29.3" },
    { name = "py-solc-x", specifier = "==2.0.0" },
    { name = "pycryptodomex", specifier = "==3.20.0" },
    { name = "pyopenssl", specifier = "==23.2.0" },
    { name = "pysha3", specifier = "==1.0.2" },
    { name = "python-axolotl-curve25519", git = "https://github.com/hannob/python-axolotl-curve25519.git?rev=901f4fb12e1290b72fbd26ea1f40755b079fa241" },
    { name = "python-bitcointx", specifier = "==1.1.5" },
    { name = "qrcode", specifier = "==7.3.1" },
    { name = "redis", specifier = "==6.2.0" },
    { name = "safe-eth-py", specifier = "==5.8.0" },
    { name = "sentry-sdk", specifier = "==1.9.8" },
    { name = "simpleeval", specifier = "==1.0.3" },
    { name = "solders", specifier = "==0.10.0" },
    { name = "user-agents", specifier = "==2.2.0" },
    { name = "web3", specifier = "==6.14.0" },
    { name = "whitenoise", specifier = "==6.8.2" },
    { name = "xhtml2pdf", specifier = "==0.2.8" },
    { name = "xlsxwriter", specifier = "==3.2.0" },
    { name = "yubico-client", specifier = "==1.13.0" },
]

[package.metadata.requires-dev]
dev = [
    { name = "bandit", specifier = "==1.6.2" },
    { name = "black", specifier = "==22.3.0" },
    { name = "coverage", specifier = ">=7.9.2" },
    { name = "django-upgrade", specifier = "==1.21.0" },
    { name = "flake8", specifier = "==3.8.3" },
    { name = "ipython", specifier = ">=8.37.0" },
    { name = "isort", specifier = "==5.1.4" },
    { name = "ossaudit", specifier = "==0.5.0" },
    { name = "pre-commit", specifier = "==2.19.0" },
    { name = "pysocks", specifier = "==1.7.1" },
]
test = [
    { name = "allure-pytest", specifier = "==2.13.2" },
    { name = "coverage", specifier = ">=7.9.2" },
    { name = "factory-boy", specifier = "==3.3.3" },
    { name = "mock", specifier = "==5.1.0" },
    { name = "pytest", specifier = "==7.4.3" },
    { name = "pytest-cache", specifier = "==1.0" },
    { name = "pytest-cov", specifier = "==4.1.0" },
    { name = "pytest-django", specifier = "==4.7.0" },
    { name = "pytest-env", specifier = "==1.1.1" },
    { name = "pytest-faulthandler", specifier = "==2.0.1" },
    { name = "pytest-mock", specifier = "==3.12.0" },
    { name = "pytest-pep8", specifier = "==1.0.6" },
    { name = "pytest-timeout", specifier = "==2.2.0" },
    { name = "pytest-xdist", specifier = "==3.6.1" },
]

[[package]]
name = "cobo-libs"
version = "0.20.270"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "base58" },
    { name = "bech32" },
    { name = "bech32m" },
    { name = "blake256" },
    { name = "blspy" },
    { name = "celery" },
    { name = "cryptography" },
    { name = "defusedxml" },
    { name = "django-redis" },
    { name = "djangorestframework" },
    { name = "djangorestframework-dataclasses" },
    { name = "drf-spectacular" },
    { name = "elastic-apm" },
    { name = "eth-hash", extra = ["pycryptodome"] },
    { name = "gevent" },
    { name = "gitpython" },
    { name = "groestlcoin-hash" },
    { name = "influxdb" },
    { name = "jinja2" },
    { name = "mysqlclient" },
    { name = "prometheus-client" },
    { name = "psutil" },
    { name = "py-ecc" },
    { name = "pycoin" },
    { name = "pydantic" },
    { name = "pyjwt" },
    { name = "pymongo" },
    { name = "pynacl" },
    { name = "pyopenssl" },
    { name = "pyotp" },
    { name = "pypd" },
    { name = "python-axolotl-curve25519" },
    { name = "python-logstash" },
    { name = "python-telegram-bot" },
    { name = "pytz" },
    { name = "requests" },
    { name = "slack-sdk" },
    { name = "sortedcontainers" },
    { name = "spiffworkflow" },
    { name = "sqlalchemy" },
    { name = "stellar-sdk" },
    { name = "websocket-client" },
]
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cobo-libs/0.20.270/cobo_libs-0.20.270-py3-none-any.whl", hash = "sha256:54b67a75ef610461be3e976cf14d845206442f18869e0b9a42acaecdb03a8ffb" },
]

[[package]]
name = "cobo-waas2-python-api"
version = "0.6.134"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pydantic" },
    { name = "pynacl" },
    { name = "python-dateutil" },
    { name = "typing-extensions" },
    { name = "urllib3" },
]
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cobo-waas2-python-api/0.6.134/cobo_waas2_python_api-0.6.134-py3-none-any.whl", hash = "sha256:c09a85b170da0df196289c484b0c731b0617c80d90ed3340a3c85b019eda3738" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/colorama/0.4.6/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/colorama/0.4.6/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6" },
]

[[package]]
name = "configparser"
version = "7.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/configparser/7.2.0/configparser-7.2.0.tar.gz", hash = "sha256:b629cc8ae916e3afbd36d1b3d093f34193d851e11998920fdcfc4552218b7b70" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/configparser/7.2.0/configparser-7.2.0-py3-none-any.whl", hash = "sha256:fee5e1f3db4156dcd0ed95bc4edfa3580475537711f67a819c966b389d09ce62" },
]

[[package]]
name = "confluent-kafka"
version = "2.3.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/confluent-kafka/2.3.0/confluent-kafka-2.3.0.tar.gz", hash = "sha256:4069e7b56e0baf9db18c053a605213f0ab2d8f23715dca7b3bd97108df446ced" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/confluent-kafka/2.3.0/confluent_kafka-2.3.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:5df845755cd3ebb9165ca00fd1d3a7d514c61e84d9fcbe7babb91193fe9b369c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/confluent-kafka/2.3.0/confluent_kafka-2.3.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:9ab2217875b731bd390582952e0f9cbe3e7b34774490f01afca70728f0d8b469" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/confluent-kafka/2.3.0/confluent_kafka-2.3.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:62046e8a75c7a6883a0f1f4a635573fd7e1665eeacace65e7f6d59cbaa94697d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/confluent-kafka/2.3.0/confluent_kafka-2.3.0-cp310-cp310-manylinux_2_28_aarch64.whl", hash = "sha256:1eba38061e9ed1c0a369c129bf01d07499286cc3cb295398b88a7037c14371fb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/confluent-kafka/2.3.0/confluent_kafka-2.3.0-cp310-cp310-win_amd64.whl", hash = "sha256:a6abece28598fa2b59d2b9399fcec03440aaa73fd207fdad048a6030d7e897e1" },
]

[[package]]
name = "constantly"
version = "23.10.4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/constantly/23.10.4/constantly-23.10.4.tar.gz", hash = "sha256:aa92b70a33e2ac0bb33cd745eb61776594dc48764b06c35e0efd050b7f1c7cbd" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/constantly/23.10.4/constantly-23.10.4-py3-none-any.whl", hash = "sha256:3fd9b4d1c3dc1ec9757f3c52aef7e53ad9323dbe39f51dfd4c43853b68dfa3f9" },
]

[[package]]
name = "cosmos-proto"
version = "0.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "betterproto" },
    { name = "markupsafe" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/cosmos-proto/0.0.0/cosmos-proto-0.0.0.tar.gz", hash = "sha256:6fec2a2450f3ee69d3738bcc895603ceeab492241d6f2e539ea92d10f76ffe35" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cosmos-proto/0.0.0/cosmos_proto-0.0.0-py3-none-any.whl", hash = "sha256:feeb5441fa4a00b5eb198a312e4cf56047f2be99d3604ed55b6ace6f03fecfee" },
]

[[package]]
name = "coverage"
version = "7.9.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2.tar.gz", hash = "sha256:997024fa51e3290264ffd7492ec97d0690293ccd2b45a6cd7d82d945a4a80c8b" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:66283a192a14a3854b2e7f3418d7db05cdf411012ab7ff5db98ff3b181e1f912" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:4e01d138540ef34fcf35c1aa24d06c3de2a4cffa349e29a10056544f35cca15f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:f22627c1fe2745ee98d3ab87679ca73a97e75ca75eb5faee48660d060875465f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:4b1c2d8363247b46bd51f393f86c94096e64a1cf6906803fa8d5a9d03784bdbf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c10c882b114faf82dbd33e876d0cbd5e1d1ebc0d2a74ceef642c6152f3f4d547" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:de3c0378bdf7066c3988d66cd5232d161e933b87103b014ab1b0b4676098fa45" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:1e2f097eae0e5991e7623958a24ced3282676c93c013dde41399ff63e230fcf2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:28dc1f67e83a14e7079b6cea4d314bc8b24d1aed42d3582ff89c0295f09b181e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-win32.whl", hash = "sha256:bf7d773da6af9e10dbddacbf4e5cab13d06d0ed93561d44dae0188a42c65be7e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-cp310-cp310-win_amd64.whl", hash = "sha256:0c0378ba787681ab1897f7c89b415bd56b0b2d9a47e5a3d8dc0ea55aac118d6c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-pp39.pp310.pp311-none-any.whl", hash = "sha256:8a1166db2fb62473285bcb092f586e081e92656c7dfa8e9f62b4d39d7e6b5050" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/coverage/7.9.2/coverage-7.9.2-py3-none-any.whl", hash = "sha256:e425cd5b00f6fc0ed7cdbd766c70be8baab4b7839e4d4fe5fac48581dd968ea4" },
]

[package.optional-dependencies]
toml = [
    { name = "tomli" },
]

[[package]]
name = "croniter"
version = "1.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "python-dateutil" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/croniter/1.4.0/croniter-1.4.0.tar.gz", hash = "sha256:edf332f2ef3b2b47d4c01004c640981c5a70d8ccb6b854aea8866a28b532449a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/croniter/1.4.0/croniter-1.4.0-py2.py3-none-any.whl", hash = "sha256:4cb177d0167d4a61b1aa3575fcf06b9a064e9a48a4b11d6ab474b1b9e126ed9c" },
]

[[package]]
name = "cryptography"
version = "41.0.5"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5.tar.gz", hash = "sha256:392cb88b597247177172e02da6b7a63deeff1937fa6fec3bbf902ebd75d97ec7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-macosx_10_12_universal2.whl", hash = "sha256:da6a0ff8f1016ccc7477e6339e1d50ce5f59b88905585f77193ebd5068f1e797" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-macosx_10_12_x86_64.whl", hash = "sha256:b948e09fe5fb18517d99994184854ebd50b57248736fd4c720ad540560174ec5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d38e6031e113b7421db1de0c1b1f7739564a88f1684c6b89234fbf6c11b75147" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e270c04f4d9b5671ebcc792b3ba5d4488bf7c42c3c241a3748e2599776f29696" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:ec3b055ff8f1dce8e6ef28f626e0972981475173d7973d63f271b29c8a2897da" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:7d208c21e47940369accfc9e85f0de7693d9a5d843c2509b3846b2db170dfd20" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:8254962e6ba1f4d2090c44daf50a547cd5f0bf446dc658a8e5f8156cae0d8548" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:a48e74dad1fb349f3dc1d449ed88e0017d792997a7ad2ec9587ed17405667e6d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-win32.whl", hash = "sha256:d3977f0e276f6f5bf245c403156673db103283266601405376f075c849a0b936" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-cp37-abi3-win_amd64.whl", hash = "sha256:73801ac9736741f220e20435f84ecec75ed70eda90f781a148f1bad546963d81" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-pp310-pypy310_pp73-macosx_10_12_x86_64.whl", hash = "sha256:3be3ca726e1572517d2bef99a818378bbcf7d7799d5372a46c79c29eb8d166c1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-pp310-pypy310_pp73-manylinux_2_28_aarch64.whl", hash = "sha256:e886098619d3815e0ad5790c973afeee2c0e6e04b4da90b88e6bd06e2a0b1b72" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-pp310-pypy310_pp73-manylinux_2_28_x86_64.whl", hash = "sha256:573eb7128cbca75f9157dcde974781209463ce56b5804983e11a1c462f0f4e88" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cryptography/41.0.5/cryptography-41.0.5-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:0c327cac00f082013c7c9fb6c46b7cc9fa3c288ca702c74773968173bda421bf" },
]

[[package]]
name = "cssselect2"
version = "0.8.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "tinycss2" },
    { name = "webencodings" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/cssselect2/0.8.0/cssselect2-0.8.0.tar.gz", hash = "sha256:7674ffb954a3b46162392aee2a3a0aedb2e14ecf99fcc28644900f4e6e3e9d3a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cssselect2/0.8.0/cssselect2-0.8.0-py3-none-any.whl", hash = "sha256:46fc70ebc41ced7a32cd42d58b1884d72ade23d21e5a4eaaf022401c13f0e76e" },
]

[[package]]
name = "cytoolz"
version = "1.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "toolz" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1.tar.gz", hash = "sha256:89cc3161b89e1bb3ed7636f74ed2e55984fd35516904fc878cae216e42b2c7d6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:cec9af61f71fc3853eb5dca3d42eb07d1f48a4599fa502cbe92adde85f74b042" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:140bbd649dbda01e91add7642149a5987a7c3ccc251f2263de894b89f50b6608" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:e90124bdc42ff58b88cdea1d24a6bc5f776414a314cc4d94f25c88badb3a16d1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:e74801b751e28f7c5cc3ad264c123954a051f546f2fdfe089f5aa7a12ccfa6da" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:582dad4545ddfb5127494ef23f3fa4855f1673a35d50c66f7638e9fb49805089" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:dd7bd0618e16efe03bd12f19c2a26a27e6e6b75d7105adb7be1cd2a53fa755d8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d74cca6acf1c4af58b2e4a89cc565ed61c5e201de2e434748c93e5a0f5c541a5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:823a3763828d8d457f542b2a45d75d6b4ced5e470b5c7cf2ed66a02f508ed442" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:51633a14e6844c61db1d68c1ffd077cf949f5c99c60ed5f1e265b9e2966f1b52" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:f3ec9b01c45348f1d0d712507d54c2bfd69c62fbd7c9ef555c9d8298693c2432" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:1855022b712a9c7a5bce354517ab4727a38095f81e2d23d3eabaf1daeb6a3b3c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:9930f7288c4866a1dc1cc87174f0c6ff4cad1671eb1f6306808aa6c445857d78" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-win32.whl", hash = "sha256:a9baad795d72fadc3445ccd0f122abfdbdf94269157e6d6d4835636dad318804" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-cp310-cp310-win_amd64.whl", hash = "sha256:ad95b386a84e18e1f6136f6d343d2509d4c3aae9f5a536f3dc96808fcc56a8cf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:83d19d55738ad9c60763b94f3f6d3c6e4de979aeb8d76841c1401081e0e58d96" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:f112a71fad6ea824578e6393765ce5c054603afe1471a5c753ff6c67fd872d10" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:5a515df8f8aa6e1eaaf397761a6e4aff2eef73b5f920aedf271416d5471ae5ee" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-pp310-pypy310_pp73-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:92c398e7b7023460bea2edffe5fcd0a76029580f06c3f6938ac3d198b47156f3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/cytoolz/1.0.1/cytoolz-1.0.1-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:3237e56211e03b13df47435b2369f5df281e02b04ad80a948ebd199b7bc10a47" },
]

[[package]]
name = "daphne"
version = "3.0.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "asgiref" },
    { name = "autobahn" },
    { name = "twisted", extra = ["tls"] },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/daphne/3.0.2/daphne-3.0.2.tar.gz", hash = "sha256:76ffae916ba3aa66b46996c14fa713e46004788167a4873d647544e750e0e99f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/daphne/3.0.2/daphne-3.0.2-py3-none-any.whl", hash = "sha256:a9af943c79717bc52fe64a3c236ae5d3adccc8b5be19c881b442d2c3db233393" },
]

[[package]]
name = "dateparser"
version = "1.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "regex" },
    { name = "tzlocal" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/dateparser/1.2.0/dateparser-1.2.0.tar.gz", hash = "sha256:7975b43a4222283e0ae15be7b4999d08c9a70e2d378ac87385b1ccf2cffbbb30" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/dateparser/1.2.0/dateparser-1.2.0-py2.py3-none-any.whl", hash = "sha256:0b21ad96534e562920a0083e97fd45fa959882d4162acc358705144520a35830" },
]

[[package]]
name = "decorator"
version = "5.2.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/decorator/5.2.1/decorator-5.2.1.tar.gz", hash = "sha256:65f266143752f734b0a7cc83c46f4618af75b8c5911b00ccb61d0ac9b6da0360" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/decorator/5.2.1/decorator-5.2.1-py3-none-any.whl", hash = "sha256:d316bb415a2d9e2d2b3abcc4084c6502fc09240e292cd76a76afc106a1c8e04a" },
]

[[package]]
name = "defusedxml"
version = "0.7.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/defusedxml/0.7.1/defusedxml-0.7.1.tar.gz", hash = "sha256:1bb3032db185915b62d7c6209c5a8792be6a32ab2fedacc84e01b52c51aa3e69" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/defusedxml/0.7.1/defusedxml-0.7.1-py2.py3-none-any.whl", hash = "sha256:a352e7e428770286cc899e2542b6cdaedb2b4953ff269a210103ec58f6198a61" },
]

[[package]]
name = "distlib"
version = "0.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/distlib/0.4.0/distlib-0.4.0.tar.gz", hash = "sha256:feec40075be03a04501a973d81f633735b4b69f98b05450592310c0f401a4e0d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/distlib/0.4.0/distlib-0.4.0-py2.py3-none-any.whl", hash = "sha256:9659f7d87e46584a30b5780e43ac7a2143098441670ff0a49d5f9034c54a6c16" },
]

[[package]]
name = "django"
version = "4.2.16"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "asgiref" },
    { name = "sqlparse" },
    { name = "tzdata", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/django/4.2.16/Django-4.2.16.tar.gz", hash = "sha256:6f1616c2786c408ce86ab7e10f792b8f15742f7b7b7460243929cb371e7f1dad" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/django/4.2.16/Django-4.2.16-py3-none-any.whl", hash = "sha256:1ddc333a16fc139fd253035a1606bb24261951bbc3a6ca256717fa06cc41a898" },
]

[[package]]
name = "django-cors-headers"
version = "3.13.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "django" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-cors-headers/3.13.0/django-cors-headers-3.13.0.tar.gz", hash = "sha256:f9dc6b4e3f611c3199700b3e5f3398c28757dcd559c2f82932687f3d0443cfdf" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-cors-headers/3.13.0/django_cors_headers-3.13.0-py3-none-any.whl", hash = "sha256:37e42883b5f1f2295df6b4bba96eb2417a14a03270cb24b2a07f021cd4487cf4" },
]

[[package]]
name = "django-filter"
version = "22.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "django" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-filter/22.1/django-filter-22.1.tar.gz", hash = "sha256:ed473b76e84f7e83b2511bb2050c3efb36d135207d0128dfe3ae4b36e3594ba5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-filter/22.1/django_filter-22.1-py3-none-any.whl", hash = "sha256:ed429e34760127e3520a67f415bec4c905d4649fbe45d0d6da37e6ff5e0287eb" },
]

[[package]]
name = "django-redis"
version = "5.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "django" },
    { name = "redis" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-redis/5.2.0/django-redis-5.2.0.tar.gz", hash = "sha256:8a99e5582c79f894168f5865c52bd921213253b7fd64d16733ae4591564465de" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-redis/5.2.0/django_redis-5.2.0-py3-none-any.whl", hash = "sha256:1d037dc02b11ad7aa11f655d26dac3fb1af32630f61ef4428860a2e29ff92026" },
]

[[package]]
name = "django-split-settings"
version = "1.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-split-settings/1.1.0/django-split-settings-1.1.0.tar.gz", hash = "sha256:6b3aed89667a95525152026eab93a9f038ff22df6883006318b8b4a3d0ca6888" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-split-settings/1.1.0/django_split_settings-1.1.0-py3-none-any.whl", hash = "sha256:5d97ae64cf9ed14a831722d82ac725944667ac8c08307b7cfd22e91367b411d0" },
]

[[package]]
name = "django-upgrade"
version = "1.21.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "tokenize-rt" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-upgrade/1.21.0/django_upgrade-1.21.0.tar.gz", hash = "sha256:e65021029e7d18b407bd128a8ccb31e5e06685068b37b6b1eaf2f77aa3d3df98" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/django-upgrade/1.21.0/django_upgrade-1.21.0-py3-none-any.whl", hash = "sha256:a0a7b11d5108fb2d3038cea2382c1332c9be4ff5059a38357fbd28116ebf3803" },
]

[[package]]
name = "djangorestframework"
version = "3.14.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "django" },
    { name = "pytz" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/djangorestframework/3.14.0/djangorestframework-3.14.0.tar.gz", hash = "sha256:579a333e6256b09489cbe0a067e66abe55c6595d8926be6b99423786334350c8" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/djangorestframework/3.14.0/djangorestframework-3.14.0-py3-none-any.whl", hash = "sha256:eb63f58c9f218e1a7d064d17a70751f528ed4e1d35547fdade9aaf4cd103fd08" },
]

[[package]]
name = "djangorestframework-dataclasses"
version = "1.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "django" },
    { name = "djangorestframework" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/djangorestframework-dataclasses/1.3.1/djangorestframework-dataclasses-1.3.1.tar.gz", hash = "sha256:d3796b5ce3f7266d525493c557ce7df9ffeae4367006250298ea4d94da4106c4" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/djangorestframework-dataclasses/1.3.1/djangorestframework_dataclasses-1.3.1-py3-none-any.whl", hash = "sha256:ca1aa1ca99b5306af874376f37355593bb3d1ac7d658d54e2790f9b303968065" },
]

[[package]]
name = "dparse"
version = "0.6.4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "packaging" },
    { name = "tomli" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/dparse/0.6.4/dparse-0.6.4.tar.gz", hash = "sha256:90b29c39e3edc36c6284c82c4132648eaf28a01863eb3c231c2512196132201a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/dparse/0.6.4/dparse-0.6.4-py3-none-any.whl", hash = "sha256:fbab4d50d54d0e739fbb4dedfc3d92771003a5b9aa8545ca7a7045e3b174af57" },
]

[[package]]
name = "drf-spectacular"
version = "0.27.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "django" },
    { name = "djangorestframework" },
    { name = "inflection" },
    { name = "jsonschema" },
    { name = "pyyaml" },
    { name = "uritemplate" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/drf-spectacular/0.27.2/drf-spectacular-0.27.2.tar.gz", hash = "sha256:a199492f2163c4101055075ebdbb037d59c6e0030692fc83a1a8c0fc65929981" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/drf-spectacular/0.27.2/drf_spectacular-0.27.2-py3-none-any.whl", hash = "sha256:b1c04bf8b2fbbeaf6f59414b4ea448c8787aba4d32f76055c3b13335cf7ec37b" },
]

[[package]]
name = "ecdsa"
version = "0.19.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ecdsa/0.19.0/ecdsa-0.19.0.tar.gz", hash = "sha256:60eaad1199659900dd0af521ed462b793bbdf867432b3948e87416ae4caf6bf8" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ecdsa/0.19.0/ecdsa-0.19.0-py2.py3-none-any.whl", hash = "sha256:2cea9b88407fdac7bbeca0833b189e4c9c53f2ef1e1eaa29f6224dbc809b707a" },
]

[[package]]
name = "ecs-logging"
version = "2.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ecs-logging/2.2.0/ecs_logging-2.2.0.tar.gz", hash = "sha256:1dc9e216f614129db0e6a2f9f926da4e4cf8edf8de16d1045a20aa8e950291d3" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ecs-logging/2.2.0/ecs_logging-2.2.0-py3-none-any.whl", hash = "sha256:f6e22d267770b06f797076f49b5fcc9d97108b22f452f5f9ed4b5367b1e61b5b" },
]

[[package]]
name = "elastic-apm"
version = "6.23.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "ecs-logging" },
    { name = "urllib3" },
    { name = "wrapt" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/elastic-apm/6.23.0/elastic-apm-6.23.0.tar.gz", hash = "sha256:1aeef4562486bd9ad611bba15f9eca5aeede6a737777541cb68c47c6cf5df7d4" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/elastic-apm/6.23.0/elastic_apm-6.23.0-py2.py3-none-any.whl", hash = "sha256:5a1a17580560e70fba40b0a2e7682348422de5a9d2d13511efd05646eb4c62ae" },
]

[[package]]
name = "et-xmlfile"
version = "2.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/et-xmlfile/2.0.0/et_xmlfile-2.0.0.tar.gz", hash = "sha256:dab3f4764309081ce75662649be815c4c9081e88f0837825f90fd28317d4da54" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/et-xmlfile/2.0.0/et_xmlfile-2.0.0-py3-none-any.whl", hash = "sha256:7a91720bc756843502c3b7504c77b8fe44217c85c537d85037f0f536151b2caa" },
]

[[package]]
name = "eth-abi"
version = "5.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "eth-typing" },
    { name = "eth-utils" },
    { name = "parsimonious" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-abi/5.2.0/eth_abi-5.2.0.tar.gz", hash = "sha256:178703fa98c07d8eecd5ae569e7e8d159e493ebb6eeb534a8fe973fbc4e40ef0" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-abi/5.2.0/eth_abi-5.2.0-py3-none-any.whl", hash = "sha256:17abe47560ad753f18054f5b3089fcb588f3e3a092136a416b6c1502cb7e8877" },
]

[[package]]
name = "eth-account"
version = "0.11.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "bitarray" },
    { name = "ckzg" },
    { name = "eth-abi" },
    { name = "eth-keyfile" },
    { name = "eth-keys" },
    { name = "eth-rlp" },
    { name = "eth-utils" },
    { name = "hexbytes" },
    { name = "rlp" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-account/0.11.2/eth-account-0.11.2.tar.gz", hash = "sha256:b43daf2c0ae43f2a24ba754d66889f043fae4d3511559cb26eb0122bae9afbbd" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-account/0.11.2/eth_account-0.11.2-py3-none-any.whl", hash = "sha256:95157c262a9823c1e08be826d4bc304bf32f0c32e80afb38c126a325a64f651a" },
]

[[package]]
name = "eth-bloom"
version = "3.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "eth-hash", extra = ["pycryptodome"] },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-bloom/3.1.0/eth_bloom-3.1.0.tar.gz", hash = "sha256:4bc918f6fde44334e92b23cfb345db961e2e3af620535cbc872444f7a143cb88" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-bloom/3.1.0/eth_bloom-3.1.0-py3-none-any.whl", hash = "sha256:c96b2dd6cafa407373bca1a9d74b650378ba672d5b17f2771bf7d3c3aaa7651c" },
]

[[package]]
name = "eth-hash"
version = "0.5.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-hash/0.5.2/eth-hash-0.5.2.tar.gz", hash = "sha256:1b5f10eca7765cc385e1430eefc5ced6e2e463bb18d1365510e2e539c1a6fe4e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-hash/0.5.2/eth_hash-0.5.2-py3-none-any.whl", hash = "sha256:251f62f6579a1e247561679d78df37548bd5f59908da0b159982bf8293ad32f0" },
]

[package.optional-dependencies]
pycryptodome = [
    { name = "pycryptodome" },
]

[[package]]
name = "eth-keyfile"
version = "0.9.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "eth-keys" },
    { name = "eth-utils" },
    { name = "py-ecc" },
    { name = "pycryptodome" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-keyfile/0.9.1/eth_keyfile-0.9.1.tar.gz", hash = "sha256:c7a8bc6af4527d1ab2eb1d1b949d59925252e17663eaf90087da121327b51df6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-keyfile/0.9.1/eth_keyfile-0.9.1-py3-none-any.whl", hash = "sha256:9789c3b4fa0bb6e2616cdc2bdd71b8755b42947d78ef1e900a0149480fabb5c2" },
]

[[package]]
name = "eth-keys"
version = "0.7.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "eth-typing" },
    { name = "eth-utils" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-keys/0.7.0/eth_keys-0.7.0.tar.gz", hash = "sha256:79d24fd876201df67741de3e3fefb3f4dbcbb6ace66e47e6fe662851a4547814" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-keys/0.7.0/eth_keys-0.7.0-py3-none-any.whl", hash = "sha256:b0cdda8ffe8e5ba69c7c5ca33f153828edcace844f67aabd4542d7de38b159cf" },
]

[[package]]
name = "eth-rlp"
version = "1.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "eth-utils" },
    { name = "hexbytes" },
    { name = "rlp" },
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-rlp/1.0.1/eth-rlp-1.0.1.tar.gz", hash = "sha256:d61dbda892ee1220f28fb3663c08f6383c305db9f1f5624dc585c9cd05115027" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-rlp/1.0.1/eth_rlp-1.0.1-py3-none-any.whl", hash = "sha256:dd76515d71654277377d48876b88e839d61553aaf56952e580bb7cebef2b1517" },
]

[[package]]
name = "eth-typing"
version = "4.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-typing/4.4.0/eth_typing-4.4.0.tar.gz", hash = "sha256:93848083ac6bb4c20cc209ea9153a08b0a528be23337c889f89e1e5ffbe9807d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-typing/4.4.0/eth_typing-4.4.0-py3-none-any.whl", hash = "sha256:a5e30a6e69edda7b1d1e96e9d71bab48b9bb988a77909d8d1666242c5562f841" },
]

[[package]]
name = "eth-utils"
version = "4.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cytoolz", marker = "implementation_name == 'cpython'" },
    { name = "eth-hash" },
    { name = "eth-typing" },
    { name = "toolz", marker = "implementation_name == 'pypy'" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-utils/4.1.1/eth_utils-4.1.1.tar.gz", hash = "sha256:71c8d10dec7494aeed20fa7a4d52ec2ce4a2e52fdce80aab4f5c3c19f3648b25" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/eth-utils/4.1.1/eth_utils-4.1.1-py3-none-any.whl", hash = "sha256:ccbbac68a6d65cb6e294c5bcb6c6a5cec79a241c56dc5d9c345ed788c30f8534" },
]

[[package]]
name = "exceptiongroup"
version = "1.3.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/exceptiongroup/1.3.0/exceptiongroup-1.3.0.tar.gz", hash = "sha256:b241f5885f560bc56a59ee63ca4c6a8bfa46ae4ad651af316d4e81817bb9fd88" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/exceptiongroup/1.3.0/exceptiongroup-1.3.0-py3-none-any.whl", hash = "sha256:4d111e6e0c13d0644cad6ddaa7ed0261a0b36971f6d23e7ec9b4b9097da78a10" },
]

[[package]]
name = "execnet"
version = "2.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/execnet/2.1.1/execnet-2.1.1.tar.gz", hash = "sha256:5189b52c6121c24feae288166ab41b32549c7e2348652736540b9e6e7d4e72e3" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/execnet/2.1.1/execnet-2.1.1-py3-none-any.whl", hash = "sha256:26dee51f1b80cebd6d0ca8e74dd8745419761d3bef34163928cbebbdc4749fdc" },
]

[[package]]
name = "executing"
version = "2.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/executing/2.2.0/executing-2.2.0.tar.gz", hash = "sha256:5d108c028108fe2551d1a7b2e8b713341e2cb4fc0aa7dcf966fa4327a5226755" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/executing/2.2.0/executing-2.2.0-py2.py3-none-any.whl", hash = "sha256:11387150cad388d62750327a53d3339fad4888b39a6fe233c3afbb54ecffd3aa" },
]

[[package]]
name = "factory-boy"
version = "3.3.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "faker" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/factory-boy/3.3.3/factory_boy-3.3.3.tar.gz", hash = "sha256:866862d226128dfac7f2b4160287e899daf54f2612778327dd03d0e2cb1e3d03" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/factory-boy/3.3.3/factory_boy-3.3.3-py2.py3-none-any.whl", hash = "sha256:1c39e3289f7e667c4285433f305f8d506efc2fe9c73aaea4151ebd5cdea394fc" },
]

[[package]]
name = "faker"
version = "37.4.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "tzdata" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/faker/37.4.2/faker-37.4.2.tar.gz", hash = "sha256:8e281bbaea30e5658895b8bea21cc50d27aaf3a43db3f2694409ca5701c56b0a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/faker/37.4.2/faker-37.4.2-py3-none-any.whl", hash = "sha256:b70ed1af57bfe988cbcd0afd95f4768c51eaf4e1ce8a30962e127ac5c139c93f" },
]

[[package]]
name = "fastecdsa"
version = "3.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/fastecdsa/3.0.1/fastecdsa-3.0.1.tar.gz", hash = "sha256:f4b4a50fd5e346c4949aba365c061c3f6b25eee7983c973e1d31de7672ba48ea" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/fastecdsa/3.0.1/fastecdsa-3.0.1-cp310-cp310-macosx_13_0_x86_64.whl", hash = "sha256:5793ac17891bc8c7002c8364b5df32ac17b486a68c6e32d941f3f0b3641b417b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/fastecdsa/3.0.1/fastecdsa-3.0.1-cp310-cp310-macosx_14_0_arm64.whl", hash = "sha256:e99b2695f3cbf363fa4fc12e036b5a2b50a01004b82972dce102bb73fa1833bd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/fastecdsa/3.0.1/fastecdsa-3.0.1-cp310-cp310-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:2c730285951bd3dce40ff6ba0527847864d4fc63145c8479d5245e5544de4ee8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/fastecdsa/3.0.1/fastecdsa-3.0.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bb11b805f0c9c4e6b8eb8766b7eeaf9b7e425ff2f17eed08a402568e0548734d" },
]

[[package]]
name = "filelock"
version = "3.18.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/filelock/3.18.0/filelock-3.18.0.tar.gz", hash = "sha256:adbc88eabb99d2fec8c9c1b229b171f18afa655400173ddc653d5d01501fb9f2" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/filelock/3.18.0/filelock-3.18.0-py3-none-any.whl", hash = "sha256:c401f4f8377c4464e6db25fff06205fd89bdd83b65eb0488ed1b160f780e21de" },
]

[[package]]
name = "flake8"
version = "3.8.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "mccabe" },
    { name = "pycodestyle" },
    { name = "pyflakes" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/flake8/3.8.3/flake8-3.8.3.tar.gz", hash = "sha256:f04b9fcbac03b0a3e58c0ab3a0ecc462e023a9faf046d57794184028123aa208" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/flake8/3.8.3/flake8-3.8.3-py2.py3-none-any.whl", hash = "sha256:15e351d19611c887e482fb960eae4d44845013cc142d42896e9862f775d8cf5c" },
]

[[package]]
name = "frozenlist"
version = "1.7.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0.tar.gz", hash = "sha256:2e310d81923c2437ea8670467121cc3e9b0f76d3043cc1d2331d56c7fb7a3a8f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:cc4df77d638aa2ed703b878dd093725b72a824c3c546c076e8fdf276f78ee84a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:716a9973a2cc963160394f701964fe25012600f3d311f60c790400b00e568b61" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:a0fd1bad056a3600047fb9462cff4c5322cebc59ebf5d0a3725e0ee78955001d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:3789ebc19cb811163e70fe2bd354cea097254ce6e707ae42e56f45e31e96cb8e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:af369aa35ee34f132fcfad5be45fbfcde0e3a5f6a1ec0712857f286b7d20cca9" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:ac64b6478722eeb7a3313d494f8342ef3478dff539d17002f849101b212ef97c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f89f65d85774f1797239693cef07ad4c97fdd0639544bad9ac4b869782eb1981" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1073557c941395fdfcfac13eb2456cb8aad89f9de27bae29fabca8e563b12615" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1ed8d2fa095aae4bdc7fdd80351009a48d286635edffee66bf865e37a9125c50" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:24c34bea555fe42d9f928ba0a740c553088500377448febecaa82cc3e88aa1fa" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-musllinux_1_2_armv7l.whl", hash = "sha256:69cac419ac6a6baad202c85aaf467b65ac860ac2e7f2ac1686dc40dbb52f6577" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:960d67d0611f4c87da7e2ae2eacf7ea81a5be967861e0c63cf205215afbfac59" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:41be2964bd4b15bf575e5daee5a5ce7ed3115320fb3c2b71fca05582ffa4dc9e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:46d84d49e00c9429238a7ce02dc0be8f6d7cd0cd405abd1bebdc991bf27c15bd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:15900082e886edb37480335d9d518cec978afc69ccbc30bd18610b7c1b22a718" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-win32.whl", hash = "sha256:400ddd24ab4e55014bba442d917203c73b2846391dd42ca5e38ff52bb18c3c5e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-cp310-cp310-win_amd64.whl", hash = "sha256:6eb93efb8101ef39d32d50bce242c84bcbddb4f7e9febfa7b524532a239b4464" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/frozenlist/1.7.0/frozenlist-1.7.0-py3-none-any.whl", hash = "sha256:9a5af342e34f7e97caf8c995864c7a396418ae2859cc6fdf1b1073020d516a7e" },
]

[[package]]
name = "gate-api"
version = "4.70.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "python-dateutil" },
    { name = "six" },
    { name = "urllib3" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/gate-api/4.70.0/gate_api-4.70.0.tar.gz", hash = "sha256:21a17c9316ccb1370f458e20856d2668bd52091b890cfba2214ad9ccb1afca8a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gate-api/4.70.0/gate_api-4.70.0-py3-none-any.whl", hash = "sha256:1d57ec36863d910222c294c897588e23c71af10064e63047fefa1de0cb1a293d" },
]

[[package]]
name = "geoip2"
version = "4.6.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aiohttp" },
    { name = "maxminddb" },
    { name = "requests" },
    { name = "urllib3" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/geoip2/4.6.0/geoip2-4.6.0.tar.gz", hash = "sha256:f0e80bce80b06bb38bd08bf4877d5a84e354e932095e6ccfb4d27bb598fa4f83" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/geoip2/4.6.0/geoip2-4.6.0-py2.py3-none-any.whl", hash = "sha256:745e2d7a665072690056f9566774727da89216c7858793f89804f6945f969714" },
]

[[package]]
name = "gevent"
version = "24.10.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cffi", marker = "platform_python_implementation == 'CPython' and sys_platform == 'win32'" },
    { name = "greenlet", marker = "platform_python_implementation == 'CPython'" },
    { name = "zope-event" },
    { name = "zope-interface" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2.tar.gz", hash = "sha256:96e7bab9de56e0aca3858b8bc9c71f4eb0c0e12b7cf3cbfd170b62ce68cf71d7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-macosx_11_0_universal2.whl", hash = "sha256:562b66d8b061b9cfae1bc704b0cd5d2b255628d86c3639ddc16e4ffa3ebf6e7a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:44dd79cfefea24f9bb630844a25047c3807e02722436e826ef2aed3d646190c1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:fa190663f964583c8dbbab06bc863966e6f7eceaac8aa67c3ac0fae0a0a73b80" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:aabffb8b86fb95cb1ee5dffa315c9bd68fe20a7fe7260c0328679723b0257b7c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-manylinux_2_28_x86_64.whl", hash = "sha256:a6a04df4732bb7fdf9969ddee9a16a829e7971692fefdcb5baca760976d23e04" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:103097b39764a0a02f1a051225ea6b4c64a53dd37603424ca8a1e09be63a460b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:539af6b66c6b9faca2cdd903f0a7564c85053f1faf95e9a37702df578ac37085" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-cp310-cp310-win_amd64.whl", hash = "sha256:dd9c966e5fd8d7b0a54a130c5ad38ef581fd93ff4c44b6e73767519860da6ebe" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gevent/24.10.2/gevent-24.10.2-pp310-pypy310_pp73-macosx_11_0_universal2.whl", hash = "sha256:421cfeacae2555b11318c6ee11f34bc0a9517657068d8911c916d55a85362ce2" },
]

[[package]]
name = "gitdb"
version = "4.0.12"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "smmap" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/gitdb/4.0.12/gitdb-4.0.12.tar.gz", hash = "sha256:5ef71f855d191a3326fcfbc0d5da835f26b13fbcba60c32c21091c349ffdb571" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gitdb/4.0.12/gitdb-4.0.12-py3-none-any.whl", hash = "sha256:67073e15955400952c6565cc3e707c554a4eea2e428946f7a4c162fab9bd9bcf" },
]

[[package]]
name = "gitpython"
version = "3.1.24"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "gitdb" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/gitpython/3.1.24/GitPython-3.1.24.tar.gz", hash = "sha256:df83fdf5e684fef7c6ee2c02fc68a5ceb7e7e759d08b694088d0cacb4eba59e5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/gitpython/3.1.24/GitPython-3.1.24-py3-none-any.whl", hash = "sha256:dc0a7f2f697657acc8d7f89033e8b1ea94dd90356b2983bca89dc8d2ab3cc647" },
]

[[package]]
name = "googleapis-common-protos"
version = "1.69.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "protobuf" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/googleapis-common-protos/1.69.1/googleapis_common_protos-1.69.1.tar.gz", hash = "sha256:e20d2d8dda87da6fe7340afbbdf4f0bcb4c8fae7e6cadf55926c31f946b0b9b1" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/googleapis-common-protos/1.69.1/googleapis_common_protos-1.69.1-py2.py3-none-any.whl", hash = "sha256:4077f27a6900d5946ee5a369fab9c8ded4c0ef1c6e880458ea2f70c14f7b70d5" },
]

[[package]]
name = "graphene"
version = "3.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aniso8601" },
    { name = "graphql-core" },
    { name = "graphql-relay" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphene/3.3/graphene-3.3.tar.gz", hash = "sha256:529bf40c2a698954217d3713c6041d69d3f719ad0080857d7ee31327112446b0" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphene/3.3/graphene-3.3-py2.py3-none-any.whl", hash = "sha256:bb3810be33b54cb3e6969506671eb72319e8d7ba0d5ca9c8066472f75bf35a38" },
]

[[package]]
name = "graphene-django"
version = "3.2.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "django" },
    { name = "graphene" },
    { name = "graphql-core" },
    { name = "graphql-relay" },
    { name = "promise" },
    { name = "text-unidecode" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphene-django/3.2.2/graphene-django-3.2.2.tar.gz", hash = "sha256:059ccf25d9a5159f28d7ebf1a648c993ab34deb064e80b70ca096aa22a609556" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphene-django/3.2.2/graphene_django-3.2.2-py2.py3-none-any.whl", hash = "sha256:0fd95c8c1cbe77ae2a5940045ce276803c3acbf200a156731e0c730f2776ae2c" },
]

[[package]]
name = "graphql-core"
version = "3.2.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphql-core/3.2.3/graphql-core-3.2.3.tar.gz", hash = "sha256:06d2aad0ac723e35b1cb47885d3e5c45e956a53bc1b209a9fc5369007fe46676" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphql-core/3.2.3/graphql_core-3.2.3-py3-none-any.whl", hash = "sha256:5766780452bd5ec8ba133f8bf287dc92713e3868ddd83aee4faab9fc3e303dc3" },
]

[[package]]
name = "graphql-relay"
version = "3.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "graphql-core" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphql-relay/3.2.0/graphql-relay-3.2.0.tar.gz", hash = "sha256:1ff1c51298356e481a0be009ccdff249832ce53f30559c1338f22a0e0d17250c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/graphql-relay/3.2.0/graphql_relay-3.2.0-py3-none-any.whl", hash = "sha256:c9b22bd28b170ba1fe674c74384a8ff30a76c8e26f88ac3aa1584dd3179953e5" },
]

[[package]]
name = "greenlet"
version = "3.2.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3.tar.gz", hash = "sha256:8b0dd8ae4c0d6f5e54ee55ba935eeb3d735a9b58a8a1e5b5cbab64e01a39f365" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-macosx_11_0_universal2.whl", hash = "sha256:1afd685acd5597349ee6d7a88a8bec83ce13c106ac78c196ee9dde7c04fe87be" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:761917cac215c61e9dc7324b2606107b3b292a8349bdebb31503ab4de3f559ac" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_ppc64le.manylinux_2_17_ppc64le.whl", hash = "sha256:a433dbc54e4a37e4fff90ef34f25a8c00aed99b06856f0119dcf09fbafa16392" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_s390x.manylinux_2_17_s390x.whl", hash = "sha256:72e77ed69312bab0434d7292316d5afd6896192ac4327d44f3d613ecb85b037c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:68671180e3849b963649254a882cd544a3c75bfcd2c527346ad8bb53494444db" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:49c8cfb18fb419b3d08e011228ef8a25882397f3a859b9fe1436946140b6756b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:efc6dc8a792243c31f2f5674b670b3a95d46fa1c6a912b8e310d6f542e7b0712" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:731e154aba8e757aedd0781d4b240f1225b075b4409f1bb83b05ff410582cf00" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-win_amd64.whl", hash = "sha256:96c20252c2f792defe9a115d3287e14811036d51e78b3aaddbee23b69b216302" },
]

[[package]]
name = "groestlcoin-hash"
version = "1.0.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/groestlcoin-hash/1.0.3/groestlcoin_hash-1.0.3.tar.gz", hash = "sha256:31a8f6fa4c19db5258c3c73c071b71702102c815ba862b6015d9e4b75ece231e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/groestlcoin-hash/1.0.3/groestlcoin_hash-1.0.3-cp310-cp310-win32.whl", hash = "sha256:6b92ab7e2d86b476c3e9e6a2c450d37b41700e0ab3ac3099f2c134b8e659ae48" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/groestlcoin-hash/1.0.3/groestlcoin_hash-1.0.3-cp310-cp310-win_amd64.whl", hash = "sha256:b7be884a13195682e3e6634a6d47ed53bc68a4794764248f2b514d3a266e8343" },
]

[[package]]
name = "grpcio"
version = "1.71.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0.tar.gz", hash = "sha256:2b85f7820475ad3edec209d3d89a7909ada16caab05d3f2e08a7e8ae3200a55c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-linux_armv7l.whl", hash = "sha256:c200cb6f2393468142eb50ab19613229dcc7829b5ccee8b658a36005f6669fdd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-macosx_12_0_universal2.whl", hash = "sha256:b2266862c5ad664a380fbbcdbdb8289d71464c42a8c29053820ee78ba0119e5d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-manylinux_2_17_aarch64.whl", hash = "sha256:0ab8b2864396663a5b0b0d6d79495657ae85fa37dcb6498a2669d067c65c11ea" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:c30f393f9d5ff00a71bb56de4aa75b8fe91b161aeb61d39528db6b768d7eac69" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f250ff44843d9a0615e350c77f890082102a0318d66a99540f54769c8766ab73" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:e6d8de076528f7c43a2f576bc311799f89d795aa6c9b637377cc2b1616473804" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-musllinux_1_1_i686.whl", hash = "sha256:9b91879d6da1605811ebc60d21ab6a7e4bae6c35f6b63a061d61eb818c8168f6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:f71574afdf944e6652203cd1badcda195b2a27d9c83e6d88dc1ce3cfb73b31a5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-win32.whl", hash = "sha256:8997d6785e93308f277884ee6899ba63baafa0dfb4729748200fcc537858a509" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-win_amd64.whl", hash = "sha256:7d6ac9481d9d0d129224f6d5934d5832c4b1cddb96b59e7eba8416868909786a" },
]

[[package]]
name = "grpclib"
version = "0.4.8"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "h2" },
    { name = "multidict" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpclib/0.4.8/grpclib-0.4.8.tar.gz", hash = "sha256:d8823763780ef94fed8b2c562f7485cf0bbee15fc7d065a640673667f7719c9a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/grpclib/0.4.8/grpclib-0.4.8-py3-none-any.whl", hash = "sha256:a5047733a7acc1c1cee6abf3c841c7c6fab67d2844a45a853b113fa2e6cd2654" },
]

[[package]]
name = "h11"
version = "0.14.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/h11/0.14.0/h11-0.14.0.tar.gz", hash = "sha256:8f19fbbe99e72420ff35c00b27a34cb9937e902a8b810e2c88300c6f0a3b699d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/h11/0.14.0/h11-0.14.0-py3-none-any.whl", hash = "sha256:e3fe4ac4b851c468cc8363d500db52c2ead036020723024a109d37346efaa761" },
]

[[package]]
name = "h2"
version = "4.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "hpack" },
    { name = "hyperframe" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/h2/4.2.0/h2-4.2.0.tar.gz", hash = "sha256:c8a52129695e88b1a0578d8d2cc6842bbd79128ac685463b887ee278126ad01f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/h2/4.2.0/h2-4.2.0-py3-none-any.whl", hash = "sha256:479a53ad425bb29af087f3458a61d30780bc818e4ebcf01f0b536ba916462ed0" },
]

[[package]]
name = "hashids"
version = "1.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/hashids/1.3.1/hashids-1.3.1.tar.gz", hash = "sha256:6c3dc775e65efc2ce2c157a65acb776d634cb814598f406469abef00ae3f635c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hashids/1.3.1/hashids-1.3.1-py2.py3-none-any.whl", hash = "sha256:8bddd1acba501bfc9306e7e5a99a1667f4f2cacdc20cbd70bcc5ddfa5147c94c" },
]

[[package]]
name = "hexbytes"
version = "0.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/hexbytes/0.3.1/hexbytes-0.3.1.tar.gz", hash = "sha256:a3fe35c6831ee8fafd048c4c086b986075fc14fd46258fa24ecb8d65745f9a9d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hexbytes/0.3.1/hexbytes-0.3.1-py3-none-any.whl", hash = "sha256:383595ad75026cf00abd570f44b368c6cdac0c6becfae5c39ff88829877f8a59" },
]

[[package]]
name = "hiredis"
version = "3.2.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1.tar.gz", hash = "sha256:5a5f64479bf04dd829fe7029fad0ea043eac4023abc6e946668cbbec3493a78d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-macosx_10_15_universal2.whl", hash = "sha256:add17efcbae46c5a6a13b244ff0b4a8fa079602ceb62290095c941b42e9d5dec" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-macosx_10_15_x86_64.whl", hash = "sha256:5fe955cc4f66c57df1ae8e5caf4de2925d43b5efab4e40859662311d1bcc5f54" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:f9ad63cd9065820a43fb1efb8ed5ae85bb78f03ef5eb53f6bde47914708f5718" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d8e7f9e5fdba08841d78d4e1450cae03a4dbed2eda8a4084673cafa5615ce24a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1dce2508eca5d4e47ef38bc7c0724cb45abcdb0089f95a2ef49baf52882979a8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:186428bf353e4819abae15aa2ad64c3f40499d596ede280fe328abb9e98e72ce" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:74f2500d90a0494843aba7abcdc3e77f859c502e0892112d708c02e1dcae8f90" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:32822a94d2fdd1da96c05b22fdeef6d145d8fdbd865ba2f273f45eb949e4a805" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:ead809fb08dd4fdb5b4b6e2999c834e78c3b0c450a07c3ed88983964432d0c64" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:b90fada20301c3a257e868dd6a4694febc089b2b6d893fa96a3fc6c1f9ab4340" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:6d8bff53f526da3d9db86c8668011e4f7ca2958ee3a46c648edab6fe2cd1e709" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:043d929ae262d03e1db0f08616e14504a9119c1ff3de13d66f857d85cd45caff" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:8d470fef39d02dbe5c541ec345cc4ffd7d2baec7d6e59c92bd9d9545dc221829" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-win32.whl", hash = "sha256:efa4c76c45cc8c42228c7989b279fa974580e053b5e6a4a834098b5324b9eafa" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-cp310-cp310-win_amd64.whl", hash = "sha256:cbac5ec3a620b095c46ef3a8f1f06da9c86c1cdc411d44a5f538876c39a2b321" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:73913d2fa379e722d17ba52f21ce12dd578140941a08efd73e73b6fab1dea4d8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:15a3dff3eca31ecbf3d7d6d104cf1b318dc2b013bad3f4bdb2839cb9ea2e1584" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c78258032c2f9fc6f39fee7b07882ce26de281e09178266ce535992572132d95" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-pp310-pypy310_pp73-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:578d6a881e64e46db065256355594e680202c3bacf3270be3140057171d2c23e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8b7f34b170093c077c972b8cc0ceb15d8ff88ad0079751a8ae9733e94d77e733" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hiredis/3.2.1/hiredis-3.2.1-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:291a18b228fc90f6720d178de2fac46522082c96330b4cc2d3dd8cb2c1cb2815" },
]

[[package]]
name = "hpack"
version = "4.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/hpack/4.1.0/hpack-4.1.0.tar.gz", hash = "sha256:ec5eca154f7056aa06f196a557655c5b009b382873ac8d1e66e79e87535f1dca" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hpack/4.1.0/hpack-4.1.0-py3-none-any.whl", hash = "sha256:157ac792668d995c657d93111f46b4535ed114f0c9c8d672271bbec7eae1b496" },
]

[[package]]
name = "html5lib"
version = "1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "six" },
    { name = "webencodings" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/html5lib/1.1/html5lib-1.1.tar.gz", hash = "sha256:b2e5b40261e20f354d198eae92afc10d750afb487ed5e50f9c4eaf07c184146f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/html5lib/1.1/html5lib-1.1-py2.py3-none-any.whl", hash = "sha256:0d78f8fde1c230e99fe37986a60526d7049ed4bf8a9fadbad5f00e22e58e041d" },
]

[[package]]
name = "httpcore"
version = "0.17.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "anyio" },
    { name = "certifi" },
    { name = "h11" },
    { name = "sniffio" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/httpcore/0.17.3/httpcore-0.17.3.tar.gz", hash = "sha256:a6f30213335e34c1ade7be6ec7c47f19f50c56db36abef1a9dfa3815b1cb3888" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/httpcore/0.17.3/httpcore-0.17.3-py3-none-any.whl", hash = "sha256:c2789b767ddddfa2a5782e3199b2b7f6894540b17b16ec26b2c4d8e103510b87" },
]

[[package]]
name = "httpx"
version = "0.24.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "httpcore" },
    { name = "idna" },
    { name = "sniffio" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/httpx/0.24.1/httpx-0.24.1.tar.gz", hash = "sha256:5853a43053df830c20f8110c5e69fe44d035d850b2dfe795e196f00fdb774bdd" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/httpx/0.24.1/httpx-0.24.1-py3-none-any.whl", hash = "sha256:06781eb9ac53cde990577af654bd990a4949de37a28bdb4a230d434f3a30b9bd" },
]

[package.optional-dependencies]
http2 = [
    { name = "h2" },
]

[[package]]
name = "hyperframe"
version = "6.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/hyperframe/6.1.0/hyperframe-6.1.0.tar.gz", hash = "sha256:f630908a00854a7adeabd6382b43923a4c4cd4b821fcb527e6ab9e15382a3b08" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hyperframe/6.1.0/hyperframe-6.1.0-py3-none-any.whl", hash = "sha256:b03380493a519fce58ea5af42e4a42317bf9bd425596f7a0835ffce80f1a42e5" },
]

[[package]]
name = "hyperlink"
version = "21.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "idna" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/hyperlink/21.0.0/hyperlink-21.0.0.tar.gz", hash = "sha256:427af957daa58bc909471c6c40f74c5450fa123dd093fc53efd2e91d2705a56b" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/hyperlink/21.0.0/hyperlink-21.0.0-py2.py3-none-any.whl", hash = "sha256:e6b14c37ecb73e89c77d78cdb4c2cc8f3fb59a885c5b3f819ff4ed80f25af1b4" },
]

[[package]]
name = "identify"
version = "2.6.12"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/identify/2.6.12/identify-2.6.12.tar.gz", hash = "sha256:d8de45749f1efb108badef65ee8386f0f7bb19a7f26185f74de6367bffbaf0e6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/identify/2.6.12/identify-2.6.12-py2.py3-none-any.whl", hash = "sha256:ad9672d5a72e0d2ff7c5c8809b62dfa60458626352fb0eb7b55e69bdc45334a2" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/idna/3.10/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/idna/3.10/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3" },
]

[[package]]
name = "incremental"
version = "24.7.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "setuptools" },
    { name = "tomli" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/incremental/24.7.2/incremental-24.7.2.tar.gz", hash = "sha256:fb4f1d47ee60efe87d4f6f0ebb5f70b9760db2b2574c59c8e8912be4ebd464c9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/incremental/24.7.2/incremental-24.7.2-py3-none-any.whl", hash = "sha256:8cb2c3431530bec48ad70513931a760f446ad6c25e8333ca5d95e24b0ed7b8fe" },
]

[[package]]
name = "inflection"
version = "0.5.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/inflection/0.5.1/inflection-0.5.1.tar.gz", hash = "sha256:1a29730d366e996aaacffb2f1f1cb9593dc38e2ddd30c91250c6dde09ea9b417" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/inflection/0.5.1/inflection-0.5.1-py2.py3-none-any.whl", hash = "sha256:f38b2b640938a4f35ade69ac3d053042959b62a0f1076a5bbaa1b9526605a8a2" },
]

[[package]]
name = "influxdb"
version = "5.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "msgpack" },
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "requests" },
    { name = "six" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/influxdb/5.3.1/influxdb-5.3.1.tar.gz", hash = "sha256:46f85e7b04ee4b3dee894672be6a295c94709003a7ddea8820deec2ac4d8b27a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/influxdb/5.3.1/influxdb-5.3.1-py2.py3-none-any.whl", hash = "sha256:65040a1f53d1a2a4f88a677e89e3a98189a7d30cf2ab61c318aaa89733280747" },
]

[[package]]
name = "iniconfig"
version = "2.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/iniconfig/2.1.0/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/iniconfig/2.1.0/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760" },
]

[[package]]
name = "ipython"
version = "8.37.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "decorator" },
    { name = "exceptiongroup" },
    { name = "jedi" },
    { name = "matplotlib-inline" },
    { name = "pexpect", marker = "sys_platform != 'emscripten' and sys_platform != 'win32'" },
    { name = "prompt-toolkit" },
    { name = "pygments" },
    { name = "stack-data" },
    { name = "traitlets" },
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ipython/8.37.0/ipython-8.37.0.tar.gz", hash = "sha256:ca815841e1a41a1e6b73a0b08f3038af9b2252564d01fc405356d34033012216" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ipython/8.37.0/ipython-8.37.0-py3-none-any.whl", hash = "sha256:ed87326596b878932dbcb171e3e698845434d8c61b8d8cd474bf663041a9dcf2" },
]

[[package]]
name = "iso8601"
version = "1.0.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/iso8601/1.0.2/iso8601-1.0.2.tar.gz", hash = "sha256:27f503220e6845d9db954fb212b95b0362d8b7e6c1b2326a87061c3de93594b1" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/iso8601/1.0.2/iso8601-1.0.2-py3-none-any.whl", hash = "sha256:d7bc01b1c2a43b259570bb307f057abc578786ea734ba2b87b836c5efc5bd443" },
]

[[package]]
name = "isort"
version = "5.1.4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/isort/5.1.4/isort-5.1.4.tar.gz", hash = "sha256:145072eedc4927cc9c1f9478f2d83b2fc1e6469df4129c02ef4e8c742207a46c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/isort/5.1.4/isort-5.1.4-py3-none-any.whl", hash = "sha256:ae3007f72a2e9da36febd3454d8be4b175d6ca17eb765841d5fe3d038aede79d" },
]

[[package]]
name = "jedi"
version = "0.19.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "parso" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/jedi/0.19.2/jedi-0.19.2.tar.gz", hash = "sha256:4770dc3de41bde3966b02eb84fbcf557fb33cce26ad23da12c742fb50ecb11f0" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/jedi/0.19.2/jedi-0.19.2-py2.py3-none-any.whl", hash = "sha256:a8ef22bde8490f57fe5c7681a3c83cb58874daf72b4784de3cce5b6ef6edb5b9" },
]

[[package]]
name = "jinja2"
version = "3.1.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "markupsafe" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/jinja2/3.1.6/jinja2-3.1.6.tar.gz", hash = "sha256:0137fb05990d35f1275a587e9aee6d56da821fc83491a0fb838183be43f66d6d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/jinja2/3.1.6/jinja2-3.1.6-py3-none-any.whl", hash = "sha256:85ece4451f492d0c13c5dd7c13a64681a86afae63a5f347908daf103ce6d2f67" },
]

[[package]]
name = "jmespath"
version = "1.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/jmespath/1.0.1/jmespath-1.0.1.tar.gz", hash = "sha256:90261b206d6defd58fdd5e85f478bf633a2901798906be2ad389150c5c60edbe" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/jmespath/1.0.1/jmespath-1.0.1-py3-none-any.whl", hash = "sha256:02e2e4cc71b5bcab88332eebf907519190dd9e6e82107fa7f83b1003a6252980" },
]

[[package]]
name = "jsonalias"
version = "0.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/jsonalias/0.1.1/jsonalias-0.1.1.tar.gz", hash = "sha256:64f04d935397d579fc94509e1fcb6212f2d081235d9d6395bd10baedf760a769" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/jsonalias/0.1.1/jsonalias-0.1.1-py3-none-any.whl", hash = "sha256:a56d2888e6397812c606156504e861e8ec00e188005af149f003c787db3d3f18" },
]

[[package]]
name = "jsonschema"
version = "4.25.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "attrs" },
    { name = "jsonschema-specifications" },
    { name = "referencing" },
    { name = "rpds-py" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/jsonschema/4.25.0/jsonschema-4.25.0.tar.gz", hash = "sha256:e63acf5c11762c0e6672ffb61482bdf57f0876684d8d249c0fe2d730d48bc55f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/jsonschema/4.25.0/jsonschema-4.25.0-py3-none-any.whl", hash = "sha256:24c2e8da302de79c8b9382fee3e76b355e44d2a4364bb207159ce10b517bd716" },
]

[[package]]
name = "jsonschema-specifications"
version = "2025.4.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "referencing" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/jsonschema-specifications/2025.4.1/jsonschema_specifications-2025.4.1.tar.gz", hash = "sha256:630159c9f4dbea161a6a2205c3011cc4f18ff381b189fff48bb39b9bf26ae608" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/jsonschema-specifications/2025.4.1/jsonschema_specifications-2025.4.1-py3-none-any.whl", hash = "sha256:4653bffbd6584f7de83a67e0d620ef16900b390ddc7939d56684d6c81e33f1af" },
]

[[package]]
name = "kombu"
version = "5.5.4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "amqp" },
    { name = "packaging" },
    { name = "tzdata" },
    { name = "vine" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/kombu/5.5.4/kombu-5.5.4.tar.gz", hash = "sha256:886600168275ebeada93b888e831352fe578168342f0d1d5833d88ba0d847363" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/kombu/5.5.4/kombu-5.5.4-py3-none-any.whl", hash = "sha256:a12ed0557c238897d8e518f1d1fdf84bd1516c5e305af2dacd85c2015115feb8" },
]

[[package]]
name = "lru-dict"
version = "1.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru-dict-1.2.0.tar.gz", hash = "sha256:13c56782f19d68ddf4d8db0170041192859616514c706b126d0df2ec72a11bd7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:de906e5486b5c053d15b7731583c25e3c9147c288ac8152a6d1f9bccdec72641" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:604d07c7604b20b3130405d137cae61579578b0e8377daae4125098feebcb970" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:203b3e78d03d88f491fa134f85a42919020686b6e6f2d09759b2f5517260c651" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:020b93870f8c7195774cbd94f033b96c14f51c57537969965c3af300331724fe" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1184d91cfebd5d1e659d47f17a60185bbf621635ca56dcdc46c6a1745d25df5c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:fc42882b554a86e564e0b662da47b8a4b32fa966920bd165e27bb8079a323bc1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-musllinux_1_1_i686.whl", hash = "sha256:18ee88ada65bd2ffd483023be0fa1c0a6a051ef666d1cd89e921dcce134149f2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-musllinux_1_1_ppc64le.whl", hash = "sha256:756230c22257597b7557eaef7f90484c489e9ba78e5bb6ab5a5bcfb6b03cb075" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:c4da599af36618881748b5db457d937955bb2b4800db891647d46767d636c408" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-win32.whl", hash = "sha256:35a142a7d1a4fd5d5799cc4f8ab2fff50a598d8cee1d1c611f50722b3e27874f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lru-dict/1.2.0/lru_dict-1.2.0-cp310-cp310-win_amd64.whl", hash = "sha256:6da5b8099766c4da3bf1ed6e7d7f5eff1681aff6b5987d1258a13bd2ed54f0c9" },
]

[[package]]
name = "lxml"
version = "6.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0.tar.gz", hash = "sha256:032e65120339d44cdc3efc326c9f660f5f7205f3a535c1fdbf898b29ea01fb72" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:35bc626eec405f745199200ccb5c6b36f202675d204aa29bb52e27ba2b71dea8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:246b40f8a4aec341cbbf52617cad8ab7c888d944bfe12a6abd2b1f6cfb6f6082" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-manylinux2010_i686.manylinux2014_i686.manylinux_2_12_i686.manylinux_2_17_i686.whl", hash = "sha256:2793a627e95d119e9f1e19720730472f5543a6d84c50ea33313ce328d870f2dd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:46b9ed911f36bfeb6338e0b482e7fe7c27d362c52fde29f221fddbc9ee2227e7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:2b4790b558bee331a933e08883c423f65bbcd07e278f91b2272489e31ab1e2b4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:e2030956cf4886b10be9a0285c6802e078ec2391e1dd7ff3eb509c2c95a69b76" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:4d23854ecf381ab1facc8f353dcd9adeddef3652268ee75297c1164c987c11dc" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-manylinux_2_31_armv7l.whl", hash = "sha256:43fe5af2d590bf4691531b1d9a2495d7aab2090547eaacd224a3afec95706d76" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:74e748012f8c19b47f7d6321ac929a9a94ee92ef12bc4298c47e8b7219b26541" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-musllinux_1_2_armv7l.whl", hash = "sha256:43cfbb7db02b30ad3926e8fceaef260ba2fb7df787e38fa2df890c1ca7966c3b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:34190a1ec4f1e84af256495436b2d196529c3f2094f0af80202947567fdbf2e7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-win32.whl", hash = "sha256:5967fe415b1920a3877a4195e9a2b779249630ee49ece22021c690320ff07452" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-win_amd64.whl", hash = "sha256:f3389924581d9a770c6caa4df4e74b606180869043b9073e2cec324bad6e306e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-cp310-cp310-win_arm64.whl", hash = "sha256:522fe7abb41309e9543b0d9b8b434f2b630c5fdaf6482bee642b34c8c70079c8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:dbdd7679a6f4f08152818043dbb39491d1af3332128b3752c3ec5cebc0011a72" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-pp310-pypy310_pp73-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:40442e2a4456e9910875ac12951476d36c0870dcb38a68719f8c4686609897c4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-pp310-pypy310_pp73-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:db0efd6bae1c4730b9c863fc4f5f3c0fa3e8f05cae2c44ae141cb9dfc7d091dc" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-pp310-pypy310_pp73-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:9ab542c91f5a47aaa58abdd8ea84b498e8e49fe4b883d67800017757a3eb78e8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-pp310-pypy310_pp73-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:013090383863b72c62a702d07678b658fa2567aa58d373d963cca245b017e065" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/lxml/6.0.0/lxml-6.0.0-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:c86df1c9af35d903d2b52d22ea3e66db8058d21dc0f59842ca5deb0595921141" },
]

[[package]]
name = "markupsafe"
version = "2.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1.tar.gz", hash = "sha256:594c67807fb16238b30c44bdf74f36c02cdf22d1c8cda91ef8a0ed8dabf5620a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:d8446c54dc28c01e5a2dbac5a25f071f6653e6e40f3a8818e8b45d790fe6ef53" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:36bc903cbb393720fad60fc28c10de6acf10dc6cc883f3e24ee4012371399a38" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2d7d807855b419fc2ed3e631034685db6079889a1f01d5d9dac950f764da3dad" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_12_i686.manylinux2010_i686.whl", hash = "sha256:add36cb2dbb8b736611303cd3bfcee00afd96471b09cda130da3581cbdc56a6d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl", hash = "sha256:168cd0a3642de83558a5153c8bd34f175a9a6e7f6dc6384b9655d2697312a646" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:4dc8f9fb58f7364b63fd9f85013b780ef83c11857ae79f2feda41e270468dd9b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-musllinux_1_1_i686.whl", hash = "sha256:20dca64a3ef2d6e4d5d615a3fd418ad3bde77a47ec8a23d984a12b5b4c74491a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:cdfba22ea2f0029c9261a4bd07e830a8da012291fbe44dc794e488b6c9bb353a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-win32.whl", hash = "sha256:99df47edb6bda1249d3e80fdabb1dab8c08ef3975f69aed437cb69d0a5de1e28" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/markupsafe/2.0.1/MarkupSafe-2.0.1-cp310-cp310-win_amd64.whl", hash = "sha256:e0f138900af21926a02425cf736db95be9f4af72ba1bb21453432a07f6082134" },
]

[[package]]
name = "matplotlib-inline"
version = "0.1.7"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "traitlets" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/matplotlib-inline/0.1.7/matplotlib_inline-0.1.7.tar.gz", hash = "sha256:8423b23ec666be3d16e16b60bdd8ac4e86e840ebd1dd11a30b9f117f2fa0ab90" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/matplotlib-inline/0.1.7/matplotlib_inline-0.1.7-py3-none-any.whl", hash = "sha256:df192d39a4ff8f21b1895d72e6a13f5fcc5099f00fa84384e0ea28c2cc0653ca" },
]

[[package]]
name = "maxminddb"
version = "2.7.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0.tar.gz", hash = "sha256:23a715ed3b3aed07adae4beeed06c51fd582137b5ae13d3c6e5ca4890f70ebbf" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:89b12a3af361f22d6e5a5949e8d224ff06dc5f5f49d9be85c3c99d95e33234ca" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:1015f866e7768fb3eb63e08f152494f5152d0ba50ef4d8332ccbaffeee7c2111" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:55eac03e6fcdec92a9f01e7812a1da6cc4b1fa94c8af714317dd21fcbefbb732" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:f630c7a540ed75f393cf76bf0702f4564040217adb1c777df40ef4d241587e04" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:4a94a96696c9d17d5c84d6f7b1bca4fbda4ab666563c4669c4ca483f4bcbb563" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:96717032bd15dcf2fd77da74d49ddf35847aae6cfd8cf3b2b1847ddb356e3e29" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:7f7c29ec728a82a0cd4d4ece6752fc3a8df2f3ff967ee35bdaf7a4a10b55016f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:2c9b5d8c433479b1d40fec45e87c22873e7d6d17310981fafcf5823759c83f0d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:ed1a8db359ad726e0cea25fd6e6f22245bfa6f5ab7bedb131f0bb18b01ed3474" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:2ae9aeeab1e1ed9a531ff1a78f7d873fe38614018223fe4b6bbde1a3a89c3f52" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:307b7080d123cfc0f90851fca127421b0222ca973bd8e878161449e4a1185ef3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:c86601ea1ea6d6075b45a5a95f888c39a17fa077590b812a39d74835930f6612" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-win32.whl", hash = "sha256:f1a4a533f8cf84f52ca3e2f07e0190daa6c6e22300f47631cb9c6d8cc2ac0325" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-cp310-cp310-win_amd64.whl", hash = "sha256:17662f4e63c269ae2a3fc74e4e93e8d99d3f4a1b080fb437107a4a57bccb1fe3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:d29236bc5349c54ab1ea121b707069c67cb4be2c1c25d5456bb3324e2a220987" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:6930d6ba283416fc50c145f9845ffd8d9d373325d2c8b7b691098ebd14c8679c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ca12a265926bd6784f8f44a881e347fad75c815d6cff43eab969719d3da2a34f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2a6156146ba2cd47d6864667f8d92e1370677593ec4d7843d5c3244aeac81b34" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:07aff6856646de82d4491788203af47827ced407ff9a37d38a736fe559ec88d8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/maxminddb/2.7.0/maxminddb-2.7.0-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:facccbb308af820d3d56f0c8edb84a4e8f525b7adda5e9bbf140cc30392a246c" },
]

[[package]]
name = "mccabe"
version = "0.6.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/mccabe/0.6.1/mccabe-0.6.1.tar.gz", hash = "sha256:dd8d182285a0fe56bace7f45b5e7d1a6ebcbf524e8f3bd87eb0f125271b8831f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/mccabe/0.6.1/mccabe-0.6.1-py2.py3-none-any.whl", hash = "sha256:ab8a6258860da4b6677da4bd2fe5dc2c659cff31b3ee4f7f5d64e79735b80d42" },
]

[[package]]
name = "mnemonic"
version = "0.20"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/mnemonic/0.20/mnemonic-0.20.tar.gz", hash = "sha256:7c6fb5639d779388027a77944680aee4870f0fcd09b1e42a5525ee2ce4c625f6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/mnemonic/0.20/mnemonic-0.20-py3-none-any.whl", hash = "sha256:acd2168872d0379e7a10873bb3e12bf6c91b35de758135c4fbd1015ef18fafc5" },
]

[[package]]
name = "mock"
version = "5.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/mock/5.1.0/mock-5.1.0.tar.gz", hash = "sha256:5e96aad5ccda4718e0a229ed94b2024df75cc2d55575ba5762d31f5767b8767d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/mock/5.1.0/mock-5.1.0-py3-none-any.whl", hash = "sha256:18c694e5ae8a208cdb3d2c20a993ca1a7b0efa258c247a1e565150f477f83744" },
]

[[package]]
name = "msgpack"
version = "1.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1.tar.gz", hash = "sha256:77b79ce34a2bdab2594f490c8e80dd62a02d650b91a75159a63ec413b8d104cd" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:353b6fc0c36fde68b661a12949d7d49f8f51ff5fa019c1e47c87c4ff34b080ed" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:79c408fcf76a958491b4e3b103d1c417044544b68e96d06432a189b43d1215c8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:78426096939c2c7482bf31ef15ca219a9e24460289c00dd0b94411040bb73ad2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8b17ba27727a36cb73aabacaa44b13090feb88a01d012c0f4be70c00f75048b4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:7a17ac1ea6ec3c7687d70201cfda3b1e8061466f28f686c24f627cae4ea8efd0" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:88d1e966c9235c1d4e2afac21ca83933ba59537e2e2727a999bf3f515ca2af26" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:f6d58656842e1b2ddbe07f43f56b10a60f2ba5826164910968f5933e5178af75" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:96decdfc4adcbc087f5ea7ebdcfd3dee9a13358cae6e81d54be962efc38f6338" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-win32.whl", hash = "sha256:6640fd979ca9a212e4bcdf6eb74051ade2c690b862b679bfcb60ae46e6dc4bfd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/msgpack/1.1.1/msgpack-1.1.1-cp310-cp310-win_amd64.whl", hash = "sha256:8b65b53204fe1bd037c40c4148d00ef918eb2108d24c9aaa20bc31f9810ce0a8" },
]

[[package]]
name = "multidict"
version = "6.6.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3.tar.gz", hash = "sha256:798a9eb12dab0a6c2e29c1de6f3468af5cb2da6053a20dfa3344907eed0937cc" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:a2be5b7b35271f7fff1397204ba6708365e3d773579fe2a30625e16c4b4ce817" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:12f4581d2930840295c461764b9a65732ec01250b46c6b2c510d7ee68872b140" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:dd7793bab517e706c9ed9d7310b06c8672fd0aeee5781bfad612f56b8e0f7d14" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-manylinux1_i686.manylinux2014_i686.manylinux_2_17_i686.manylinux_2_5_i686.whl", hash = "sha256:72d8815f2cd3cf3df0f83cac3f3ef801d908b2d90409ae28102e0553af85545a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:531e331a2ee53543ab32b16334e2deb26f4e6b9b28e41f8e0c87e99a6c8e2d69" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-manylinux2014_armv7l.manylinux_2_17_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:42ca5aa9329a63be8dc49040f63817d1ac980e02eeddba763a9ae5b4027b9c9c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl", hash = "sha256:208b9b9757060b9faa6f11ab4bc52846e4f3c2fb8b14d5680c8aac80af3dc751" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl", hash = "sha256:acf6b97bd0884891af6a8b43d0f586ab2fcf8e717cbd47ab4bdddc09e20652d8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:68e9e12ed00e2089725669bdc88602b0b6f8d23c0c95e52b95f0bc69f7fe9b55" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:05db2f66c9addb10cfa226e1acb363450fab2ff8a6df73c622fefe2f5af6d4e7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-musllinux_1_2_armv7l.whl", hash = "sha256:0db58da8eafb514db832a1b44f8fa7906fdd102f7d982025f816a93ba45e3dcb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:14117a41c8fdb3ee19c743b1c027da0736fdb79584d61a766da53d399b71176c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:877443eaaabcd0b74ff32ebeed6f6176c71850feb7d6a1d2db65945256ea535c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:70b72e749a4f6e7ed8fb334fa8d8496384840319512746a5f42fa0aec79f4d61" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:43571f785b86afd02b3855c5ac8e86ec921b760298d6f82ff2a61daf5a35330b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-win32.whl", hash = "sha256:20c5a0c3c13a15fd5ea86c42311859f970070e4e24de5a550e99d7c271d76318" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-win_amd64.whl", hash = "sha256:ab0a34a007704c625e25a9116c6770b4d3617a071c8a7c30cd338dfbadfe6485" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-cp310-cp310-win_arm64.whl", hash = "sha256:769841d70ca8bdd140a715746199fc6473414bd02efd678d75681d2d6a8986c5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/multidict/6.6.3/multidict-6.6.3-py3-none-any.whl", hash = "sha256:8db10f29c7541fc5da4defd8cd697e1ca429db743fa716325f236079b96f775a" },
]

[[package]]
name = "mypy-extensions"
version = "1.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/mypy-extensions/1.1.0/mypy_extensions-1.1.0.tar.gz", hash = "sha256:52e68efc3284861e772bbcd66823fde5ae21fd2fdb51c62a211403730b916558" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/mypy-extensions/1.1.0/mypy_extensions-1.1.0-py3-none-any.whl", hash = "sha256:1be4cccdb0f2482337c4743e60421de3a356cd97508abadd57d47403e94f5505" },
]

[[package]]
name = "mysqlclient"
version = "2.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/mysqlclient/2.2.0/mysqlclient-2.2.0.tar.gz", hash = "sha256:04368445f9c487d8abb7a878e3d23e923e6072c04a6c320f9e0dc8a82efba14e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/mysqlclient/2.2.0/mysqlclient-2.2.0-cp310-cp310-win_amd64.whl", hash = "sha256:68837b6bb23170acffb43ae411e47533a560b6360c06dac39aa55700972c93b2" },
]

[[package]]
name = "nodeenv"
version = "1.9.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/nodeenv/1.9.1/nodeenv-1.9.1.tar.gz", hash = "sha256:6ec12890a2dab7946721edbfbcd91f3319c6ccc9aec47be7c7e6b7011ee6645f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/nodeenv/1.9.1/nodeenv-1.9.1-py2.py3-none-any.whl", hash = "sha256:ba11c9782d29c27c70ffbdda2d7415098754709be8a7056d79a737cd901155c9" },
]

[[package]]
name = "numpy"
version = "2.2.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6.tar.gz", hash = "sha256:e29554e2bef54a90aa5cc07da6ce955accb83f21ab5de01a62c8478897b264fd" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:b412caa66f72040e6d268491a59f2c43bf03eb6c96dd8f0307829feb7fa2b6fb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:8e41fd67c52b86603a91c1a505ebaef50b3314de0213461c7a6e99c9a3beff90" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-macosx_14_0_arm64.whl", hash = "sha256:37e990a01ae6ec7fe7fa1c26c55ecb672dd98b19c3d0e1d1f326fa13cb38d163" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-macosx_14_0_x86_64.whl", hash = "sha256:5a6429d4be8ca66d889b7cf70f536a397dc45ba6faeb5f8c5427935d9592e9cf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:efd28d4e9cd7d7a8d39074a4d44c63eda73401580c5c76acda2ce969e0a38e83" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fc7b73d02efb0e18c000e9ad8b83480dfcd5dfd11065997ed4c6747470ae8915" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:74d4531beb257d2c3f4b261bfb0fc09e0f9ebb8842d82a7b4209415896adc680" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:8fc377d995680230e83241d8a96def29f204b5782f371c532579b4f20607a289" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-win32.whl", hash = "sha256:b093dd74e50a8cba3e873868d9e93a85b78e0daf2e98c6797566ad8044e8363d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-cp310-cp310-win_amd64.whl", hash = "sha256:f0fd6321b839904e15c46e0d257fdd101dd7f530fe03fd6359c1ea63738703f3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:0b605b275d7bd0c640cad4e5d30fa701a8d59302e127e5f79138ad62762c3e3d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-pp310-pypy310_pp73-macosx_14_0_x86_64.whl", hash = "sha256:7befc596a7dc9da8a337f79802ee8adb30a552a94f792b9c9d18c840055907db" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ce47521a4754c8f4593837384bd3424880629f718d87c5d44f8ed763edd63543" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/numpy/2.2.6/numpy-2.2.6-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:d042d24c90c41b54fd506da306759e06e568864df8ec17ccc17e9e884634fd00" },
]

[[package]]
name = "onfido-python"
version = "2.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "requests" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/onfido-python/2.1.0/onfido-python-2.1.0.tar.gz", hash = "sha256:971aa435b9ae1c0e9a108d55bcd321a55c4caa1b62187c286a18dcdf3ce6900f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/onfido-python/2.1.0/onfido_python-2.1.0-py3-none-any.whl", hash = "sha256:3b39f9871f4a12f6ab4bcf8b84cc09d952b5e3826482bfe74a75bbc8e34ebef3" },
]

[[package]]
name = "openpyxl"
version = "3.1.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "et-xmlfile" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/openpyxl/3.1.2/openpyxl-3.1.2.tar.gz", hash = "sha256:a6f5977418eff3b2d5500d54d9db50c8277a368436f4e4f8ddb1be3422870184" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/openpyxl/3.1.2/openpyxl-3.1.2-py2.py3-none-any.whl", hash = "sha256:f91456ead12ab3c6c2e9491cf33ba6d08357d802192379bb482f1033ade496f5" },
]

[[package]]
name = "oscrypto"
version = "1.3.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "asn1crypto" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/oscrypto/1.3.0/oscrypto-1.3.0.tar.gz", hash = "sha256:6f5fef59cb5b3708321db7cca56aed8ad7e662853351e7991fcf60ec606d47a4" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/oscrypto/1.3.0/oscrypto-1.3.0-py2.py3-none-any.whl", hash = "sha256:2b2f1d2d42ec152ca90ccb5682f3e051fb55986e1b170ebde472b133713e7085" },
]

[[package]]
name = "ossaudit"
version = "0.5.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "appdirs" },
    { name = "click" },
    { name = "dparse" },
    { name = "requests" },
    { name = "texttable" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ossaudit/0.5.0/ossaudit-0.5.0.tar.gz", hash = "sha256:fad61318b1866c92bd3cf7d0d6d5542687fe11b9c8e1f8ed5b2199b5eec3ef40" }

[[package]]
name = "packaging"
version = "23.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/packaging/23.2/packaging-23.2.tar.gz", hash = "sha256:048fb0e9405036518eaaf48a55953c750c11e1a1b68e0dd1a9d62ed0c092cfc5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/packaging/23.2/packaging-23.2-py3-none-any.whl", hash = "sha256:8c491190033a9af7e1d931d0b5dacc2ef47509b34dd0de67ed209b5203fc88c7" },
]

[[package]]
name = "parsimonious"
version = "0.10.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "regex" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/parsimonious/0.10.0/parsimonious-0.10.0.tar.gz", hash = "sha256:8281600da180ec8ae35427a4ab4f7b82bfec1e3d1e52f80cb60ea82b9512501c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/parsimonious/0.10.0/parsimonious-0.10.0-py3-none-any.whl", hash = "sha256:982ab435fabe86519b57f6b35610aa4e4e977e9f02a14353edf4bbc75369fc0f" },
]

[[package]]
name = "parso"
version = "0.8.4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/parso/0.8.4/parso-0.8.4.tar.gz", hash = "sha256:eb3a7b58240fb99099a345571deecc0f9540ea5f4dd2fe14c2a99d6b281ab92d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/parso/0.8.4/parso-0.8.4-py2.py3-none-any.whl", hash = "sha256:a418670a20291dacd2dddc80c377c5c3791378ee1e8d12bffc35420643d43f18" },
]

[[package]]
name = "pathspec"
version = "0.12.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pathspec/0.12.1/pathspec-0.12.1.tar.gz", hash = "sha256:a482d51503a1ab33b1c67a6c3813a26953dbdc71c31dacaef9a838c4e29f5712" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pathspec/0.12.1/pathspec-0.12.1-py3-none-any.whl", hash = "sha256:a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08" },
]

[[package]]
name = "pbr"
version = "6.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "setuptools" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pbr/6.1.1/pbr-6.1.1.tar.gz", hash = "sha256:93ea72ce6989eb2eed99d0f75721474f69ad88128afdef5ac377eb797c4bf76b" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pbr/6.1.1/pbr-6.1.1-py2.py3-none-any.whl", hash = "sha256:38d4daea5d9fa63b3f626131b9d34947fd0c8be9b05a29276870580050a25a76" },
]

[[package]]
name = "pep8"
version = "1.7.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pep8/1.7.1/pep8-1.7.1.tar.gz", hash = "sha256:fe249b52e20498e59e0b5c5256aa52ee99fc295b26ec9eaa85776ffdb9fe6374" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pep8/1.7.1/pep8-1.7.1-py2.py3-none-any.whl", hash = "sha256:b22cfae5db09833bb9bd7c8463b53e1a9c9b39f12e304a8d0bba729c501827ee" },
]

[[package]]
name = "pexpect"
version = "4.9.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "ptyprocess" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pexpect/4.9.0/pexpect-4.9.0.tar.gz", hash = "sha256:ee7d41123f3c9911050ea2c2dac107568dc43b2d3b0c7557a33212c398ead30f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pexpect/4.9.0/pexpect-4.9.0-py2.py3-none-any.whl", hash = "sha256:7236d1e080e4936be2dc3e326cec0af72acf9212a7e1d060210e70a47e253523" },
]

[[package]]
name = "phonenumbers"
version = "8.12.45"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/phonenumbers/8.12.45/phonenumbers-8.12.45.tar.gz", hash = "sha256:94e30f59b2be6c4310a90f3d5da53d49900bdb440484506f3333c694ebb0cdab" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/phonenumbers/8.12.45/phonenumbers-8.12.45-py2.py3-none-any.whl", hash = "sha256:e3af21c1e33a3dd063cddba3cad653abb8d23c37c62cedee597a3f3ea0f5365c" },
]

[[package]]
name = "pillow"
version = "9.3.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0.tar.gz", hash = "sha256:c935a22a557a560108d780f9a0fc426dd7459940dc54faa49d83249c8d3e760f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-macosx_10_10_x86_64.whl", hash = "sha256:0b7257127d646ff8676ec8a15520013a698d1fdc48bc2a79ba4e53df792526f2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:b90f7616ea170e92820775ed47e136208e04c967271c9ef615b6fbd08d9af0e3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:68943d632f1f9e3dce98908e873b3a090f6cba1cbb1b892a9e8d97c938871fbe" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:be55f8457cd1eac957af0c3f5ece7bc3f033f89b114ef30f710882717670b2a8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:5d77adcd56a42d00cc1be30843d3426aa4e660cab4a61021dc84467123f7a00c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-manylinux_2_28_aarch64.whl", hash = "sha256:829f97c8e258593b9daa80638aee3789b7df9da5cf1336035016d76f03b8860c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-manylinux_2_28_x86_64.whl", hash = "sha256:801ec82e4188e935c7f5e22e006d01611d6b41661bba9fe45b60e7ac1a8f84de" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:871b72c3643e516db4ecf20efe735deb27fe30ca17800e661d769faab45a18d7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-win32.whl", hash = "sha256:655a83b0058ba47c7c52e4e2df5ecf484c1b0b0349805896dd350cbc416bdd91" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pillow/9.3.0/Pillow-9.3.0-cp310-cp310-win_amd64.whl", hash = "sha256:9f47eabcd2ded7698106b05c2c338672d16a6f2a485e74481f524e2a23c2794b" },
]

[[package]]
name = "platformdirs"
version = "4.3.8"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/platformdirs/4.3.8/platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/platformdirs/4.3.8/platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4" },
]

[[package]]
name = "pluggy"
version = "1.6.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pluggy/1.6.0/pluggy-1.6.0.tar.gz", hash = "sha256:7dcc130b76258d33b90f61b658791dede3486c3e6bfb003ee5c9bfb396dd22f3" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pluggy/1.6.0/pluggy-1.6.0-py3-none-any.whl", hash = "sha256:e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746" },
]

[[package]]
name = "pre-commit"
version = "2.19.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cfgv" },
    { name = "identify" },
    { name = "nodeenv" },
    { name = "pyyaml" },
    { name = "toml" },
    { name = "virtualenv" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pre-commit/2.19.0/pre_commit-2.19.0.tar.gz", hash = "sha256:4233a1e38621c87d9dda9808c6606d7e7ba0e087cd56d3fe03202a01d2919615" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pre-commit/2.19.0/pre_commit-2.19.0-py2.py3-none-any.whl", hash = "sha256:10c62741aa5704faea2ad69cb550ca78082efe5697d6f04e5710c3c229afdd10" },
]

[[package]]
name = "prometheus-client"
version = "0.12.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/prometheus-client/0.12.0/prometheus_client-0.12.0.tar.gz", hash = "sha256:1b12ba48cee33b9b0b9de64a1047cbd3c5f2d0ab6ebcead7ddda613a750ec3c5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/prometheus-client/0.12.0/prometheus_client-0.12.0-py2.py3-none-any.whl", hash = "sha256:317453ebabff0a1b02df7f708efbab21e3489e7072b61cb6957230dd004a0af0" },
]

[[package]]
name = "promise"
version = "2.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/promise/2.3/promise-2.3.tar.gz", hash = "sha256:dfd18337c523ba4b6a58801c164c1904a9d4d1b1747c7d5dbf45b693a49d93d0" }

[[package]]
name = "prompt-toolkit"
version = "3.0.51"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "wcwidth" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/prompt-toolkit/3.0.51/prompt_toolkit-3.0.51.tar.gz", hash = "sha256:931a162e3b27fc90c86f1b48bb1fb2c528c2761475e57c9c06de13311c7b54ed" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/prompt-toolkit/3.0.51/prompt_toolkit-3.0.51-py3-none-any.whl", hash = "sha256:52742911fde84e2d423e2f9a4cf1de7d7ac4e51958f648d9540e0fb8db077b07" },
]

[[package]]
name = "protobuf"
version = "5.29.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/protobuf/5.29.3/protobuf-5.29.3.tar.gz", hash = "sha256:5da0f41edaf117bde316404bad1a486cb4ededf8e4a54891296f648e8e076620" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/protobuf/5.29.3/protobuf-5.29.3-cp310-abi3-win32.whl", hash = "sha256:3ea51771449e1035f26069c4c7fd51fba990d07bc55ba80701c78f886bf9c888" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/protobuf/5.29.3/protobuf-5.29.3-cp310-abi3-win_amd64.whl", hash = "sha256:a4fa6f80816a9a0678429e84973f2f98cbc218cca434abe8db2ad0bffc98503a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/protobuf/5.29.3/protobuf-5.29.3-cp38-abi3-macosx_10_9_universal2.whl", hash = "sha256:a8434404bbf139aa9e1300dbf989667a83d42ddda9153d8ab76e0d5dcaca484e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/protobuf/5.29.3/protobuf-5.29.3-cp38-abi3-manylinux2014_aarch64.whl", hash = "sha256:daaf63f70f25e8689c072cfad4334ca0ac1d1e05a92fc15c54eb9cf23c3efd84" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/protobuf/5.29.3/protobuf-5.29.3-cp38-abi3-manylinux2014_x86_64.whl", hash = "sha256:c027e08a08be10b67c06bf2370b99c811c466398c357e615ca88c91c07f0910f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/protobuf/5.29.3/protobuf-5.29.3-py3-none-any.whl", hash = "sha256:0a18ed4a24198528f2333802eb075e59dea9d679ab7a6c5efb017a59004d849f" },
]

[[package]]
name = "psutil"
version = "5.9.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/psutil/5.9.2/psutil-5.9.2.tar.gz", hash = "sha256:feb861a10b6c3bb00701063b37e4afc754f8217f0f09c42280586bd6ac712b5c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/psutil/5.9.2/psutil-5.9.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:614337922702e9be37a39954d67fdb9e855981624d8011a9927b8f2d3c9625d9" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/psutil/5.9.2/psutil-5.9.2-cp310-cp310-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:39ec06dc6c934fb53df10c1672e299145ce609ff0611b569e75a88f313634969" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/psutil/5.9.2/psutil-5.9.2-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e3ac2c0375ef498e74b9b4ec56df3c88be43fe56cac465627572dbfb21c4be34" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/psutil/5.9.2/psutil-5.9.2-cp310-cp310-win32.whl", hash = "sha256:e4c4a7636ffc47b7141864f1c5e7d649f42c54e49da2dd3cceb1c5f5d29bfc85" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/psutil/5.9.2/psutil-5.9.2-cp310-cp310-win_amd64.whl", hash = "sha256:f4cb67215c10d4657e320037109939b1c1d2fd70ca3d76301992f89fe2edb1f1" },
]

[[package]]
name = "ptyprocess"
version = "0.7.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ptyprocess/0.7.0/ptyprocess-0.7.0.tar.gz", hash = "sha256:5c5d0a3b48ceee0b48485e0c26037c0acd7d29765ca3fbb5cb3831d347423220" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ptyprocess/0.7.0/ptyprocess-0.7.0-py2.py3-none-any.whl", hash = "sha256:4b41f3967fce3af57cc7e94b888626c18bf37a083e3651ca8feeb66d492fef35" },
]

[[package]]
name = "pure-eval"
version = "0.2.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pure-eval/0.2.3/pure_eval-0.2.3.tar.gz", hash = "sha256:5f4e983f40564c576c7c8635ae88db5956bb2229d7e9237d03b3c0b0190eaf42" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pure-eval/0.2.3/pure_eval-0.2.3-py3-none-any.whl", hash = "sha256:1db8e35b67b3d218d818ae653e27f06c3aa420901fa7b081ca98cbedc874e0d0" },
]

[[package]]
name = "py-ecc"
version = "7.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cached-property" },
    { name = "eth-typing" },
    { name = "eth-utils" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/py-ecc/7.0.1/py_ecc-7.0.1.tar.gz", hash = "sha256:557461f42e57294d734305a30faf6b8903421651871e9cdeff8d8e67c6796c70" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/py-ecc/7.0.1/py_ecc-7.0.1-py3-none-any.whl", hash = "sha256:84a8b4d436163c83c65345a68e32f921ef6e64374a36f8e561f0455b4b08f5f2" },
]

[[package]]
name = "py-evm"
version = "0.11.0b1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cached-property" },
    { name = "ckzg" },
    { name = "eth-bloom" },
    { name = "eth-keys" },
    { name = "eth-typing" },
    { name = "eth-utils" },
    { name = "lru-dict" },
    { name = "py-ecc" },
    { name = "rlp" },
    { name = "trie" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/py-evm/0.11.0b1/py_evm-0.11.0b1.tar.gz", hash = "sha256:7d3259b2c2b7d933da35fe17c6da8fd08b9f2de72003d4c2ec5c63b41f84c338" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/py-evm/0.11.0b1/py_evm-0.11.0b1-py3-none-any.whl", hash = "sha256:05fdce3a79c63379305c3b572c318f2266d2fc1ae094be0ef024454ce24e1dc5" },
]

[[package]]
name = "py-solc-x"
version = "2.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "packaging" },
    { name = "requests" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/py-solc-x/2.0.0/py-solc-x-2.0.0.tar.gz", hash = "sha256:28a96082435dd2d6313d52028864afaded22dfec3ecb6df64c7c331912f26be7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/py-solc-x/2.0.0/py_solc_x-2.0.0-py3-none-any.whl", hash = "sha256:59a447057d382949e0264fd1e13333c180ac6eb7ba42e590056101fd55daa9dc" },
]

[[package]]
name = "pyasn1"
version = "0.6.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyasn1/0.6.1/pyasn1-0.6.1.tar.gz", hash = "sha256:6f580d2bdd84365380830acf45550f2511469f673cb4a5ae3857a3170128b034" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyasn1/0.6.1/pyasn1-0.6.1-py3-none-any.whl", hash = "sha256:0d632f46f2ba09143da3a8afe9e33fb6f92fa2320ab7e886e2d0f7672af84629" },
]

[[package]]
name = "pyasn1-modules"
version = "0.4.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pyasn1" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyasn1-modules/0.4.2/pyasn1_modules-0.4.2.tar.gz", hash = "sha256:677091de870a80aae844b1ca6134f54652fa2c8c5a52aa396440ac3106e941e6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyasn1-modules/0.4.2/pyasn1_modules-0.4.2-py3-none-any.whl", hash = "sha256:29253a9207ce32b64c3ac6600edc75368f98473906e8fd1043bd6b5b1de2c14a" },
]

[[package]]
name = "pycares"
version = "4.9.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0.tar.gz", hash = "sha256:8ee484ddb23dbec4d88d14ed5b6d592c1960d2e93c385d5e52b6fad564d82395" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:0b8bd9a3ee6e9bc990e1933dc7e7e2f44d4184f49a90fa444297ac12ab6c0c84" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:417a5c20861f35977240ad4961479a6778125bcac21eb2ad1c3aad47e2ff7fab" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ab290faa4ea53ce53e3ceea1b3a42822daffce2d260005533293a52525076750" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:7b1df81193084c9717734e4615e8c5074b9852478c9007d1a8bb242f7f580e67" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:20c7a6af0c2ccd17cc5a70d76e299a90e7ebd6c4d8a3d7fff5ae533339f61431" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:370f41442a5b034aebdb2719b04ee04d3e805454a20d3f64f688c1c49f9137c3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:340e4a3bbfd14d73c01ec0793a321b8a4a93f64c508225883291078b7ee17ac8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:f0ec94785856ea4f5556aa18f4c027361ba4b26cb36c4ad97d2105ef4eec68ba" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:dd6b7e23a4a9e2039b5d67dfa0499d2d5f114667dc13fb5d7d03eed230c7ac4f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:490c978b0be9d35a253a5e31dd598f6d66b453625f0eb7dc2d81b22b8c3bb3f4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:e433faaf07f44e44f1a1b839fee847480fe3db9431509dafc9f16d618d491d0f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:cf6d8851a06b79d10089962c9dadcb34dad00bf027af000f7102297a54aaff2e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-win32.whl", hash = "sha256:4f803e7d66ac7d8342998b8b07393788991353a46b05bbaad0b253d6f3484ea8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-win_amd64.whl", hash = "sha256:8e17bd32267e3870855de3baed7d0efa6337344d68f44853fd9195c919f39400" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycares/4.9.0/pycares-4.9.0-cp310-cp310-win_arm64.whl", hash = "sha256:6b74f75d8e430f9bb11a1cc99b2e328eed74b17d8d4b476de09126f38d419eb9" },
]

[[package]]
name = "pycodestyle"
version = "2.6.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycodestyle/2.6.0/pycodestyle-2.6.0.tar.gz", hash = "sha256:c58a7d2815e0e8d7972bf1803331fb0152f867bd89adf8a01dfd55085434192e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycodestyle/2.6.0/pycodestyle-2.6.0-py2.py3-none-any.whl", hash = "sha256:2295e7b2f6b5bd100585ebcb1f616591b652db8a741695b3d8f5d28bdc934367" },
]

[[package]]
name = "pycoin"
version = "0.92.20241201"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycoin/0.92.20241201/pycoin-0.92.20241201.tar.gz", hash = "sha256:6e937be181573ccf02b35064844bec46de130386b45f3df196d3074a8c790512" }

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycparser/2.22/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycparser/2.22/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc" },
]

[[package]]
name = "pycryptodome"
version = "3.23.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0.tar.gz", hash = "sha256:447700a657182d60338bab09fdb27518f8856aecd80ae4c6bdddb67ff5da44ef" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-macosx_10_9_universal2.whl", hash = "sha256:187058ab80b3281b1de11c2e6842a357a1f71b42cb1e15bce373f3d238135c27" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-macosx_10_9_x86_64.whl", hash = "sha256:cfb5cd445280c5b0a4e6187a7ce8de5a07b5f3f897f235caa11f1f435f182843" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:67bd81fcbe34f43ad9422ee8fd4843c8e7198dd88dd3d40e6de42ee65fbe1490" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c8987bd3307a39bc03df5c8e0e3d8be0c4c3518b7f044b0f4c15d1aa78f52575" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:aa0698f65e5b570426fc31b8162ed4603b0c2841cbb9088e2b01641e3065915b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:53ecbafc2b55353edcebd64bf5da94a2a2cdf5090a6915bcca6eca6cc452585a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-musllinux_1_2_i686.whl", hash = "sha256:156df9667ad9f2ad26255926524e1c136d6664b741547deb0a86a9acf5ea631f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:dea827b4d55ee390dc89b2afe5927d4308a8b538ae91d9c6f7a5090f397af1aa" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-win32.whl", hash = "sha256:507dbead45474b62b2bbe318eb1c4c8ee641077532067fec9c1aa82c31f84886" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-win_amd64.whl", hash = "sha256:c75b52aacc6c0c260f204cbdd834f76edc9fb0d8e0da9fbf8352ef58202564e2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-cp37-abi3-win_arm64.whl", hash = "sha256:11eeeb6917903876f134b56ba11abe95c0b0fd5e3330def218083c7d98bbcb3c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:ddb95b49df036ddd264a0ad246d1be5b672000f12d6961ea2c267083a5e19379" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d8e95564beb8782abfd9e431c974e14563a794a4944c29d6d3b7b5ea042110b4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:14e15c081e912c4b0d75632acd8382dfce45b258667aa3c67caf7a4d4c13f630" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a7fc76bf273353dc7e5207d172b83f569540fc9a28d63171061c42e361d22353" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodome/3.23.0/pycryptodome-3.23.0-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:45c69ad715ca1a94f778215a11e66b7ff989d792a4d63b68dc586a1da1392ff5" },
]

[[package]]
name = "pycryptodomex"
version = "3.20.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0.tar.gz", hash = "sha256:7a710b79baddd65b806402e14766c721aee8fb83381769c27920f26476276c1e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-macosx_10_9_universal2.whl", hash = "sha256:59af01efb011b0e8b686ba7758d59cf4a8263f9ad35911bfe3f416cee4f5c08c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-macosx_10_9_x86_64.whl", hash = "sha256:82ee7696ed8eb9a82c7037f32ba9b7c59e51dda6f105b39f043b6ef293989cb3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:91852d4480a4537d169c29a9d104dda44094c78f1f5b67bca76c29a91042b623" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bca649483d5ed251d06daf25957f802e44e6bb6df2e8f218ae71968ff8f8edc4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:6e186342cfcc3aafaad565cbd496060e5a614b441cacc3995ef0091115c1f6c5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:25cd61e846aaab76d5791d006497134602a9e451e954833018161befc3b5b9ed" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-musllinux_1_1_i686.whl", hash = "sha256:9c682436c359b5ada67e882fec34689726a09c461efd75b6ea77b2403d5665b7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:7a7a8f33a1f1fb762ede6cc9cbab8f2a9ba13b196bfaf7bc6f0b39d2ba315a43" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-win32.whl", hash = "sha256:c39778fd0548d78917b61f03c1fa8bfda6cfcf98c767decf360945fe6f97461e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-cp35-abi3-win_amd64.whl", hash = "sha256:2a47bcc478741b71273b917232f521fd5704ab4b25d301669879e7273d3586cc" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-pp310-pypy310_pp73-macosx_10_9_x86_64.whl", hash = "sha256:f2e497413560e03421484189a6b65e33fe800d3bd75590e6d78d4dfdb7accf3b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e48217c7901edd95f9f097feaa0388da215ed14ce2ece803d3f300b4e694abea" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d00fe8596e1cc46b44bf3907354e9377aa030ec4cd04afbbf6e899fc1e2a7781" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pycryptodomex/3.20.0/pycryptodomex-3.20.0-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:88afd7a3af7ddddd42c2deda43d53d3dfc016c11327d0915f90ca34ebda91499" },
]

[[package]]
name = "pydantic"
version = "2.9.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "annotated-types" },
    { name = "pydantic-core" },
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic/2.9.2/pydantic-2.9.2.tar.gz", hash = "sha256:d155cef71265d1e9807ed1c32b4c8deec042a44a50a4188b25ac67ecd81a9c0f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic/2.9.2/pydantic-2.9.2-py3-none-any.whl", hash = "sha256:f048cec7b26778210e28a0459867920654d48e5e62db0958433636cde4254f12" },
]

[[package]]
name = "pydantic-core"
version = "2.23.4"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4.tar.gz", hash = "sha256:2584f7cf844ac4d970fba483a717dbe10c1c1c96a969bf65d61ffe94df1b2863" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-macosx_10_12_x86_64.whl", hash = "sha256:b10bd51f823d891193d4717448fab065733958bdb6a6b351967bd349d48d5c9b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:4fc714bdbfb534f94034efaa6eadd74e5b93c8fa6315565a222f7b6f42ca1166" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:63e46b3169866bd62849936de036f901a9356e36376079b05efa83caeaa02ceb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:ed1a53de42fbe34853ba90513cea21673481cd81ed1be739f7f2efb931b24916" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:cfdd16ab5e59fc31b5e906d1a3f666571abc367598e3e02c83403acabc092e07" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:255a8ef062cbf6674450e668482456abac99a5583bbafb73f9ad469540a3a232" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4a7cd62e831afe623fbb7aabbb4fe583212115b3ef38a9f6b71869ba644624a2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:f09e2ff1f17c2b51f2bc76d1cc33da96298f0a036a137f5440ab3ec5360b624f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:e38e63e6f3d1cec5a27e0afe90a085af8b6806ee208b33030e65b6516353f1a3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:0dbd8dbed2085ed23b5c04afa29d8fd2771674223135dc9bc937f3c09284d071" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-none-win32.whl", hash = "sha256:6531b7ca5f951d663c339002e91aaebda765ec7d61b7d1e3991051906ddde119" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-cp310-none-win_amd64.whl", hash = "sha256:7c9129eb40958b3d4500fa2467e6a83356b3b61bfff1b414c7361d9220f9ae8f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-macosx_10_12_x86_64.whl", hash = "sha256:f455ee30a9d61d3e1a15abd5068827773d6e4dc513e795f380cdd59932c782d5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:1e90d2e3bd2c3863d48525d297cd143fe541be8bbf6f579504b9712cb6b643ec" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2e203fdf807ac7e12ab59ca2bfcabb38c7cf0b33c41efeb00f8e5da1d86af480" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e08277a400de01bc72436a0ccd02bdf596631411f592ad985dcee21445bd0068" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:f220b0eea5965dec25480b6333c788fb72ce5f9129e8759ef876a1d805d00801" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-musllinux_1_1_aarch64.whl", hash = "sha256:d06b0c8da4f16d1d1e352134427cb194a0a6e19ad5db9161bf32b2113409e728" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-musllinux_1_1_x86_64.whl", hash = "sha256:ba1a0996f6c2773bd83e63f18914c1de3c9dd26d55f4ac302a7efe93fb8e7433" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pydantic-core/2.23.4/pydantic_core-2.23.4-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:9a5bce9d23aac8f0cf0836ecfc033896aa8443b501c58d0602dbfd5bd5b37753" },
]

[[package]]
name = "pyflakes"
version = "2.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyflakes/2.2.0/pyflakes-2.2.0.tar.gz", hash = "sha256:35b2d75ee967ea93b55750aa9edbbf72813e06a66ba54438df2cfac9e3c27fc8" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyflakes/2.2.0/pyflakes-2.2.0-py2.py3-none-any.whl", hash = "sha256:0d94e0e05a19e57a99444b6ddcf9a6eb2e5c68d3ca1e98e90707af8152c90a92" },
]

[[package]]
name = "pygments"
version = "2.19.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pygments/2.19.2/pygments-2.19.2.tar.gz", hash = "sha256:636cb2477cec7f8952536970bc533bc43743542f70392ae026374600add5b887" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pygments/2.19.2/pygments-2.19.2-py3-none-any.whl", hash = "sha256:86540386c03d588bb81d44bc3928634ff26449851e99741617ecb9037ee5ec0b" },
]

[[package]]
name = "pyhanko"
version = "0.21.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "asn1crypto" },
    { name = "click" },
    { name = "cryptography" },
    { name = "pyhanko-certvalidator" },
    { name = "pyyaml" },
    { name = "qrcode" },
    { name = "requests" },
    { name = "tzlocal" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyhanko/0.21.0/pyHanko-0.21.0.tar.gz", hash = "sha256:e5609fb8a0b8d60168999cffaa382f0361066cb429204ad2b76e84e6e7f5eb93" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyhanko/0.21.0/pyHanko-0.21.0-py3-none-any.whl", hash = "sha256:0a4de2c26a55c85ee6da79fc995a7fc6a61b7520270f03251caf8977ba27ccb8" },
]

[[package]]
name = "pyhanko-certvalidator"
version = "0.26.8"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "asn1crypto" },
    { name = "cryptography" },
    { name = "oscrypto" },
    { name = "requests" },
    { name = "uritools" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyhanko-certvalidator/0.26.8/pyhanko_certvalidator-0.26.8.tar.gz", hash = "sha256:57c496cebfa5d5e4d3d300dbb4974c66e1a4d9ca4a7a978b20bc66fdeb9dbcdb" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyhanko-certvalidator/0.26.8/pyhanko_certvalidator-0.26.8-py3-none-any.whl", hash = "sha256:72a6872366fc7ceed0c7f5c61634e5ae5962ae75c169679b367aafcda9b9bdb9" },
]

[[package]]
name = "pyjwt"
version = "2.8.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyjwt/2.8.0/PyJWT-2.8.0.tar.gz", hash = "sha256:57e28d156e3d5c10088e0c68abb90bfac3df82b40a71bd0daa20c65ccd5c23de" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyjwt/2.8.0/PyJWT-2.8.0-py3-none-any.whl", hash = "sha256:59127c392cc44c2da5bb3192169a91f429924e17aff6534d70fdc02ab3e04320" },
]

[[package]]
name = "pymongo"
version = "4.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1.tar.gz", hash = "sha256:13d0624c13a91da71fa0d960205d93b3d98344481be865ee7cc238c972d41d73" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:62459b91a513a7b441cfd70ea7fd15c50b858877ca823915d32bab08fe173edb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux1_i686.whl", hash = "sha256:633ca2001f80900142068bab907feca99554b557ac105c74a9ed157ed38ca5d6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux2014_aarch64.whl", hash = "sha256:774b9f48bdc385af6654def31e7a7617e01b99cc8aaca1ab3ef6ea0492205e57" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux2014_i686.whl", hash = "sha256:1c153274699424e8f89f2097d5113f8cbe7898a8d62afaad0270a0f0bd0af53b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux2014_ppc64le.whl", hash = "sha256:c878286b1464f462616a47f315d14f02f03512c6b81cb568e996c3f1f79bff8a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux2014_s390x.whl", hash = "sha256:e4e36810c541bd1976cd05452e797860b775886cf32c3e8136b9fe48c2c8ba95" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux2014_x86_64.whl", hash = "sha256:75e449ab068af63b7729195343315bc63d242166d88467314be182cc54ce235d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:b0606d14892ae2a2b1450e37c8924381e9b64683386a9853e4467f02fd5b44b6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:5cbfa85a12cfe3dca21951cd432051c505ac461bd9f4a635207d982dd9df2373" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:71810eade75ae1c466adc158d1fa8141040f75427b76240316d97f3c89edd72f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:716499113650aacfe1b94d37e0a863f1e84b8d47737c74a2f44f8dfccad46952" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:349c8e522e0b785f442fc9d7fc01c59f7f13f1abe9395310d0d817cff03ec034" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-win32.whl", hash = "sha256:0271bbba36bb130202e011171c1883c4c193036ad0b1e02ecfbea6837790b7de" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pymongo/4.0.1/pymongo-4.0.1-cp310-cp310-win_amd64.whl", hash = "sha256:d419e2dbc4943ad6df7ee05e707d7b2c2b512b92407bb6ff643bccbdea399c3a" },
]

[[package]]
name = "pynacl"
version = "1.5.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0.tar.gz", hash = "sha256:8ac7448f09ab85811607bdd21ec2464495ac8b7c66d146bf545b0f08fb9220ba" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-macosx_10_10_universal2.whl", hash = "sha256:401002a4aaa07c9414132aaed7f6836ff98f59277a234704ff66878c2ee4a0d1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_24_aarch64.whl", hash = "sha256:52cb72a79269189d4e0dc537556f4740f7f0a9ec41c1322598799b0bdad4ef92" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a36d4a9dda1f19ce6e03c9a784a2921a4b726b02e1c736600ca9c22029474394" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_24_x86_64.whl", hash = "sha256:0c84947a22519e013607c9be43706dd42513f9e6ae5d39d3613ca1e142fba44d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:06b8f6fa7f5de8d5d2f7573fe8c863c051225a27b61e6860fd047b1775807858" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:a422368fc821589c228f4c49438a368831cb5bbc0eab5ebe1d7fac9dded6567b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:61f642bf2378713e2c2e1de73444a3778e5f0a38be6fee0fe532fe30060282ff" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-win32.whl", hash = "sha256:e46dae94e34b085175f8abb3b0aaa7da40767865ac82c928eeb9e57e1ea8a543" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-win_amd64.whl", hash = "sha256:20f42270d27e1b6a29f54032090b972d97f0a1b0948cc52392041ef7831fee93" },
]

[[package]]
name = "pyopenssl"
version = "23.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cryptography" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyopenssl/23.2.0/pyOpenSSL-23.2.0.tar.gz", hash = "sha256:276f931f55a452e7dea69c7173e984eb2a4407ce413c918aa34b55f82f9b8bac" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyopenssl/23.2.0/pyOpenSSL-23.2.0-py3-none-any.whl", hash = "sha256:24f0dc5227396b3e831f4c7f602b950a5e9833d292c8e4a2e06b709292806ae2" },
]

[[package]]
name = "pyotp"
version = "2.6.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyotp/2.6.0/pyotp-2.6.0.tar.gz", hash = "sha256:d28ddfd40e0c1b6a6b9da961c7d47a10261fb58f378cb00f05ce88b26df9c432" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyotp/2.6.0/pyotp-2.6.0-py2.py3-none-any.whl", hash = "sha256:9d144de0f8a601d6869abe1409f4a3f75f097c37b50a36a3bf165810a6e23f28" },
]

[[package]]
name = "pypd"
version = "1.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "requests" },
    { name = "six" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pypd/1.1.0/pypd-1.1.0.tar.gz", hash = "sha256:e955f7bd2adb059e576308ef11e437bdd8d1ddca14b599a9250f6f78a6c70694" }

[[package]]
name = "pypdf3"
version = "1.0.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "tqdm" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pypdf3/1.0.6/PyPDF3-1.0.6.tar.gz", hash = "sha256:c946f3273419e37258e35e72273f49904ab15723d87a761c1115ef99799f8c5f" }

[[package]]
name = "pysha3"
version = "1.0.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pysha3/1.0.2/pysha3-1.0.2.tar.gz", hash = "sha256:fe988e73f2ce6d947220624f04d467faf05f1bbdbc64b0a201296bb3af92739e" }

[[package]]
name = "pysocks"
version = "1.7.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pysocks/1.7.1/PySocks-1.7.1.tar.gz", hash = "sha256:3f8804571ebe159c380ac6de37643bb4685970655d3bba243530d6558b799aa0" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pysocks/1.7.1/PySocks-1.7.1-py3-none-any.whl", hash = "sha256:2725bd0a9925919b9b51739eea5f9e2bae91e83288108a9ad338b2e3a4435ee5" },
]

[[package]]
name = "pytest"
version = "7.4.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "exceptiongroup" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
    { name = "tomli" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest/7.4.3/pytest-7.4.3.tar.gz", hash = "sha256:d989d136982de4e3b29dabcc838ad581c64e8ed52c11fbe86ddebd9da0818cd5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest/7.4.3/pytest-7.4.3-py3-none-any.whl", hash = "sha256:0d009c083ea859a71b76adf7c1d502e4bc170b80a8ef002da5806527b9591fac" },
]

[[package]]
name = "pytest-cache"
version = "1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "execnet" },
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-cache/1.0/pytest-cache-1.0.tar.gz", hash = "sha256:be7468edd4d3d83f1e844959fd6e3fd28e77a481440a7118d430130ea31b07a9" }

[[package]]
name = "pytest-cov"
version = "4.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "coverage", extra = ["toml"] },
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-cov/4.1.0/pytest-cov-4.1.0.tar.gz", hash = "sha256:3904b13dfbfec47f003b8e77fd5b589cd11904a21ddf1ab38a64f204d6a10ef6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-cov/4.1.0/pytest_cov-4.1.0-py3-none-any.whl", hash = "sha256:6ba70b9e97e69fcc3fb45bfeab2d0a138fb65c4d0d6a41ef33983ad114be8c3a" },
]

[[package]]
name = "pytest-django"
version = "4.7.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-django/4.7.0/pytest-django-4.7.0.tar.gz", hash = "sha256:92d6fd46b1d79b54fb6b060bbb39428073396cec717d5f2e122a990d4b6aa5e8" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-django/4.7.0/pytest_django-4.7.0-py3-none-any.whl", hash = "sha256:4e1c79d5261ade2dd58d91208017cd8f62cb4710b56e012ecd361d15d5d662a2" },
]

[[package]]
name = "pytest-env"
version = "1.1.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pytest" },
    { name = "tomli" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-env/1.1.1/pytest_env-1.1.1.tar.gz", hash = "sha256:1efb8acce1f6431196150f3b30673443ff05a6fabff64539a9495cd2248adf9e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-env/1.1.1/pytest_env-1.1.1-py3-none-any.whl", hash = "sha256:2b71b37c6810f28bec790a7b373c777af87352b3a359b3de0edb9d24df5cf8b3" },
]

[[package]]
name = "pytest-faulthandler"
version = "2.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-faulthandler/2.0.1/pytest-faulthandler-2.0.1.tar.gz", hash = "sha256:ed72bbce87ac344da81eb7d882196a457d4a1026a3da4a57154dacd85cd71ae5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-faulthandler/2.0.1/pytest_faulthandler-2.0.1-py2.py3-none-any.whl", hash = "sha256:236430ba962fd1c910d670922be55fe5b25ea9bc3fc6561a0cafbb8759e7504d" },
]

[[package]]
name = "pytest-mock"
version = "3.12.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-mock/3.12.0/pytest-mock-3.12.0.tar.gz", hash = "sha256:31a40f038c22cad32287bb43932054451ff5583ff094bca6f675df2f8bc1a6e9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-mock/3.12.0/pytest_mock-3.12.0-py3-none-any.whl", hash = "sha256:0972719a7263072da3a21c7f4773069bcc7486027d7e8e1f81d98a47e701bc4f" },
]

[[package]]
name = "pytest-pep8"
version = "1.0.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pep8" },
    { name = "pytest" },
    { name = "pytest-cache" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-pep8/1.0.6/pytest-pep8-1.0.6.tar.gz", hash = "sha256:032ef7e5fa3ac30f4458c73e05bb67b0f036a8a5cb418a534b3170f89f120318" }

[[package]]
name = "pytest-timeout"
version = "2.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-timeout/2.2.0/pytest-timeout-2.2.0.tar.gz", hash = "sha256:3b0b95dabf3cb50bac9ef5ca912fa0cfc286526af17afc806824df20c2f72c90" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-timeout/2.2.0/pytest_timeout-2.2.0-py3-none-any.whl", hash = "sha256:bde531e096466f49398a59f2dde76fa78429a09a12411466f88a07213e220de2" },
]

[[package]]
name = "pytest-xdist"
version = "3.6.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "execnet" },
    { name = "pytest" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-xdist/3.6.1/pytest_xdist-3.6.1.tar.gz", hash = "sha256:ead156a4db231eec769737f57668ef58a2084a34b2e55c4a8fa20d861107300d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytest-xdist/3.6.1/pytest_xdist-3.6.1-py3-none-any.whl", hash = "sha256:9ed4adfb68a016610848639bb7e02c9352d5d9f03d04809919e2dafc3be4cca7" },
]

[[package]]
name = "python-axolotl-curve25519"
version = "0.4.1.post2"
source = { git = "https://github.com/hannob/python-axolotl-curve25519.git?rev=901f4fb12e1290b72fbd26ea1f40755b079fa241#901f4fb12e1290b72fbd26ea1f40755b079fa241" }

[[package]]
name = "python-bidi"
version = "0.6.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6.tar.gz", hash = "sha256:07db4c7da502593bd6e39c07b3a38733704070de0cbf92a7b7277b7be8867dd9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-macosx_10_12_x86_64.whl", hash = "sha256:09d4da6b5851d0df01d7313a11d22f308fdfb0e12461f7262e0f55c521ccc0f1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:493a844891e23264411b01df58ba77d5dbb0045da3787f4195f50a56bfb847d9" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:6a4f4c664b2594d2d6be6a31c9254e784d6d5c1b17edfdccb5f0fac317a1cd5e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:b53b8b061b67908b5b436abede8c450c8d2fa965cb713d541688f552b4cfa3d3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:b144a1b8766fa6a536cc0feb6fdd29d91af7a82a0c09d89db5fc0b79d5678d7d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:41fde9b4bb45c0e1b3283599e7539c82624ef8a8d3115da76b06160d923aab09" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:de020488c334c31916ee7526c1a867bf632516c1c2a0420d14d10b79f00761c7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:27cf629a0ef983a25cfd62c6238ee1e742e35552409d5c1b43f6d22945adc4c2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:9a9de76229ac22cb6bd40b56a8f7f0c42cbdff985dbd14b65bac955acf070594" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-musllinux_1_2_armv7l.whl", hash = "sha256:2150ac84f7b15f00f8cd9e29fee7edb4639b7ed2cd9e3d23e2dfd83098f719b7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:dc8b0566cef5277f127a80e7546b52393050e5a572f08a352ca220e3f94807cf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:3564e574db1a0b3826ed6e646dc7206602189c31194d8da412007477ce653174" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-win32.whl", hash = "sha256:92eb89f9d8aa0c877cb49fc6356c7f5566e819ea29306992e26be59a5ce468d7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-cp310-cp310-win_amd64.whl", hash = "sha256:1d627f8cfeba70fe4e0ec27b35615c938a483cbef2d9eb7e1e42400d2196019e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-macosx_10_12_x86_64.whl", hash = "sha256:fd9bf9736269ad5cb0d215308fd44e1e02fe591cb9fbb7927d83492358c7ed5f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:d941a6a8a7159982d904982cfe0feb0a794913c5592d8137ccae0d518b2575e4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c0e715b500b09cefccaddb7087978dcd755443b9620aa1cc7b441824253cf2b8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:4142467ec0caa063aca894ca8f1e8a4d9ca6834093c06b0ad5e7aa98dc801079" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:e2f227ee564e0241e57269043bdfa13025d08d0919b349f5c686e8cfc0540dbf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:00081439e969c9d9d2ede8eccef4e91397f601931c4f02864edccb760c8f1db5" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:804c74d070f4e85c6976e55cdbb3f4ead5ec5d7ea0cfad8f18f5464be5174ec9" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:0781c3c63b4bc3b37273de2076cb9b875436ae19be0ff04752914d02a4375790" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-musllinux_1_2_aarch64.whl", hash = "sha256:39eed023add8c53684f1de96cb72b4309cc4d412745f59b5d0dab48e6b88317b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-musllinux_1_2_armv7l.whl", hash = "sha256:91a8cb8feac5d0042e2897042fe7bbbeab5dea1ab785f4b7d0c0bbbf6bc7aefd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-musllinux_1_2_i686.whl", hash = "sha256:a6ac2a3ec5ccc3736e29bb201f27bd33707bfde774d3d222826aa181552590b2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bidi/0.6.6/python_bidi-0.6.6-pp310-pypy310_pp73-musllinux_1_2_x86_64.whl", hash = "sha256:6dfa55611022f95058bb7deb2ac20755ae8abbe1104f87515f561e4a56944ba1" },
]

[[package]]
name = "python-bitcointx"
version = "1.1.5"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-bitcointx/1.1.5/python_bitcointx-1.1.5-py3-none-any.whl", hash = "sha256:7d961afac69dde865140e65af47c4ecc7b73a0697ced27244345539d8e94c555" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-dateutil/2.9.0.post0/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-dateutil/2.9.0.post0/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427" },
]

[[package]]
name = "python-logstash"
version = "0.4.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-logstash/0.4.6/python-logstash-0.4.6.tar.gz", hash = "sha256:10943e5df83f592b4d61b63ad1afff855ccc8c9467f78718f0a59809ba1fe68c" }

[[package]]
name = "python-telegram-bot"
version = "20.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "httpx" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-telegram-bot/20.3/python-telegram-bot-20.3.tar.gz", hash = "sha256:73e46a534be9d1c790ce8b494765cca18a5c2f3f5b4932d83bcb06bb0051eb4a" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/python-telegram-bot/20.3/python_telegram_bot-20.3-py3-none-any.whl", hash = "sha256:1185edee387db7b08027e87b67fa9a3cc3263ae5ab5bb55513acd1bca5c3cf4b" },
]

[[package]]
name = "pytz"
version = "2021.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytz/2021.3/pytz-2021.3.tar.gz", hash = "sha256:acad2d8b20a1af07d4e4c9d2e9285c5ed9104354062f275f3fcd88dcef4f1326" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pytz/2021.3/pytz-2021.3-py2.py3-none-any.whl", hash = "sha256:3672058bc3453457b622aab7a1c3bfd5ab0bdae451512f6cf25f64ed37f5b87c" },
]

[[package]]
name = "pyunormalize"
version = "16.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyunormalize/16.0.0/pyunormalize-16.0.0.tar.gz", hash = "sha256:2e1dfbb4a118154ae26f70710426a52a364b926c9191f764601f5a8cb12761f7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyunormalize/16.0.0/pyunormalize-16.0.0-py3-none-any.whl", hash = "sha256:c647d95e5d1e2ea9a2f448d1d95d8518348df24eab5c3fd32d2b5c3300a49152" },
]

[[package]]
name = "pywin32"
version = "311"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pywin32/311/pywin32-311-cp310-cp310-win32.whl", hash = "sha256:d03ff496d2a0cd4a5893504789d4a15399133fe82517455e78bad62efbb7f0a3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pywin32/311/pywin32-311-cp310-cp310-win_amd64.whl", hash = "sha256:797c2772017851984b97180b0bebe4b620bb86328e8a884bb626156295a63b3b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pywin32/311/pywin32-311-cp310-cp310-win_arm64.whl", hash = "sha256:0502d1facf1fed4839a9a51ccbcc63d952cf318f78ffc00a7e78528ac27d7a2b" },
]

[[package]]
name = "pyyaml"
version = "6.0.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:0a9a2848a5b7feac301353437eb7d5957887edbf81d56e903999a75a3d743086" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:29717114e51c84ddfba879543fb232a6ed60086602313ca38cce623c1d62cfbf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:8824b5a04a04a047e72eea5cec3bc266db09e35de6bdfe34c9436ac5ee27d237" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:7c36280e6fb8385e520936c3cb3b8042851904eba0e58d277dca80a5cfed590b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ec031d5d2feb36d1d1a24380e4db6d43695f3748343d99434e6f5f9156aaa2ed" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:936d68689298c36b53b29f23c6dbb74de12b4ac12ca6cfe0e047bedceea56180" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:23502f431948090f597378482b4812b0caae32c22213aecf3b55325e049a6c68" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-win32.whl", hash = "sha256:2e99c6826ffa974fe6e27cdb5ed0021786b03fc98e5ee3c5bfe1fd5015f42b99" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-win_amd64.whl", hash = "sha256:a4d3091415f010369ae4ed1fc6b79def9416358877534caf6a0fdd2146c87a3e" },
]

[[package]]
name = "qrcode"
version = "7.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/qrcode/7.3.1/qrcode-7.3.1.tar.gz", hash = "sha256:375a6ff240ca9bd41adc070428b5dfc1dcfbb0f2507f1ac848f6cded38956578" }

[[package]]
name = "redis"
version = "6.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "async-timeout" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/redis/6.2.0/redis-6.2.0.tar.gz", hash = "sha256:e821f129b75dde6cb99dd35e5c76e8c49512a5a0d8dfdc560b2fbd44b85ca977" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/redis/6.2.0/redis-6.2.0-py3-none-any.whl", hash = "sha256:c8ddf316ee0aab65f04a11229e94a64b2618451dab7a67cb2f77eb799d872d5e" },
]

[[package]]
name = "referencing"
version = "0.36.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "attrs" },
    { name = "rpds-py" },
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/referencing/0.36.2/referencing-0.36.2.tar.gz", hash = "sha256:df2e89862cd09deabbdba16944cc3f10feb6b3e6f18e902f7cc25609a34775aa" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/referencing/0.36.2/referencing-0.36.2-py3-none-any.whl", hash = "sha256:e8699adbbf8b5c7de96d8ffa0eb5c158b3beafce084968e2ea8bb08c6794dcd0" },
]

[[package]]
name = "regex"
version = "2024.11.6"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6.tar.gz", hash = "sha256:7ab159b063c52a0333c884e4679f8d7a85112ee3078fe3d9004b2dd875585519" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:ff590880083d60acc0433f9c3f713c51f7ac6ebb9adf889c79a261ecf541aa91" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:658f90550f38270639e83ce492f27d2c8d2cd63805c65a13a14d36ca126753f0" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:164d8b7b3b4bcb2068b97428060b2a53be050085ef94eca7f240e7947f1b080e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d3660c82f209655a06b587d55e723f0b813d3a7db2e32e5e7dc64ac2a9e86fde" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:d22326fcdef5e08c154280b71163ced384b428343ae16a5ab2b3354aed12436e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f1ac758ef6aebfc8943560194e9fd0fa18bcb34d89fd8bd2af18183afd8da3a2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:997d6a487ff00807ba810e0f8332c18b4eb8d29463cfb7c820dc4b6e7562d0cf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:02a02d2bb04fec86ad61f3ea7f49c015a0681bf76abb9857f945d26159d2968c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl", hash = "sha256:f02f93b92358ee3f78660e43b4b0091229260c5d5c408d17d60bf26b6c900e86" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:06eb1be98df10e81ebaded73fcd51989dcf534e3c753466e4b60c4697a003b67" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:040df6fe1a5504eb0f04f048e6d09cd7c7110fef851d7c567a6b6e09942feb7d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:fdabbfc59f2c6edba2a6622c647b716e34e8e3867e0ab975412c5c2f79b82da2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:8447d2d39b5abe381419319f942de20b7ecd60ce86f16a23b0698f22e1b70008" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:da8f5fc57d1933de22a9e23eec290a0d8a5927a5370d24bda9a6abe50683fe62" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-win32.whl", hash = "sha256:b489578720afb782f6ccf2840920f3a32e31ba28a4b162e13900c3e6bd3f930e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/regex/2024.11.6/regex-2024.11.6-cp310-cp310-win_amd64.whl", hash = "sha256:5071b2093e793357c9d8b2929dfc13ac5f0a6c650559503bb81189d0a3814519" },
]

[[package]]
name = "reportlab"
version = "4.4.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "charset-normalizer" },
    { name = "pillow" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/reportlab/4.4.2/reportlab-4.4.2.tar.gz", hash = "sha256:fc6283048ddd0781a9db1d671715990e6aa059c8d40ec9baf34294c4bd583a36" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/reportlab/4.4.2/reportlab-4.4.2-py3-none-any.whl", hash = "sha256:58e11be387457928707c12153b7e41e52533a5da3f587b15ba8f8fd0805c6ee2" },
]

[[package]]
name = "requests"
version = "2.31.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/requests/2.31.0/requests-2.31.0.tar.gz", hash = "sha256:942c5a758f98d790eaed1a29cb6eefc7ffb0d1cf7af05c3d2791656dbd6ad1e1" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/requests/2.31.0/requests-2.31.0-py3-none-any.whl", hash = "sha256:58cd2187c01e70e6e26505bca751777aa9f2ee0b7f4300988b709f44e013003f" },
]

[[package]]
name = "rlp"
version = "4.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "eth-utils" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/rlp/4.1.0/rlp-4.1.0.tar.gz", hash = "sha256:be07564270a96f3e225e2c107db263de96b5bc1f27722d2855bd3459a08e95a9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rlp/4.1.0/rlp-4.1.0-py3-none-any.whl", hash = "sha256:8eca394c579bad34ee0b937aecb96a57052ff3716e19c7a578883e767bc5da6f" },
]

[[package]]
name = "rpds-py"
version = "0.26.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0.tar.gz", hash = "sha256:20dae58a859b0906f0685642e591056f1e787f3a8b39c8e8749a45dc7d26bdb0" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-macosx_10_12_x86_64.whl", hash = "sha256:4c70c70f9169692b36307a95f3d8c0a9fcd79f7b4a383aad5eaa0e9718b79b37" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:777c62479d12395bfb932944e61e915741e364c843afc3196b694db3d669fcd0" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ec671691e72dff75817386aa02d81e708b5a7ec0dec6669ec05213ff6b77e1bd" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:6a1cb5d6ce81379401bbb7f6dbe3d56de537fb8235979843f0d53bc2e9815a79" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:4f789e32fa1fb6a7bf890e0124e7b42d1e60d28ebff57fe806719abb75f0e9a3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:9c55b0a669976cf258afd718de3d9ad1b7d1fe0a91cd1ab36f38b03d4d4aeaaf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c70d9ec912802ecfd6cd390dadb34a9578b04f9bcb8e863d0a7598ba5e9e7ccc" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:3021933c2cb7def39d927b9862292e0f4c75a13d7de70eb0ab06efed4c508c19" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:8a7898b6ca3b7d6659e55cdac825a2e58c638cbf335cde41f4619e290dd0ad11" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:12bff2ad9447188377f1b2794772f91fe68bb4bbfa5a39d7941fbebdbf8c500f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:191aa858f7d4902e975d4cf2f2d9243816c91e9605070aeb09c0a800d187e323" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-win32.whl", hash = "sha256:b37a04d9f52cb76b6b78f35109b513f6519efb481d8ca4c321f6a3b9580b3f45" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-cp310-cp310-win_amd64.whl", hash = "sha256:38721d4c9edd3eb6670437d8d5e2070063f305bfa2d5aa4278c51cedcd508a84" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-macosx_10_12_x86_64.whl", hash = "sha256:3c0909c5234543ada2515c05dc08595b08d621ba919629e94427e8e03539c958" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:c1fb0cda2abcc0ac62f64e2ea4b4e64c57dfd6b885e693095460c61bde7bb18e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:84d142d2d6cf9b31c12aa4878d82ed3b2324226270b89b676ac62ccd7df52d08" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:a547e21c5610b7e9093d870be50682a6a6cf180d6da0f42c47c306073bfdbbf6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:35e9a70a0f335371275cdcd08bc5b8051ac494dd58bff3bbfb421038220dc871" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:0dfa6115c6def37905344d56fb54c03afc49104e2ca473d5dedec0f6606913b4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:313cfcd6af1a55a286a3c9a25f64af6d0e46cf60bc5798f1db152d97a216ff6f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:f7bf2496fa563c046d05e4d232d7b7fd61346e2402052064b773e5c378bf6f73" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-musllinux_1_2_aarch64.whl", hash = "sha256:aa81873e2c8c5aa616ab8e017a481a96742fdf9313c40f14338ca7dbf50cb55f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-musllinux_1_2_i686.whl", hash = "sha256:68ffcf982715f5b5b7686bdd349ff75d422e8f22551000c24b30eaa1b7f7ae84" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-musllinux_1_2_x86_64.whl", hash = "sha256:6188de70e190847bb6db3dc3981cbadff87d27d6fe9b4f0e18726d55795cee9b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/rpds-py/0.26.0/rpds_py-0.26.0-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:1c962145c7473723df9722ba4c058de12eb5ebedcb4e27e7d902920aa3831ee8" },
]

[[package]]
name = "s3transfer"
version = "0.5.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "botocore" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/s3transfer/0.5.2/s3transfer-0.5.2.tar.gz", hash = "sha256:95c58c194ce657a5f4fb0b9e60a84968c808888aed628cd98ab8771fe1db98ed" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/s3transfer/0.5.2/s3transfer-0.5.2-py3-none-any.whl", hash = "sha256:7a6f4c4d1fdb9a2b640244008e142cbc2cd3ae34b386584ef044dd0f27101971" },
]

[[package]]
name = "safe-eth-py"
version = "5.8.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "packaging" },
    { name = "py-evm" },
    { name = "requests" },
    { name = "safe-pysha3" },
    { name = "web3" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/safe-eth-py/5.8.0/safe-eth-py-5.8.0.tar.gz", hash = "sha256:ec65c8885d410bc218a289b0d690078c50e9cd3a43397919ea5409c3b080663f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/safe-eth-py/5.8.0/safe_eth_py-5.8.0-py3-none-any.whl", hash = "sha256:aa99fabddef61d76a29ccb2d5cc2158967fbb1a65e2fecd6900b2efc3b65c363" },
]

[[package]]
name = "safe-pysha3"
version = "1.0.5"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/safe-pysha3/1.0.5/safe_pysha3-1.0.5.tar.gz", hash = "sha256:88ceaad6af4b6bdecd2f54b31ad0e5e5e210d4f5ecabb1bd1fd3539ad61b7bf1" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/safe-pysha3/1.0.5/safe_pysha3-1.0.5-cp310-cp310-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:3d15b9b8e25c47dcf68857660b48c7bfb540b8aaaa4158651402f19ef047dff7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/safe-pysha3/1.0.5/safe_pysha3-1.0.5-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:dbdc2f048fa48b660d26eb6eb897eec4e250d01219ae20cf5b1f8f8682194a41" },
]

[[package]]
name = "sentry-sdk"
version = "1.9.8"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "urllib3" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/sentry-sdk/1.9.8/sentry-sdk-1.9.8.tar.gz", hash = "sha256:****************************************************************" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sentry-sdk/1.9.8/sentry_sdk-1.9.8-py2.py3-none-any.whl", hash = "sha256:7378c08d81da78a4860da7b4900ac51fef38be841d01bc5b7cb249ac51e070c6" },
]

[[package]]
name = "service-identity"
version = "24.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "attrs" },
    { name = "cryptography" },
    { name = "pyasn1" },
    { name = "pyasn1-modules" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/service-identity/24.2.0/service_identity-24.2.0.tar.gz", hash = "sha256:b8683ba13f0d39c6cd5d625d2c5f65421d6d707b013b375c355751557cbe8e09" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/service-identity/24.2.0/service_identity-24.2.0-py3-none-any.whl", hash = "sha256:6b047fbd8a84fd0bb0d55ebce4031e400562b9196e1e0d3e0fe2b8a59f6d4a85" },
]

[[package]]
name = "setuptools"
version = "80.9.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/setuptools/80.9.0/setuptools-80.9.0.tar.gz", hash = "sha256:f36b47402ecde768dbfafc46e8e4207b4360c654f1f3bb84475f0a28628fb19c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/setuptools/80.9.0/setuptools-80.9.0-py3-none-any.whl", hash = "sha256:062d34222ad13e0cc312a4c02d73f059e86a4acbfbdea8f8f76b28c99f306922" },
]

[[package]]
name = "simpleeval"
version = "1.0.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/simpleeval/1.0.3/simpleeval-1.0.3.tar.gz", hash = "sha256:67bbf246040ac3b57c29cf048657b9cf31d4e7b9d6659684daa08ca8f1e45829" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/simpleeval/1.0.3/simpleeval-1.0.3-py3-none-any.whl", hash = "sha256:e3bdbb8c82c26297c9a153902d0fd1858a6c3774bf53ff4f134788c3f2035c38" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/six/1.17.0/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/six/1.17.0/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274" },
]

[[package]]
name = "slack-sdk"
version = "3.21.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/slack-sdk/3.21.2/slack_sdk-3.21.2.tar.gz", hash = "sha256:cb74aa764ae32cd61971baa745d8bdf50ffea4ad06e4a0a2ab1e879ae6918496" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/slack-sdk/3.21.2/slack_sdk-3.21.2-py2.py3-none-any.whl", hash = "sha256:fd04ca4aae3071fbd6b284b11ef39b6c72d687b1a2118f40dbcba9921da07d62" },
]

[[package]]
name = "smmap"
version = "5.0.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/smmap/5.0.2/smmap-5.0.2.tar.gz", hash = "sha256:26ea65a03958fa0c8a1c7e8c7a58fdc77221b8910f6be2131affade476898ad5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/smmap/5.0.2/smmap-5.0.2-py3-none-any.whl", hash = "sha256:b30115f0def7d7531d22a0fb6502488d879e75b260a9db4d0819cfb25403af5e" },
]

[[package]]
name = "sniffio"
version = "1.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/sniffio/1.3.1/sniffio-1.3.1.tar.gz", hash = "sha256:f4324edc670a0f49750a81b895f35c3adb843cca46f0530f79fc1babb23789dc" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sniffio/1.3.1/sniffio-1.3.1-py3-none-any.whl", hash = "sha256:2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2" },
]

[[package]]
name = "solders"
version = "0.10.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "jsonalias" },
    { name = "typing-extensions" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0.tar.gz", hash = "sha256:dd5409c4441b88c07f277fc4b2b5ed935e439c570375f681f672d24e5c8a438c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-macosx_10_9_x86_64.macosx_11_0_arm64.macosx_10_9_universal2.whl", hash = "sha256:e0a15c016d1ef7cb6d25c3945596ff815ef72bb0fe15249d6b49eb0c46b3535e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0b6f93e3d6d8a6e2771fb7368d5d0547cbfb5ff69258950f561e21bedb22fe29" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:cb838808e29a15365e200a75b4268b2ad134416a23522e04663f6690891427e9" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", hash = "sha256:2d73896d10bfe7dec2856ca847297ff12eb027c327af9d6cf506da6f883ccb54" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:804f092bb86127168db1f660f10848d585812e667d67dd8e013c929f57479e94" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d7b0ef566186e7c9b852c1fd50c8ca9ea6a88eededf039da4c20181a93b03afa" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:a062e436be397ac3daddb7acecb8eac95abee558f2a847484df445e028b0e907" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-musllinux_1_2_i686.whl", hash = "sha256:6d1f2713a3688fe96eaaf35f95ce2dde75cfc89795d39a6ee74d76e29ca7ed43" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:a135df7e151c7fa36b717030444b83cc5e6bf8e6d376a53a8161d84e2444e133" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/solders/0.10.0/solders-0.10.0-cp37-abi3-win_amd64.whl", hash = "sha256:a56905e96dded9605d48ad1a04874fcb5c48862ade20303ef1b380ef02bda7dd" },
]

[[package]]
name = "sortedcontainers"
version = "2.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/sortedcontainers/2.4.0/sortedcontainers-2.4.0.tar.gz", hash = "sha256:25caa5a06cc30b6b83d11423433f65d1f9d76c4c6a0c90e3379eaa43b9bfdb88" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sortedcontainers/2.4.0/sortedcontainers-2.4.0-py2.py3-none-any.whl", hash = "sha256:a163dcaede0f1c021485e957a39245190e74249897e2ae4b2aa38595db237ee0" },
]

[[package]]
name = "spiffworkflow"
version = "1.1.7"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "celery" },
    { name = "configparser" },
    { name = "dateparser" },
    { name = "lxml" },
    { name = "pytz" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/spiffworkflow/1.1.7/SpiffWorkflow-1.1.7.tar.gz", hash = "sha256:2349c064ec3b4920d3889111798fac6831491109aa3dad34f6d0688ad9070983" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/spiffworkflow/1.1.7/SpiffWorkflow-1.1.7-py3-none-any.whl", hash = "sha256:0333f0883a24bc40bd2345cde98ddff8335f67827de382bc91abb43af61f1da0" },
]

[[package]]
name = "sqlalchemy"
version = "1.4.29"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "greenlet", marker = "platform_machine == 'AMD64' or platform_machine == 'WIN32' or platform_machine == 'aarch64' or platform_machine == 'amd64' or platform_machine == 'ppc64le' or platform_machine == 'win32' or platform_machine == 'x86_64'" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlalchemy/1.4.29/SQLAlchemy-1.4.29.tar.gz", hash = "sha256:fa2bad14e1474ba649cfc969c1d2ec915dd3e79677f346bbfe08e93ef9020b39" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlalchemy/1.4.29/SQLAlchemy-1.4.29-cp310-cp310-macosx_10_14_x86_64.whl", hash = "sha256:78abc507d17753ed434b6cc0c0693126279723d5656d9775bfcac966a99a899b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlalchemy/1.4.29/SQLAlchemy-1.4.29-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:eb8c993706e86178ce15a6b86a335a2064f52254b640e7f53365e716423d33f4" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlalchemy/1.4.29/SQLAlchemy-1.4.29-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl", hash = "sha256:804e22d5b6165a4f3f019dd9c94bec5687de985a9c54286b93ded9f7846b8c82" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlalchemy/1.4.29/SQLAlchemy-1.4.29-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:56d9d62021946263d4478c9ca012fbd1805f10994cb615c88e7bfd1ae14604d8" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlalchemy/1.4.29/SQLAlchemy-1.4.29-cp310-cp310-win32.whl", hash = "sha256:027f356c727db24f3c75828c7feb426f87ce1241242d08958e454bd025810660" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlalchemy/1.4.29/SQLAlchemy-1.4.29-cp310-cp310-win_amd64.whl", hash = "sha256:debaf09a823061f88a8dee04949814cf7e82fb394c5bca22c780cb03172ca23b" },
]

[[package]]
name = "sqlparse"
version = "0.5.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlparse/0.5.3/sqlparse-0.5.3.tar.gz", hash = "sha256:09f67787f56a0b16ecdbde1bfc7f5d9c3371ca683cfeaa8e6ff60b4807ec9272" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/sqlparse/0.5.3/sqlparse-0.5.3-py3-none-any.whl", hash = "sha256:cf2196ed3418f3ba5de6af7e82c694a9fbdbfecccdfc72e281548517081f16ca" },
]

[[package]]
name = "stack-data"
version = "0.6.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "asttokens" },
    { name = "executing" },
    { name = "pure-eval" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/stack-data/0.6.3/stack_data-0.6.3.tar.gz", hash = "sha256:836a778de4fec4dcd1dcd89ed8abff8a221f58308462e1c4aa2a3cf30148f0b9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/stack-data/0.6.3/stack_data-0.6.3-py3-none-any.whl", hash = "sha256:d5558e0c25a4cb0853cddad3d77da9891a08cb85dd9f9f91b9f8cd66e511e695" },
]

[[package]]
name = "stellar-base-sseclient"
version = "0.0.21"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "requests" },
    { name = "six" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/stellar-base-sseclient/0.0.21/stellar-base-sseclient-0.0.21.tar.gz", hash = "sha256:2a500f3015dede4e9fac0f9d6d9d85f4fdd7fe1c9c10b2b111a6ae190cc5dc00" }

[[package]]
name = "stellar-sdk"
version = "8.2.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aiohttp" },
    { name = "aiohttp-sse-client" },
    { name = "mnemonic" },
    { name = "pynacl" },
    { name = "requests" },
    { name = "stellar-base-sseclient" },
    { name = "toml" },
    { name = "typeguard" },
    { name = "urllib3" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/stellar-sdk/8.2.1/stellar_sdk-8.2.1.tar.gz", hash = "sha256:5d70b15d4ef9c8c6d613b5d8d932e5edd80900a0979aea7549a3d3449245d134" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/stellar-sdk/8.2.1/stellar_sdk-8.2.1-py3-none-any.whl", hash = "sha256:f7f4ecffa331e0137643f12a0e62843bf75c080b2ed2f275cc974ad180339ceb" },
]

[[package]]
name = "stevedore"
version = "5.4.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "pbr" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/stevedore/5.4.1/stevedore-5.4.1.tar.gz", hash = "sha256:3135b5ae50fe12816ef291baff420acb727fcd356106e3e9cbfa9e5985cd6f4b" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/stevedore/5.4.1/stevedore-5.4.1-py3-none-any.whl", hash = "sha256:d10a31c7b86cba16c1f6e8d15416955fc797052351a56af15e608ad20811fcfe" },
]

[[package]]
name = "svglib"
version = "1.5.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "cssselect2" },
    { name = "lxml" },
    { name = "reportlab" },
    { name = "tinycss2" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/svglib/1.5.1/svglib-1.5.1.tar.gz", hash = "sha256:3ae765d3a9409ee60c0fb4d24c2deb6a80617aa927054f5bcd7fc98f0695e587" }

[[package]]
name = "text-unidecode"
version = "1.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/text-unidecode/1.3/text-unidecode-1.3.tar.gz", hash = "sha256:bad6603bb14d279193107714b288be206cac565dfa49aa5b105294dd5c4aab93" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/text-unidecode/1.3/text_unidecode-1.3-py2.py3-none-any.whl", hash = "sha256:1311f10e8b895935241623731c2ba64f4c455287888b18189350b67134a822e8" },
]

[[package]]
name = "texttable"
version = "1.7.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/texttable/1.7.0/texttable-1.7.0.tar.gz", hash = "sha256:2d2068fb55115807d3ac77a4ca68fa48803e84ebb0ee2340f858107a36522638" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/texttable/1.7.0/texttable-1.7.0-py2.py3-none-any.whl", hash = "sha256:72227d592c82b3d7f672731ae73e4d1f88cd8e2ef5b075a7a7f01a23a3743917" },
]

[[package]]
name = "tinycss2"
version = "1.4.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "webencodings" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/tinycss2/1.4.0/tinycss2-1.4.0.tar.gz", hash = "sha256:10c0972f6fc0fbee87c3edb76549357415e94548c1ae10ebccdea16fb404a9b7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/tinycss2/1.4.0/tinycss2-1.4.0-py3-none-any.whl", hash = "sha256:3a49cf47b7675da0b15d0c6e1df8df4ebd96e9394bb905a5775adb0d884c5289" },
]

[[package]]
name = "tokenize-rt"
version = "6.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/tokenize-rt/6.2.0/tokenize_rt-6.2.0.tar.gz", hash = "sha256:8439c042b330c553fdbe1758e4a05c0ed460dbbbb24a606f11f0dee75da4cad6" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/tokenize-rt/6.2.0/tokenize_rt-6.2.0-py2.py3-none-any.whl", hash = "sha256:a152bf4f249c847a66497a4a95f63376ed68ac6abf092a2f7cfb29d044ecff44" },
]

[[package]]
name = "toml"
version = "0.10.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/toml/0.10.2/toml-0.10.2.tar.gz", hash = "sha256:b3bda1d108d5dd99f4a20d24d9c348e91c4db7ab1b749200bded2f839ccbe68f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/toml/0.10.2/toml-0.10.2-py2.py3-none-any.whl", hash = "sha256:806143ae5bfb6a3c6e736a764057db0e6a0e05e338b5630894a5f779cabb4f9b" },
]

[[package]]
name = "tomli"
version = "2.2.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/tomli/2.2.1/tomli-2.2.1.tar.gz", hash = "sha256:cd45e1dc79c835ce60f7404ec8119f2eb06d38b1deba146f07ced3bbc44505ff" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/tomli/2.2.1/tomli-2.2.1-py3-none-any.whl", hash = "sha256:cb55c73c5f4408779d0cf3eef9f762b9c9f147a77de7b258bef0a5628adc85cc" },
]

[[package]]
name = "toolz"
version = "1.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/toolz/1.0.0/toolz-1.0.0.tar.gz", hash = "sha256:2c86e3d9a04798ac556793bced838816296a2f085017664e4995cb40a1047a02" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/toolz/1.0.0/toolz-1.0.0-py3-none-any.whl", hash = "sha256:292c8f1c4e7516bf9086f8850935c799a874039c8bcf959d47b600e4c44a6236" },
]

[[package]]
name = "tqdm"
version = "4.67.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/tqdm/4.67.1/tqdm-4.67.1.tar.gz", hash = "sha256:f8aef9c52c08c13a65f30ea34f4e5aac3fd1a34959879d7e59e63027286627f2" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/tqdm/4.67.1/tqdm-4.67.1-py3-none-any.whl", hash = "sha256:26445eca388f82e72884e0d580d5464cd801a3ea01e63e5601bdff9ba6a48de2" },
]

[[package]]
name = "traitlets"
version = "5.14.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/traitlets/5.14.3/traitlets-5.14.3.tar.gz", hash = "sha256:9ed0579d3502c94b4b3732ac120375cda96f923114522847de4b3bb98b96b6b7" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/traitlets/5.14.3/traitlets-5.14.3-py3-none-any.whl", hash = "sha256:b74e89e397b1ed28cc831db7aea759ba6640cb3de13090ca145426688ff1ac4f" },
]

[[package]]
name = "trie"
version = "3.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "eth-hash" },
    { name = "eth-utils" },
    { name = "hexbytes" },
    { name = "rlp" },
    { name = "sortedcontainers" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/trie/3.1.0/trie-3.1.0.tar.gz", hash = "sha256:b31fd3376d6dccfe8ad13b525e233f2c268d5c48afb90a4de09672423d4b1026" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/trie/3.1.0/trie-3.1.0-py3-none-any.whl", hash = "sha256:dfc3e6ac0e76f0efa900ec1bfd082f0f1ba87f95cbfd81cc12338b03f4c679c4" },
]

[[package]]
name = "twisted"
version = "25.5.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "attrs" },
    { name = "automat" },
    { name = "constantly" },
    { name = "hyperlink" },
    { name = "incremental" },
    { name = "typing-extensions" },
    { name = "zope-interface" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/twisted/25.5.0/twisted-25.5.0.tar.gz", hash = "sha256:1deb272358cb6be1e3e8fc6f9c8b36f78eb0fa7c2233d2dbe11ec6fee04ea316" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/twisted/25.5.0/twisted-25.5.0-py3-none-any.whl", hash = "sha256:8559f654d01a54a8c3efe66d533d43f383531ebf8d81d9f9ab4769d91ca15df7" },
]

[package.optional-dependencies]
tls = [
    { name = "idna" },
    { name = "pyopenssl" },
    { name = "service-identity" },
]

[[package]]
name = "txaio"
version = "25.6.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/txaio/25.6.1/txaio-25.6.1.tar.gz", hash = "sha256:d8c03dca823515c9bca920df33504923ae54f2dabf476cc5a9ed5cc1691ed687" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/txaio/25.6.1/txaio-25.6.1-py2.py3-none-any.whl", hash = "sha256:f461b917a14d46077fb1668d0bea4998695d93c9c569cd05fd7f193abdd22414" },
]

[[package]]
name = "typeguard"
version = "2.13.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/typeguard/2.13.3/typeguard-2.13.3.tar.gz", hash = "sha256:00edaa8da3a133674796cf5ea87d9f4b4c367d77476e185e80251cc13dfbb8c4" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/typeguard/2.13.3/typeguard-2.13.3-py3-none-any.whl", hash = "sha256:5e3e3be01e887e7eafae5af63d1f36c849aaa94e3a0112097312aabfa16284f1" },
]

[[package]]
name = "typing-extensions"
version = "4.14.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/typing-extensions/4.14.1/typing_extensions-4.14.1.tar.gz", hash = "sha256:38b39f4aeeab64884ce9f74c94263ef78f3c22467c8724005483154c26648d36" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/typing-extensions/4.14.1/typing_extensions-4.14.1-py3-none-any.whl", hash = "sha256:d1e1e3b58374dc93031d6eda2420a48ea44a36c2b4766a4fdeb3710755731d76" },
]

[[package]]
name = "tzdata"
version = "2025.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/tzdata/2025.2/tzdata-2025.2.tar.gz", hash = "sha256:b60a638fcc0daffadf82fe0f57e53d06bdec2f36c4df66280ae79bce6bd6f2b9" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/tzdata/2025.2/tzdata-2025.2-py2.py3-none-any.whl", hash = "sha256:1a403fada01ff9221ca8044d701868fa132215d84beb92242d9acd2147f667a8" },
]

[[package]]
name = "tzlocal"
version = "5.3.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "tzdata", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/tzlocal/5.3.1/tzlocal-5.3.1.tar.gz", hash = "sha256:cceffc7edecefea1f595541dbd6e990cb1ea3d19bf01b2809f362a03dd7921fd" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/tzlocal/5.3.1/tzlocal-5.3.1-py3-none-any.whl", hash = "sha256:eb1a66c3ef5847adf7a834f1be0800581b683b5608e74f86ecbcef8ab91bb85d" },
]

[[package]]
name = "ua-parser"
version = "1.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "ua-parser-builtins" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/ua-parser/1.0.1/ua_parser-1.0.1.tar.gz", hash = "sha256:f9d92bf19d4329019cef91707aecc23c6d65143ad7e29a233f0580fb0d15547d" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ua-parser/1.0.1/ua_parser-1.0.1-py3-none-any.whl", hash = "sha256:b059f2cb0935addea7e551251cbbf42e9a8872f86134163bc1a4f79e0945ffea" },
]

[[package]]
name = "ua-parser-builtins"
version = "0.18.0.post1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/ua-parser-builtins/0.18.0.post1/ua_parser_builtins-0.18.0.post1-py3-none-any.whl", hash = "sha256:eb4f93504040c3a990a6b0742a2afd540d87d7f9f05fd66e94c101db1564674d" },
]

[[package]]
name = "uritemplate"
version = "4.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/uritemplate/4.2.0/uritemplate-4.2.0.tar.gz", hash = "sha256:480c2ed180878955863323eea31b0ede668795de182617fef9c6ca09e6ec9d0e" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/uritemplate/4.2.0/uritemplate-4.2.0-py3-none-any.whl", hash = "sha256:962201ba1c4edcab02e60f9a0d3821e82dfc5d2d6662a21abd533879bdb8a686" },
]

[[package]]
name = "uritools"
version = "5.0.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/uritools/5.0.0/uritools-5.0.0.tar.gz", hash = "sha256:68180cad154062bd5b5d9ffcdd464f8de6934414b25462ae807b00b8df9345de" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/uritools/5.0.0/uritools-5.0.0-py3-none-any.whl", hash = "sha256:cead3a49ba8fbca3f91857343849d506d8639718f4a2e51b62e87393b493bd6f" },
]

[[package]]
name = "urllib3"
version = "1.26.20"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/urllib3/1.26.20/urllib3-1.26.20.tar.gz", hash = "sha256:40c2dc0c681e47eb8f90e7e27bf6ff7df2e677421fd46756da1161c39ca70d32" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/urllib3/1.26.20/urllib3-1.26.20-py2.py3-none-any.whl", hash = "sha256:0ed14ccfbf1c30a9072c7ca157e4319b70d65f623e91e7b32fadb2853431016e" },
]

[[package]]
name = "user-agents"
version = "2.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "ua-parser" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/user-agents/2.2.0/user-agents-2.2.0.tar.gz", hash = "sha256:d36d25178db65308d1458c5fa4ab39c9b2619377010130329f3955e7626ead26" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/user-agents/2.2.0/user_agents-2.2.0-py3-none-any.whl", hash = "sha256:a98c4dc72ecbc64812c4534108806fb0a0b3a11ec3fd1eafe807cee5b0a942e7" },
]

[[package]]
name = "vine"
version = "5.1.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/vine/5.1.0/vine-5.1.0.tar.gz", hash = "sha256:8b62e981d35c41049211cf62a0a1242d8c1ee9bd15bb196ce38aefd6799e61e0" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/vine/5.1.0/vine-5.1.0-py3-none-any.whl", hash = "sha256:40fdf3c48b2cfe1c38a49e9ae2da6fda88e4794c810050a728bd7413811fb1dc" },
]

[[package]]
name = "virtualenv"
version = "20.31.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "distlib" },
    { name = "filelock" },
    { name = "platformdirs" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/virtualenv/20.31.2/virtualenv-20.31.2.tar.gz", hash = "sha256:e10c0a9d02835e592521be48b332b6caee6887f332c111aa79a09b9e79efc2af" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/virtualenv/20.31.2/virtualenv-20.31.2-py3-none-any.whl", hash = "sha256:36efd0d9650ee985f0cad72065001e66d49a6f24eb44d98980f630686243cf11" },
]

[[package]]
name = "wcwidth"
version = "0.2.13"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/wcwidth/0.2.13/wcwidth-0.2.13.tar.gz", hash = "sha256:72ea0c06399eb286d978fdedb6923a9eb47e1c486ce63e9b4e64fc18303972b5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wcwidth/0.2.13/wcwidth-0.2.13-py2.py3-none-any.whl", hash = "sha256:3da69048e4540d84af32131829ff948f1e022c1c6bdb8d6102117aac784f6859" },
]

[[package]]
name = "web3"
version = "6.14.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "aiohttp" },
    { name = "eth-abi" },
    { name = "eth-account" },
    { name = "eth-hash", extra = ["pycryptodome"] },
    { name = "eth-typing" },
    { name = "eth-utils" },
    { name = "hexbytes" },
    { name = "jsonschema" },
    { name = "lru-dict" },
    { name = "protobuf" },
    { name = "pyunormalize" },
    { name = "pywin32", marker = "sys_platform == 'win32'" },
    { name = "requests" },
    { name = "typing-extensions" },
    { name = "websockets" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/web3/6.14.0/web3-6.14.0.tar.gz", hash = "sha256:a3726289da9eff2ce30f9b1b49ec59e9245216f7aecbfa2007f73dbe94999717" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/web3/6.14.0/web3-6.14.0-py3-none-any.whl", hash = "sha256:e7023669ea05d6c9675d25e14342638da25d9b1a512d8a6472b860ed97914aec" },
]

[[package]]
name = "webencodings"
version = "0.5.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/webencodings/0.5.1/webencodings-0.5.1.tar.gz", hash = "sha256:b36a1c245f2d304965eb4e0a82848379241dc04b865afcc4aab16748587e1923" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/webencodings/0.5.1/webencodings-0.5.1-py2.py3-none-any.whl", hash = "sha256:a0af1213f3c2226497a97e2b3aa01a7e4bee4f403f95be16fc9acd2947514a78" },
]

[[package]]
name = "websocket-client"
version = "1.2.3"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/websocket-client/1.2.3/websocket-client-1.2.3.tar.gz", hash = "sha256:1315816c0acc508997eb3ae03b9d3ff619c9d12d544c9a9b553704b1cc4f6af5" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websocket-client/1.2.3/websocket_client-1.2.3-py3-none-any.whl", hash = "sha256:2eed4cc58e4d65613ed6114af2f380f7910ff416fc8c46947f6e76b6815f56c0" },
]

[[package]]
name = "websockets"
version = "15.0.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1.tar.gz", hash = "sha256:82544de02076bafba038ce055ee6412d68da13ab47f0c60cab827346de828dee" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:d63efaa0cd96cf0c5fe4d581521d9fa87744540d4bc999ae6e08595a1014b45b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:ac60e3b188ec7574cb761b08d50fcedf9d77f1530352db4eef1707fe9dee7205" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:5756779642579d902eed757b21b0164cd6fe338506a8083eb58af5c372e39d9a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0fdfe3e2a29e4db3659dbd5bbf04560cea53dd9610273917799f1cde46aa725e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:4c2529b320eb9e35af0fa3016c187dffb84a3ecc572bcee7c3ce302bfeba52bf" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ac1e5c9054fe23226fb11e05a6e630837f074174c4c2f0fe442996112a6de4fb" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:5df592cd503496351d6dc14f7cdad49f268d8e618f80dce0cd5a36b93c3fc08d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:0a34631031a8f05657e8e90903e656959234f3a04552259458aac0b0f9ae6fd9" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:3d00075aa65772e7ce9e990cab3ff1de702aa09be3940d1dc88d5abf1ab8a09c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-win32.whl", hash = "sha256:1234d4ef35db82f5446dca8e35a7da7964d02c127b095e172e54397fb6a6c256" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-cp310-cp310-win_amd64.whl", hash = "sha256:39c1fec2c11dc8d89bba6b2bf1556af381611a173ac2b511cf7231622058af41" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-pp310-pypy310_pp73-macosx_10_15_x86_64.whl", hash = "sha256:0c9e74d766f2818bb95f84c25be4dea09841ac0f734d1966f415e4edfc4ef1c3" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:1009ee0c7739c08a0cd59de430d6de452a55e42d6b522de7aa15e6f67db0b8e1" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:76d1f20b1c7a2fa82367e04982e708723ba0e7b8d43aa643d3dcd404d74f1475" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f29d80eb9a9263b8d109135351caf568cc3f80b9928bccde535c235de55c22d9" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-pp310-pypy310_pp73-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b359ed09954d7c18bbc1680f380c7301f92c60bf924171629c5db97febb12f04" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:cad21560da69f4ce7658ca2cb83138fb4cf695a2ba3e475e0559e05991aa8122" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/websockets/15.0.1/websockets-15.0.1-py3-none-any.whl", hash = "sha256:f7a866fbc1e97b5c617ee4116daaa09b722101d4a3c170c787450ba409f9736f" },
]

[[package]]
name = "wheel"
version = "0.45.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/wheel/0.45.1/wheel-0.45.1.tar.gz", hash = "sha256:661e1abd9198507b1409a20c02106d9670b2576e916d58f520316666abca6729" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wheel/0.45.1/wheel-0.45.1-py3-none-any.whl", hash = "sha256:708e7481cc80179af0e556bbf0cc00b8444c7321e2700b8d8580231d13017248" },
]

[[package]]
name = "whitenoise"
version = "6.8.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/whitenoise/6.8.2/whitenoise-6.8.2.tar.gz", hash = "sha256:486bd7267a375fa9650b136daaec156ac572971acc8bf99add90817a530dd1d4" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/whitenoise/6.8.2/whitenoise-6.8.2-py3-none-any.whl", hash = "sha256:df12dce147a043d1956d81d288c6f0044147c6d2ab9726e5772ac50fb45d2280" },
]

[[package]]
name = "wrapt"
version = "1.17.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2.tar.gz", hash = "sha256:41388e9d4d1522446fe79d3213196bd9e3b301a336965b9e27ca2788ebd122f3" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:3d57c572081fed831ad2d26fd430d565b76aa277ed1d30ff4d40670b1c0dd984" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:b5e251054542ae57ac7f3fba5d10bfff615b6c2fb09abeb37d2f1463f841ae22" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:80dd7db6a7cb57ffbc279c4394246414ec99537ae81ffd702443335a61dbf3a7" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0a6e821770cf99cc586d33833b2ff32faebdbe886bd6322395606cf55153246c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:b60fb58b90c6d63779cb0c0c54eeb38941bae3ecf7a73c764c52c88c2dcb9d72" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b870b5df5b71d8c3359d21be8f0d6c485fa0ebdb6477dda51a1ea54a9b558061" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:4011d137b9955791f9084749cba9a367c68d50ab8d11d64c50ba1688c9b457f2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:1473400e5b2733e58b396a04eb7f35f541e1fb976d0c0724d0223dd607e0f74c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:3cedbfa9c940fdad3e6e941db7138e26ce8aad38ab5fe9dcfadfed9db7a54e62" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-win32.whl", hash = "sha256:582530701bff1dec6779efa00c516496968edd851fba224fbd86e46cc6b73563" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-cp310-cp310-win_amd64.whl", hash = "sha256:58705da316756681ad3c9c73fd15499aa4d8c69f9fd38dc8a35e06c12468582f" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/wrapt/1.17.2/wrapt-1.17.2-py3-none-any.whl", hash = "sha256:b18f2d1533a71f069c7f82d524a52599053d4c7166e9dd374ae2136b7f40f7c8" },
]

[[package]]
name = "xhtml2pdf"
version = "0.2.8"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "arabic-reshaper" },
    { name = "html5lib" },
    { name = "pillow" },
    { name = "pyhanko" },
    { name = "pyhanko-certvalidator" },
    { name = "pypdf3" },
    { name = "python-bidi" },
    { name = "reportlab" },
    { name = "svglib" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/xhtml2pdf/0.2.8/xhtml2pdf-0.2.8.tar.gz", hash = "sha256:****************************************************************" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/xhtml2pdf/0.2.8/xhtml2pdf-0.2.8-py3-none-any.whl", hash = "sha256:330bf335d1bc436eb0fe7efc64d82aa09c7a728df6a57f07a8b062e478c7aa36" },
]

[[package]]
name = "xlsxwriter"
version = "3.2.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/xlsxwriter/3.2.0/XlsxWriter-3.2.0.tar.gz", hash = "sha256:9977d0c661a72866a61f9f7a809e25ebbb0fb7036baa3b9fe74afcfca6b3cb8c" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/xlsxwriter/3.2.0/XlsxWriter-3.2.0-py3-none-any.whl", hash = "sha256:ecfd5405b3e0e228219bcaf24c2ca0915e012ca9464a14048021d21a995d490e" },
]

[[package]]
name = "yarl"
version = "1.7.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "idna" },
    { name = "multidict" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2.tar.gz", hash = "sha256:45399b46d60c253327a460e99856752009fcee5f5d3c80b2f7c0cae1c38d56dd" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:f2a8508f7350512434e41065684076f640ecce176d262a7d54f0da41d99c5a95" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:da6df107b9ccfe52d3a48165e48d72db0eca3e3029b5b8cb4fe6ee3cb870ba8b" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:a1d0894f238763717bdcfea74558c94e3bc34aeacd3351d769460c1a586a8b05" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dfe4b95b7e00c6635a72e2d00b478e8a28bfb122dc76349a06e20792eb53a523" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:c145ab54702334c42237a6c6c4cc08703b6aa9b94e2f227ceb3d477d20c36c63" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:1ca56f002eaf7998b5fcf73b2421790da9d2586331805f38acd9997743114e98" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_12_i686.manylinux2010_i686.whl", hash = "sha256:1d3d5ad8ea96bd6d643d80c7b8d5977b4e2fb1bab6c9da7322616fd26203d125" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl", hash = "sha256:167ab7f64e409e9bdd99333fe8c67b5574a1f0495dcfd905bc7454e766729b9e" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:95a1873b6c0dd1c437fb3bb4a4aaa699a48c218ac7ca1e74b0bee0ab16c7d60d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-musllinux_1_1_i686.whl", hash = "sha256:6152224d0a1eb254f97df3997d79dadd8bb2c1a02ef283dbb34b97d4f8492d23" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-musllinux_1_1_ppc64le.whl", hash = "sha256:5bb7d54b8f61ba6eee541fba4b83d22b8a046b4ef4d8eb7f15a7e35db2e1e245" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-musllinux_1_1_s390x.whl", hash = "sha256:9c1f083e7e71b2dd01f7cd7434a5f88c15213194df38bc29b388ccdf1492b739" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:f44477ae29025d8ea87ec308539f95963ffdc31a82f42ca9deecf2d505242e72" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-win32.whl", hash = "sha256:cff3ba513db55cc6a35076f32c4cdc27032bd075c9faef31fec749e64b45d26c" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yarl/1.7.2/yarl-1.7.2-cp310-cp310-win_amd64.whl", hash = "sha256:c9c6d927e098c2d360695f2e9d38870b2e92e0919be07dbe339aefa32a090265" },
]

[[package]]
name = "yubico-client"
version = "1.13.0"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "requests" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/yubico-client/1.13.0/yubico-client-1.13.0.tar.gz", hash = "sha256:e3b86cd2a123105edfacad40551c7b26e9c1193d81ffe168ee704ebfd3d11162" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/yubico-client/1.13.0/yubico_client-1.13.0-py2.py3-none-any.whl", hash = "sha256:59d818661f638e3f041fae44ba2c0569e4eb2a17865fa7cc9ad6577185c4d185" },
]

[[package]]
name = "zope-event"
version = "5.1"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "setuptools" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-event/5.1/zope_event-5.1.tar.gz", hash = "sha256:a153660e0c228124655748e990396b9d8295d6e4f546fa1b34f3319e1c666e7f" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-event/5.1/zope_event-5.1-py3-none-any.whl", hash = "sha256:53de8f0e9f61dc0598141ac591f49b042b6d74784dab49971b9cc91d0f73a7df" },
]

[[package]]
name = "zope-interface"
version = "7.2"
source = { registry = "https://codeartifact.1cobo.com/pypi/default/simple/" }
dependencies = [
    { name = "setuptools" },
]
sdist = { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-interface/7.2/zope.interface-7.2.tar.gz", hash = "sha256:8b49f1a3d1ee4cdaf5b32d2e738362c7f5e40ac8b46dd7d1a65e82a4872728fe" }
wheels = [
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-interface/7.2/zope.interface-7.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:ce290e62229964715f1011c3dbeab7a4a1e4971fd6f31324c4519464473ef9f2" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-interface/7.2/zope.interface-7.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:05b910a5afe03256b58ab2ba6288960a2892dfeef01336dc4be6f1b9ed02ab0a" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-interface/7.2/zope.interface-7.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:550f1c6588ecc368c9ce13c44a49b8d6b6f3ca7588873c679bd8fd88a1b557b6" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-interface/7.2/zope.interface-7.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:0ef9e2f865721553c6f22a9ff97da0f0216c074bd02b25cf0d3af60ea4d6931d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-interface/7.2/zope.interface-7.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:27f926f0dcb058211a3bb3e0e501c69759613b17a553788b2caeb991bed3b61d" },
    { url = "https://codeartifact.1cobo.com/pypi/default/simple/zope-interface/7.2/zope.interface-7.2-cp310-cp310-win_amd64.whl", hash = "sha256:144964649eba4c5e4410bb0ee290d338e78f179cdbfd15813de1a664e7649b3b" },
]
