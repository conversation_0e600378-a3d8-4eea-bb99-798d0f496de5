[project]
name = "cobo-custody"
version = "0.1.0"
description = "Django-based custody management system"
readme = "README.md"
requires-python = ">=3.10,<3.11"
license = { text = "Proprietary" }
authors = [{ name = "Cobo Team", email = "<EMAIL>" }]
keywords = ["django", "custody", "crypto", "wallet"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Framework :: Django :: 4.2",
    "Intended Audience :: Developers",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
]

dependencies = [
    "asgiref==3.6.0",
    "authlib==1.2.1",
    "backoff==2.1.2",
    "base58==2.1.1",
    "bc-waas2==0.0.48",
    "betterproto==2.0.0b4",
    "bitcoinlib==********",
    "boto3==1.21.27",
    "ccxt==1.95.1",
    "certifi==2025.1.31",
    "channels==3.0.5",
    "channels-redis==3.4.1",
    "cobo-app-python-api==0.1.64",
    "cobo-libs==0.20.270",
    "cobo-waas2-python-api==0.6.134",
    "confluent-kafka==2.3.0",
    "cosmos-proto==0.0.0",
    "croniter==1.4.0",
    "daphne==3.0.2",
    "django==4.2.16",
    "django-cors-headers==3.13.0",
    "django-filter==22.1",
    "django-split-settings==1.1.0",
    "djangorestframework-dataclasses==1.3.1",
    "ecdsa==0.19.0",
    "eth-typing==4.4.0",
    "gate-api==4.70.0",
    "geoip2==4.6.0",
    "googleapis-common-protos==1.69.1",
    "graphene==3.3",
    "graphene-django==3.2.2",
    "graphql-core==3.2.3",
    "grpcio==1.71.0",
    "hashids==1.3.1",
    "httpx[http2]==0.24.1",
    "ipython>=8.37.0",
    "iso8601==1.0.2",
    "onfido-python==2.1.0",
    "openpyxl==3.1.2",
    "phonenumbers==8.12.45",
    "pillow==9.3",
    "protobuf==5.29.3",
    "py-solc-x==2.0.0",
    "pycryptodomex==3.20.0",
    "pyopenssl==23.2.0",
    "pysha3==1.0.2",
    "python-axolotl-curve25519 @ git+https://github.com/hannob/python-axolotl-curve25519.git@901f4fb12e1290b72fbd26ea1f40755b079fa241",
    "python-bitcointx==1.1.5",
    "qrcode==7.3.1",
    "redis==6.2.0",
    "safe-eth-py==5.8.0",
    "sentry-sdk==1.9.8",
    "simpleeval==1.0.3",
    "solders==0.10.0",
    "user-agents==2.2.0",
    "web3==6.14.0",
    "whitenoise==6.8.2",
    "xhtml2pdf==0.2.8",
    "xlsxwriter==3.2.0",
    "yubico-client==1.13.0",
]

[dependency-groups]
dev = [
    "bandit==1.6.2",
    "black==22.3.0",
    "coverage>=7.9.2",
    "django-upgrade==1.21.0",
    "flake8==3.8.3",
    "ipython>=8.37.0",
    "isort==5.1.4",
    "ossaudit==0.5.0",
    "pre-commit==2.19.0",
    "pysocks==1.7.1",
]
test = [
    "allure-pytest==2.13.2",
    "coverage>=7.9.2",
    "factory-boy==3.3.3",
    "mock==5.1.0",
    "pytest==7.4.3",
    "pytest-cache==1.0",
    "pytest-cov==4.1.0",
    "pytest-django==4.7.0",
    "pytest-env==1.1.1",
    "pytest-faulthandler==2.0.1",
    "pytest-mock==3.12.0",
    "pytest-pep8==1.0.6",
    "pytest-timeout==2.2.0",
    "pytest-xdist==3.6.1",
]

[[tool.uv.index]]
url = "https://codeartifact.1cobo.com/pypi/default/simple/"
default = true

# [[tool.uv.index]]
# url = "https://pypi.org/simple/"
