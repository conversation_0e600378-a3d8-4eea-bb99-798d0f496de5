from decimal import Decimal
from typing import Optional

import cobo_waas2
from pydantic import BaseModel, Field


class TokenizationExtra(BaseModel):
    # 初始化信息
    init_params: Optional[cobo_waas2.TokenizationIssuedTokenRequest] = None
    # 白名单是否启用
    token_access_activated: bool = Field(default=False, alias="allowlist_enabled")

    class Config:
        populate_by_name = True


class AmountTokenConfig(BaseModel):
    """
    按币本位金额限制的 Gas Sponsor 配置详情
    """

    max_amount_token: Decimal = Field(..., description="最大垫付金额（币本位），字符串形式以避免浮点数精度问题")


class AmountUsdConfig(BaseModel):
    """
    按U本位金额限制的 Gas Sponsor 配置详情
    """

    max_amount_usd: Decimal = Field(..., description="最大垫付金额（U本位），字符串形式以避免浮点数精度问题")


class CountConfig(BaseModel):
    """
    按次数限制的 Gas Sponsor 配置详情
    """

    max_count: int = Field(..., gt=0, description="最大垫付次数")
