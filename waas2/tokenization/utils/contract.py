import logging
from typing import Dict, List

from django.conf import settings
from web3 import Web3

from waas2.smart_contract.utils.web3 import web3_contract
from waas2.tokenization.abis.CoboERC20 import COBO_ERC20_ABI
from waas2.tokenization.abis.CoboFactory import COBO_FACTORY_ABI

logger = logging.getLogger("waas2.tokenization")


class CoboERC20ContractHelper:
    """
    Token 合约辅助类
    """

    INIT_CODE = "0x60806040526102de8038038061001481610194565b92833981019060408183031261018f5780516001600160a01b03811680820361018f5760208381015190936001600160401b03821161018f570184601f8201121561018f5780519061006d610068836101cf565b610194565b9582875285838301011161018f57849060005b83811061017b57505060009186010152813b15610163577f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc80546001600160a01b03191682179055604051907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b600080a28351156101455750600080848461012c96519101845af4903d1561013c573d61011c610068826101cf565b908152600081943d92013e6101ea565b505b6040516090908161024e8239f35b606092506101ea565b9250505034610154575061012e565b63b398979f60e01b8152600490fd5b60249060405190634c9c8ce360e01b82526004820152fd5b818101830151888201840152869201610080565b600080fd5b6040519190601f01601f191682016001600160401b038111838210176101b957604052565b634e487b7160e01b600052604160045260246000fd5b6001600160401b0381116101b957601f01601f191660200190565b9061021157508051156101ff57805190602001fd5b60405163d6bda27560e01b8152600490fd5b81511580610244575b610222575090565b604051639996b31560e01b81526001600160a01b039091166004820152602490fd5b50803b1561021a56fe608060405273ffffffffffffffffffffffffffffffffffffffff7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc54166000808092368280378136915af43d82803e156056573d90f35b3d90fdfea264697066735822122014a2db0f905ecb3463adc66272cf47bd8e565a7022b1a81e3c4157d37977ef1a64736f6c634300081700330000000000000000000000001fc11f5e118b77cf1904a32a7879adc5ad571fc800000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000"

    @classmethod
    def get_enabled_chains(cls) -> List[str]:
        return list(settings.COBO_FACTORY_ADDRESS.keys())

    @classmethod
    def get_factory_addresses(cls) -> Dict[str, str]:
        return settings.COBO_FACTORY_ADDRESS

    @classmethod
    def get_contract(cls, chain_id: str, token_address: str):
        """获取代币合约实例"""
        return web3_contract(
            chain_id,
            Web3.to_checksum_address(token_address),
            COBO_ERC20_ABI,
        )

    @classmethod
    def get_factory_address(cls, chain_id: str):
        """获取代币合约实例"""
        factory_address = cls.get_factory_addresses().get(chain_id)
        if not factory_address:
            raise ValueError(f"Unsupported chain {chain_id}")
        return factory_address

    @classmethod
    def get_factory_contract(cls, chain_id: str):
        """获取代币合约实例"""
        return web3_contract(
            chain_id,
            cls.get_factory_address(chain_id),
            COBO_FACTORY_ABI,
        )
