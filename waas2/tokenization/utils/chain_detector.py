"""
简单的链类型检测器
"""
import logging
from enum import Enum

logger = logging.getLogger("waas2.tokenization.chain_detector")


class ChainType(str, Enum):
    """链类型枚举"""
    EVM = "EVM"
    SOLANA = "SOLANA"


class ChainDetector:
    """简单的链类型检测器"""
    
    @classmethod
    def get_chain_type(cls, chain_id: str) -> ChainType:
        """
        根据 chain_id 判断链类型
        """
        if not chain_id:
            return ChainType.EVM
            
        # Solana链检测
        if chain_id.startswith("SOL"):
            return ChainType.SOLANA
        
        # 默认为EVM
        return ChainType.EVM
    
    @classmethod
    def is_solana_chain(cls, chain_id: str) -> bool:
        return cls.get_chain_type(chain_id) == ChainType.SOLANA
    
    @classmethod
    def is_evm_chain(cls, chain_id: str) -> bool:
        return cls.get_chain_type(chain_id) == ChainType.EVM
