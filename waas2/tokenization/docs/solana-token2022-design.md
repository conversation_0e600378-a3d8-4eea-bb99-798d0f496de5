# Solana Token 2022 支持设计方案

## 概述

本文档详细描述了在现有 waas2/tokenization 模块中添加 Solana Token 2022 支持的完整设计方案。设计遵循接口兼容、底层路由的原则，确保现有功能不受影响的同时，为 Solana Token 2022 提供完整支持。

## 设计原则

1. **接口兼容性**：现有 API 接口保持完全不变
2. **底层路由**：通过 `chain_id` 和 `standard` 字段自动路由到对应实现
3. **技术要求**：使用 anchorpy 构造 Solana instructions
4. **扩展性**：架构支持未来添加其他链类型
5. **渐进实施**：可以逐步实现各个功能模块

## 架构概览

```mermaid
graph TB
    A[API Views] --> B[Controllers]
    B --> C[Manager Factory]
    C --> D{Chain Type}
    D -->|EVM| E[EVM Managers]
    D -->|Solana| F[Solana Managers]
    
    E --> G[CoboERC20Helper]
    F --> H[SolanaToken2022Helper]
    F --> I[AnchorPy Instructions]
    
    E --> J[Token Model]
    F --> J
    
    subgraph "EVM Implementation"
        E
        G
    end
    
    subgraph "Solana Implementation"
        F
        H
        I
    end
    
    subgraph "Shared Components"
        J
        B
        C
    end
```

## 详细设计

### 1. 链类型抽象层

#### 1.1 链类型枚举

```python
# waas2/tokenization/enums/tokenization.py (扩展)
class ChainType(StrEnum):
    """链类型枚举"""
    EVM = "EVM"
    SOLANA = "SOLANA"

class SolanaTokenExtension(StrEnum):
    """Solana Token 2022 扩展类型"""
    TRANSFER_FEE = "TRANSFER_FEE"
    CONFIDENTIAL_TRANSFER = "CONFIDENTIAL_TRANSFER"
    DEFAULT_ACCOUNT_STATE = "DEFAULT_ACCOUNT_STATE"
    IMMUTABLE_OWNER = "IMMUTABLE_OWNER"
    MEMO_TRANSFER = "MEMO_TRANSFER"
    NON_TRANSFERABLE = "NON_TRANSFERABLE"
    INTEREST_BEARING = "INTEREST_BEARING"
    PERMANENT_DELEGATE = "PERMANENT_DELEGATE"
```

#### 1.2 链类型检测器

```python
# waas2/tokenization/utils/chain_detector.py
class ChainDetector:
    """链类型检测器"""
    
    SOLANA_CHAIN_PREFIXES = ["SOL"]
    EVM_CHAIN_PREFIXES = ["ETH", "BSC", "POLYGON", "AVAX", "SETH"]
    
    @classmethod
    def get_chain_type(cls, chain_id: str) -> ChainType:
        """根据 chain_id 判断链类型"""
        chain_prefix = chain_id.split("_")[0]
        
        if chain_prefix in cls.SOLANA_CHAIN_PREFIXES:
            return ChainType.SOLANA
        elif chain_prefix in cls.EVM_CHAIN_PREFIXES:
            return ChainType.EVM
        else:
            # 默认为 EVM，保持向后兼容
            return ChainType.EVM
    
    @classmethod
    def is_solana_chain(cls, chain_id: str) -> bool:
        return cls.get_chain_type(chain_id) == ChainType.SOLANA
    
    @classmethod
    def is_evm_chain(cls, chain_id: str) -> bool:
        return cls.get_chain_type(chain_id) == ChainType.EVM
```

### 2. 数据模型扩展

#### 2.1 Solana Token 配置

```python
# waas2/tokenization/data/solana.py
class SolanaTokenExtra(BaseModel):
    """Solana Token 2022 特有配置"""
    mint_authority: Optional[str] = None
    freeze_authority: Optional[str] = None
    close_authority: Optional[str] = None
    program_id: str = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
    
    # Token Extensions 配置
    extensions: Dict[str, Any] = Field(default_factory=dict)
    
    # Solana 特有字段
    rent_exempt_reserve: Optional[int] = None
    supply: Optional[int] = None
    
    class Config:
        extra = "allow"

class SolanaTokenParams(BaseModel):
    """Solana Token 2022 创建参数"""
    name: str
    symbol: str
    decimals: int
    mint_authority: str
    freeze_authority: Optional[str] = None
    extensions: List[Dict[str, Any]] = Field(default_factory=list)
```

#### 2.2 现有模型扩展

- 扩展 `cobo_waas2.TokenizationTokenStandard` 添加 `SPL_TOKEN_2022`
- 利用现有 [`Token`](waas2/tokenization/models/tokenization.py:9) 模型的 [`extra`](waas2/tokenization/models/tokenization.py:33) 字段存储 Solana 配置
- 无需修改数据库结构，保持向后兼容

### 3. Solana 专用工具类

#### 3.1 Solana 合约辅助类

```python
# waas2/tokenization/utils/solana_contract.py
class SolanaToken2022Helper:
    """Solana Token 2022 合约辅助类"""
    
    TOKEN_2022_PROGRAM_ID = TOKEN_2022_PROGRAM_ID
    
    @classmethod
    def get_enabled_chains(cls) -> List[str]:
        """获取支持的 Solana 链"""
        return ["SOL", "SOL_DEVNET", "SOL_TESTNET"]
    
    @classmethod
    def get_rpc_endpoints(cls) -> Dict[str, str]:
        """获取 RPC 端点配置"""
        from django.conf import settings
        return getattr(settings, 'SOLANA_RPC_ENDPOINTS', {
            "SOL": "https://api.mainnet-beta.solana.com",
            "SOL_DEVNET": "https://api.devnet.solana.com",
            "SOL_TESTNET": "https://api.testnet.solana.com"
        })
    
    @classmethod
    def get_rpc_client(cls, chain_id: str) -> Client:
        """获取 Solana RPC 客户端"""
        endpoints = cls.get_rpc_endpoints()
        endpoint = endpoints.get(chain_id)
        if not endpoint:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")
        return Client(endpoint)
    
    @classmethod
    def get_mint_info(cls, chain_id: str, mint_address: str):
        """获取 mint 信息"""
        client = cls.get_rpc_client(chain_id)
        mint_pubkey = PublicKey(mint_address)
        return client.get_account_info(mint_pubkey)
    
    @classmethod
    def validate_address(cls, address: str) -> bool:
        """验证 Solana 地址格式"""
        try:
            PublicKey(address)
            return True
        except Exception:
            return False
```

#### 3.2 Anchorpy Instruction 构造工具

```python
# waas2/tokenization/utils/solana_instructions.py
class SolanaInstructionBuilder:
    """使用 anchorpy 和 spl-token 构造 Solana instructions"""
    
    @classmethod
    def create_mint_instruction(
        cls,
        mint_keypair: Keypair,
        mint_authority: PublicKey,
        freeze_authority: Optional[PublicKey] = None,
        decimals: int = 9,
        extensions: Optional[List[Dict[str, Any]]] = None
    ):
        """创建 Token 2022 mint instruction"""
        instructions = []
        
        # 1. 创建账户指令
        # 2. 处理 Token Extensions
        # 3. 初始化 mint 指令
        
        return instructions
    
    @classmethod
    def create_mint_to_instruction(cls, mint, destination, mint_authority, amount, signers=None):
        """创建 mint_to instruction"""
        return create_mint_to_instruction(
            mint=mint,
            dest=destination,
            mint_authority=mint_authority,
            amount=amount,
            multi_signers=signers or [],
            program_id=TOKEN_2022_PROGRAM_ID
        )
    
    @classmethod
    def create_burn_instruction(cls, mint, account, owner, amount, signers=None):
        """创建 burn instruction"""
        return create_burn_instruction(
            mint=mint,
            account=account,
            owner=owner,
            amount=amount,
            multi_signers=signers or [],
            program_id=TOKEN_2022_PROGRAM_ID
        )
    
    @classmethod
    def create_transfer_instruction(cls, source, dest, owner, amount, signers=None):
        """创建 transfer instruction"""
        return create_transfer_instruction(
            source=source,
            dest=dest,
            owner=owner,
            amount=amount,
            multi_signers=signers or [],
            program_id=TOKEN_2022_PROGRAM_ID
        )
```

### 4. 管理器层重构

#### 4.1 抽象基类

```python
# waas2/tokenization/managers/base.py
class BaseTokenizationManager(ABC):
    """代币化管理器抽象基类"""
    
    @abstractmethod
    def deploy(self, params, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """部署代币"""
        pass
    
    @abstractmethod
    def mint(self, params, token, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """铸造代币"""
        pass
    
    @abstractmethod
    def burn(self, params, token, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """销毁代币"""
        pass
    
    @abstractmethod
    def estimate_fee(self, params, operation_type: str) -> Any:
        """估算费用"""
        pass
```

#### 4.2 管理器工厂

```python
# waas2/tokenization/managers/factory.py
class TokenizationManagerFactory:
    """代币化管理器工厂"""
    
    _managers = {}
    
    @classmethod
    def get_manager(cls, chain_id: str) -> BaseTokenizationManager:
        """根据链 ID 获取对应的管理器"""
        chain_type = ChainDetector.get_chain_type(chain_id)
        
        if chain_type not in cls._managers:
            if chain_type == ChainType.SOLANA:
                cls._managers[chain_type] = SolanaTokenizationManager()
            else:  # EVM
                cls._managers[chain_type] = EVMTokenizationManager()
        
        return cls._managers[chain_type]
```

#### 4.3 Solana 专用管理器

```python
# waas2/tokenization/managers/solana/manager.py
class SolanaTokenizationManager(BaseTokenizationManager):
    """Solana Token 2022 管理器"""
    
    def deploy(self, params, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """部署 Solana Token 2022"""
        # 1. 创建 Token 记录
        # 2. 生成 mint keypair
        # 3. 构造 create_mint instructions
        # 4. 处理 Token Extensions
        # 5. 发起 Solana 交易
        pass
    
    def mint(self, params, token, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """铸造 Token 2022"""
        # 1. 构造 mint_to instruction
        # 2. 发起交易
        pass
    
    def burn(self, params, token, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """销毁 Token 2022"""
        # 1. 构造 burn instruction
        # 2. 发起交易
        pass
```

### 5. 控制器层改动

#### 5.1 部署控制器

```python
# waas2/tokenization/controllers/deploy.py (修改)
class TokenizationDeployController:
    def deploy(self, params, org_id, biz_org_id, api_request_info, sign_info):
        # 根据 chain_id 路由到对应的管理器
        manager = TokenizationManagerFactory.get_manager(params.chain_id)
        return manager.deploy(params, org_id, biz_org_id, api_request_info, sign_info)
```

#### 5.2 其他控制器

类似地修改其他控制器：
- [`TokenizationMintController`](waas2/tokenization/controllers/mint.py)
- [`TokenizationBurnController`](waas2/tokenization/controllers/burn.py)
- [`TokenizationContractCallController`](waas2/tokenization/controllers/contract_call.py)
- [`TokenizationEstimateFeeController`](waas2/tokenization/controllers/estimate_fee.py)

### 6. 费用估算扩展

#### 6.1 Solana 费用估算

```python
# waas2/tokenization/controllers/solana/estimate_fee.py
class SolanaEstimateFeeController:
    """Solana 费用估算控制器"""
    
    @classmethod
    def estimate_deploy_fee(cls, params):
        """估算部署费用"""
        # 1. 计算账户租金
        # 2. 计算交易费用
        # 3. 计算 Extension 费用
        pass
    
    @classmethod
    def estimate_mint_fee(cls, params):
        """估算铸造费用"""
        pass
    
    @classmethod
    def estimate_burn_fee(cls, params):
        """估算销毁费用"""
        pass
```

### 7. 配置和设置

#### 7.1 Solana 配置

```python
# waas2/tokenization/settings/base.py (扩展)
SOLANA_TOKEN_2022_CONFIG = {
    "SOL": {
QdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb",
        "rpc_endpoint": "https://api.mainnet-beta.solana.com"
    },
    "SOL_DEVNET": {
        "program_id": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb", 
        "rpc_endpoint": "https://api.devnet.solana.com"
    },
    "SOL_TESTNET": {
        "program_id": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb",
        "rpc_endpoint": "https://api.testnet.solana.com"
    }
}

# Solana RPC 端点配置
SOLANA_RPC_ENDPOINTS = SOLANA_TOKEN_2022_CONFIG
```

#### 7.2 依赖配置

```python
# requirements.txt (新增)
solana>=0.30.0
spl-token>=0.2.0
anchorpy>=0.18.0
```

### 8. Token Extensions 支持

#### 8.1 支持的扩展类型

| 扩展类型 | 描述 | 实现优先级 |
|---------|------|----------|
| Transfer Fee | 转账费用 | 高 |
| Confidential Transfer | 机密转账 | 中 |
| Default Account State | 默认账户状态 | 高 |
| Immutable Owner | 不可变所有者 | 高 |
| Memo Transfer | 备注转账 | 低 |
| Non Transferable | 不可转账 | 中 |
| Interest Bearing | 计息代币 | 低 |
| Permanent Delegate | 永久委托 | 低 |

#### 8.2 扩展配置示例

```json
{
  "extensions": [
    {
      "type": "TRANSFER_FEE",
      "config": {
        "transfer_fee_basis_points": 100,
        "maximum_fee": 1000000,
        "withdraw_withheld_authority": "11111111111111111111111111111112"
      }
    },
    {
      "type": "DEFAULT_ACCOUNT_STATE",
      "config": {
        "state": "frozen"
      }
    }
  ]
}
```

### 9. 接口分层改动总结

#### 9.1 视图层 (Views)
- **改动程度**：无需改动
- **说明**：现有 API 接口保持完全不变，对外透明

#### 9.2 控制器层 (Controllers)
- **改动程度**：轻微改动
- **主要变更**：
  - 添加管理器工厂路由逻辑
  - 保持现有接口签名不变
  - 添加链类型检测

#### 9.3 管理器层 (Managers)
- **改动程度**：重构
- **主要变更**：
  - 创建抽象基类 `BaseTokenizationManager`
  - 现有 EVM 逻辑封装为 `EVMTokenizationManager`
  - 新增 `SolanaTokenizationManager`
  - 添加管理器工厂 `TokenizationManagerFactory`

#### 9.4 工具层 (Utils)
- **改动程度**：扩展
- **主要变更**：
  - 新增 `SolanaToken2022Helper`
  - 新增 `SolanaInstructionBuilder`
  - 新增 `ChainDetector`
  - 现有 `CoboERC20ContractHelper` 保持不变

#### 9.5 数据层 (Models/DAO)
- **改动程度**：扩展
- **主要变更**：
  - 扩展 `TokenizationTokenStandard` 枚举
  - 利用现有 `Token.extra` 字段存储 Solana 配置
  - 无需修改数据库结构

### 10. 实施计划

#### 10.1 第一阶段：基础架构
- [ ] 创建链类型抽象层
- [ ] 实现管理器工厂和抽象基类
- [ ] 重构现有 EVM 管理器
- [ ] 添加基础配置

#### 10.2 第二阶段：Solana 基础功能
- [ ] 实现 Solana 合约辅助类
- [ ] 创建 anchorpy instruction 构造工具
- [ ] 实现基础的 deploy/mint/burn 功能
- [ ] 添加费用估算

#### 10.3 第三阶段：Token Extensions
- [ ] 实现 Transfer Fee 扩展
- [ ] 实现 Default Account State 扩展
- [ ] 实现 Immutable Owner 扩展
- [ ] 添加其他优先级较高的扩展

#### 10.4 第四阶段：完善和测试
- [ ] 添加完整的测试用例
- [ ] 性能优化
- [ ] 文档完善
- [ ] 集成测试

### 11. 风险评估

#### 11.1 技术风险
- **Solana 网络稳定性**：依赖 Solana 网络的稳定性
- **依赖库兼容性**：anchorpy 和 spl-token 库的版本兼容性
- **交易费用波动**：Solana 网络拥堵时费用可能大幅波动

#### 11.2 实施风险
- **现有功能影响**：重构可能影响现有 EVM 功能
- **测试覆盖度**：新功能需要充分的测试覆盖
- **性能影响**：新增的路由逻辑可能影响性能

#### 11.3 风险缓解措施
- 渐进式实施，每个阶段充分测试
- 保持现有接口不变，确保向后兼容
- 添加完整的单元测试和集成测试
- 监控性能指标，及时优化

### 12. 成功指标

#### 12.1 功能指标
- [ ] 支持 Solana Token 2022 的创建、铸造、销毁
- [ ] 支持至少 3 种 Token Extensions
- [ ] 费用估算准确率 > 95%
- [ ] API 响应时间 < 2s

#### 12.2 质量指标
- [ ] 代码覆盖率 > 90%
- [ ] 现有功能回归测试通过率 100%
- [ ] 文档完整性 > 95%

#### 12.3 性能指标
- [ ] 现有 EVM 功能性能无明显下降
- [ ] Solana 交易成功率 > 98%
- [ ] 系统可用性 > 99.9%

## 总结

本设计方案通过引入链类型抽象层和管理器工厂模式，在保持现有接口完全兼容的前提下，为 Solana Token 2022 提供了完整的支持。方案具有以下优势：

1. **零破坏性**：现有代码和 API 完全不受影响
2. **高扩展性**：架构支持未来添加更多链类型
3. **技术先进**：使用 anchorpy 构造 instructions，支持 Token Extensions
4. **实施灵活**：可以渐进式实施，降低风险
5. **维护友好**：清晰的分层架构，便于维护和扩展

通过这个设计方案，waas2/tokenization 模块将能够同时支持 EVM 和 Solana 生态，为用户提供更丰富的代币化服务。
        "program_id": "Tokenz