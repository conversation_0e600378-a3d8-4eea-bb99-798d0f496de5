# Solana Token 2022 实施指南

## 概述

本文档提供了实施 Solana Token 2022 支持的详细步骤指南，按照设计文档中的分阶段计划进行。每个阶段都有明确的目标、任务清单和验收标准。

## 实施原则

1. **渐进式实施**：每个阶段独立完成并测试
2. **向后兼容**：确保现有功能不受影响
3. **充分测试**：每个阶段都要有完整的测试覆盖
4. **文档同步**：代码和文档同步更新

## 第一阶段：基础架构搭建

### 目标
建立支持多链的基础架构，为 Solana 集成做准备。

### 任务清单

#### 1.1 创建链类型抽象层

```bash
# 创建新的枚举文件
touch waas2/tokenization/enums/chain_type.py
touch waas2/tokenization/utils/chain_detector.py
```

**文件内容：**

`waas2/tokenization/enums/chain_type.py`:
```python
from waas2.base.enums.utils import StrEnum

class ChainType(StrEnum):
    """链类型枚举"""
    EVM = "EVM"
    SOLANA = "SOLANA"
```

`waas2/tokenization/utils/chain_detector.py`:
```python
from waas2.tokenization.enums.chain_type import ChainType

class ChainDetector:
    """链类型检测器"""
    
    SOLANA_CHAIN_PREFIXES = ["SOL"]
    EVM_CHAIN_PREFIXES = ["ETH", "BSC", "POLYGON", "AVAX", "SETH"]
    
    @classmethod
    def get_chain_type(cls, chain_id: str) -> ChainType:
        """根据 chain_id 判断链类型"""
        chain_prefix = chain_id.split("_")[0]
        
        if chain_prefix in cls.SOLANA_CHAIN_PREFIXES:
            return ChainType.SOLANA
        elif chain_prefix in cls.EVM_CHAIN_PREFIXES:
            return ChainType.EVM
        else:
            # 默认为 EVM，保持向后兼容
            return ChainType.EVM
    
    @classmethod
    def is_solana_chain(cls, chain_id: str) -> bool:
        return cls.get_chain_type(chain_id) == ChainType.SOLANA
    
    @classmethod
    def is_evm_chain(cls, chain_id: str) -> bool:
        return cls.get_chain_type(chain_id) == ChainType.EVM
```

#### 1.2 创建管理器抽象基类

```bash
# 创建管理器基类
touch waas2/tokenization/managers/base.py
touch waas2/tokenization/managers/factory.py
```

`waas2/tokenization/managers/base.py`:
```python
from abc import ABC, abstractmethod
from typing import Any
import cobo_waas2

class BaseTokenizationManager(ABC):
    """代币化管理器抽象基类"""
    
    @abstractmethod
    def deploy(
        self,
        params: cobo_waas2.TokenizationIssuedTokenRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """部署代币"""
        pass
    
    @abstractmethod
    def mint(
        self,
        params: cobo_waas2.TokenizationMintRequest,
        token,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """铸造代币"""
        pass
    
    @abstractmethod
    def burn(
        self,
        params: cobo_waas2.TokenizationBurnRequest,
        token,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """销毁代币"""
        pass
    
    @abstractmethod
    def estimate_fee(self, params, operation_type: str) -> Any:
        """估算费用"""
        pass
```

#### 1.3 重构现有 EVM 管理器

```bash
# 创建 EVM 管理器目录
mkdir -p waas2/tokenization/managers/evm
touch waas2/tokenization/managers/evm/__init__.py
touch waas2/tokenization/managers/evm/manager.py
```

`waas2/tokenization/managers/evm/manager.py`:
```python
from waas2.tokenization.managers.base import BaseTokenizationManager
from waas2.tokenization.managers.deploy import TokenizationDeployManager
from waas2.tokenization.managers.mint import TokenizationMintManager
from waas2.tokenization.managers.burn import TokenizationBurnManager
from waas2.tokenization.controllers.estimate_fee import TokenizationEstimateFeeController

class EVMTokenizationManager(BaseTokenizationManager):
    """EVM 代币化管理器"""
    
    def deploy(self, params, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """部署 EVM 代币"""
        return TokenizationDeployManager.deploy(
            params, org_id, biz_org_id, api_request_info, sign_info
        )
    
    def mint(self, params, token, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """铸造 EVM 代币"""
        return TokenizationMintManager.mint(
            params, token, org_id, biz_org_id, api_request_info, sign_info
        )
    
    def burn(self, params, token, org_id, biz_org_id, api_request_info, sign_info) -> str:
        """销毁 EVM 代币"""
        return TokenizationBurnManager.burn(
            params, token, org_id, biz_org_id, api_request_info, sign_info
        )
    
    def estimate_fee(self, params, operation_type: str):
        """估算 EVM 费用"""
        return TokenizationEstimateFeeController.estimate_fee(params, operation_type)
```

#### 1.4 创建管理器工厂

`waas2/tokenization/managers/factory.py`:
```python
from waas2.tokenization.utils.chain_detector import ChainDetector, ChainType
from waas2.tokenization.managers.base import BaseTokenizationManager
from waas2.tokenization.managers.evm.manager import EVMTokenizationManager

class TokenizationManagerFactory:
    """代币化管理器工厂"""
    
    _managers = {}
    
    @classmethod
    def get_manager(cls, chain_id: str) -> BaseTokenizationManager:
        """根据链 ID 获取对应的管理器"""
        chain_type = ChainDetector.get_chain_type(chain_id)
        
        if chain_type not in cls._managers:
            if chain_type == ChainType.SOLANA:
                # 暂时抛出异常，第二阶段实现
                raise NotImplementedError("Solana support not implemented yet")
            else:  # EVM
                cls._managers[chain_type] = EVMTokenizationManager()
        
        return cls._managers[chain_type]
```

#### 1.5 更新控制器以使用工厂

修改现有控制器以使用管理器工厂：

```python
# 在相关控制器中添加导入
from waas2.tokenization.managers.factory import TokenizationManagerFactory

# 在部署控制器中修改
def deploy(self, params, org_id, biz_org_id, api_request_info, sign_info):
    manager = TokenizationManagerFactory.get_manager(params.chain_id)
    return manager.deploy(params, org_id, biz_org_id, api_request_info, sign_info)
```

### 验收标准

- [ ] 所有现有测试通过
- [ ] EVM 功能正常工作
- [ ] 链类型检测正确
- [ ] 管理器工厂正确路由到 EVM 管理器
- [ ] Solana 链 ID 抛出 NotImplementedError

### 测试用例

```python
# waas2/tokenization/tests/test_chain_detector.py
class TestChainDetector(TestCase):
    def test_evm_chain_detection(self):
        self.assertEqual(ChainDetector.get_chain_type("ETH"), ChainType.EVM)
        self.assertEqual(ChainDetector.get_chain_type("SETH"), ChainType.EVM)
        
    def test_solana_chain_detection(self):
        self.assertEqual(ChainDetector.get_chain_type("SOL"), ChainType.SOLANA)
        self.assertEqual(ChainDetector.get_chain_type("SOL_DEVNET"), ChainType.SOLANA)
        
    def test_unknown_chain_defaults_to_evm(self):
        self.assertEqual(ChainDetector.get_chain_type("UNKNOWN"), ChainType.EVM)

# waas2/tokenization/tests/test_manager_factory.py
class TestManagerFactory(TestCase):
    def test_evm_manager_creation(self):
        manager = TokenizationManagerFactory.get_manager("ETH")
        self.assertIsInstance(manager, EVMTokenizationManager)
        
    def test_solana_not_implemented(self):
        with self.assertRaises(NotImplementedError):
            TokenizationManagerFactory.get_manager("SOL")
```

## 第二阶段：Solana 基础功能

### 目标
实现 Solana Token 2022 的基础功能，包括部署、铸造、销毁。

### 前置条件
- 第一阶段完成并通过所有测试
- 安装 Solana 相关依赖

### 依赖安装

```bash
# 添加到 requirements.txt
echo "solana>=0.30.0" >> requirements.txt
echo "spl-token>=0.2.0" >> requirements.txt
echo "anchorpy>=0.18.0" >> requirements.txt

pip install -r requirements.txt
```

### 任务清单

#### 2.1 创建 Solana 数据模型

```bash
# 创建 Solana 数据模型
mkdir -p waas2/tokenization/data
touch waas2/tokenization/data/solana.py
```

`waas2/tokenization/data/solana.py`:
```python
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

class SolanaTokenExtra(BaseModel):
    """Solana Token 2022 特有配置"""
    mint_authority: Optional[str] = None
    freeze_authority: Optional[str] = None
    close_authority: Optional[str] = None
    program_id: str = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
    
    # Token Extensions 配置
    extensions: Dict[str, Any] = Field(default_factory=dict)
    
    # Solana 特有字段
    rent_exempt_reserve: Optional[int] = None
    supply: Optional[int] = None
    
    class Config:
        extra = "allow"

class SolanaTokenParams(BaseModel):
    """Solana Token 2022 创建参数"""
    name: str
    symbol: str
    decimals: int
    mint_authority: str
    freeze_authority: Optional[str] = None
    extensions: List[Dict[str, Any]] = Field(default_factory=list)
```

#### 2.2 创建 Solana 工具类

```bash
# 创建 Solana 工具类
touch waas2/tokenization/utils/solana_contract.py
touch waas2/tokenization/utils/solana_instructions.py
```

`waas2/tokenization/utils/solana_contract.py`:
```python
import logging
from typing import Dict, List, Optional
from solana.rpc.api import Client
from solana.publickey import PublicKey
from spl.token.constants import TOKEN_2022_PROGRAM_ID

logger = logging.getLogger("waas2.tokenization")

class SolanaToken2022Helper:
    """Solana Token 2022 合约辅助类"""
    
    TOKEN_2022_PROGRAM_ID = TOKEN_2022_PROGRAM_ID
    
    @classmethod
    def get_enabled_chains(cls) -> List[str]:
        """获取支持的 Solana 链"""
        return ["SOL", "SOL_DEVNET", "SOL_TESTNET"]
    
    @classmethod
    def get_rpc_endpoints(cls) -> Dict[str, str]:
        """获取 RPC 端点配置"""
        from django.conf import settings
        return getattr(settings, 'SOLANA_RPC_ENDPOINTS', {
            "SOL": "https://api.mainnet-beta.solana.com",
            "SOL_DEVNET": "https://api.devnet.solana.com",
            "SOL_TESTNET": "https://api.testnet.solana.com"
        })
    
    @classmethod
    def get_rpc_client(cls, chain_id: str) -> Client:
        """获取 Solana RPC 客户端"""
        endpoints = cls.get_rpc_endpoints()
        endpoint = endpoints.get(chain_id)
        if not endpoint:
            raise ValueError(f"Unsupported Solana chain: {chain_id}")
        return Client(endpoint)
    
    @classmethod
    def validate_address(cls, address: str) -> bool:
        """验证 Solana 地址格式"""
        try:
            PublicKey(address)
            return True
        except Exception:
            return False
```

#### 2.3 创建 Solana 管理器

```bash
# 创建 Solana 管理器目录
mkdir -p waas2/tokenization/managers/solana
touch waas2/tokenization/managers/solana/__init__.py
touch waas2/tokenization/managers/solana/manager.py
touch waas2/tokenization/managers/solana/deploy.py
touch waas2/tokenization/managers/solana/mint.py
touch waas2/tokenization/managers/solana/burn.py
```

#### 2.4 更新管理器工厂

修改 `waas2/tokenization/managers/factory.py`:
```python
from waas2.tokenization.managers.solana.manager import SolanaTokenizationManager

class TokenizationManagerFactory:
    # ... 现有代码 ...
    
    @classmethod
    def get_manager(cls,
chain_id: str) -> BaseTokenizationManager:
        """根据链 ID 获取对应的管理器"""
        chain_type = ChainDetector.get_chain_type(chain_id)
        
        if chain_type not in cls._managers:
            if chain_type == ChainType.SOLANA:
                cls._managers[chain_type] = SolanaTokenizationManager()
            else:  # EVM
                cls._managers[chain_type] = EVMTokenizationManager()
        
        return cls._managers[chain_type]
```

#### 2.5 扩展 TokenStandard 枚举

在 `cobo_waas2` 库中添加新的标准：
```python
# 需要在 cobo_waas2 中添加
TokenizationTokenStandard.SPL_TOKEN_2022 = "SPL_TOKEN_2022"
```

### 验收标准

- [ ] Solana 工具类正常工作
- [ ] 管理器工厂正确路由到 Solana 管理器
- [ ] 基础的 deploy/mint/burn 功能实现
- [ ] 所有测试通过

### 测试用例

```python
# waas2/tokenization/tests/test_solana_helper.py
class TestSolanaToken2022Helper(TestCase):
    def test_validate_address(self):
        # 有效的 Solana 地址
        valid_address = "********************************"
        self.assertTrue(SolanaToken2022Helper.validate_address(valid_address))
        
        # 无效的地址
        invalid_address = "invalid_address"
        self.assertFalse(SolanaToken2022Helper.validate_address(invalid_address))
    
    def test_get_rpc_client(self):
        client = SolanaToken2022Helper.get_rpc_client("SOL_DEVNET")
        self.assertIsInstance(client, Client)
```

## 第三阶段：Token Extensions 支持

### 目标
实现 Solana Token 2022 的扩展功能，优先实现高优先级扩展。

### 任务清单

#### 3.1 实现 Transfer Fee 扩展

```bash
# 创建扩展相关文件
mkdir -p waas2/tokenization/utils/solana/extensions
touch waas2/tokenization/utils/solana/extensions/__init__.py
touch waas2/tokenization/utils/solana/extensions/transfer_fee.py
touch waas2/tokenization/utils/solana/extensions/default_account_state.py
```

`waas2/tokenization/utils/solana/extensions/transfer_fee.py`:
```python
from typing import Dict, Any
from solana.publickey import PublicKey
from solana.transaction import Instruction

class TransferFeeExtension:
    """Transfer Fee 扩展实现"""
    
    @classmethod
    def create_initialize_instruction(
        cls,
        mint: PublicKey,
        transfer_fee_config_authority: PublicKey,
        withdraw_withheld_authority: PublicKey,
        transfer_fee_basis_points: int,
        maximum_fee: int
    ) -> Instruction:
        """创建 Transfer Fee 初始化指令"""
        # 使用 anchorpy 或直接构造指令
        # 这里需要根据 Token 2022 的具体 IDL 实现
        pass
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """验证 Transfer Fee 配置"""
        required_fields = [
            "transfer_fee_basis_points",
            "maximum_fee",
            "withdraw_withheld_authority"
        ]
        return all(field in config for field in required_fields)
```

#### 3.2 实现 Default Account State 扩展

`waas2/tokenization/utils/solana/extensions/default_account_state.py`:
```python
from typing import Dict, Any
from solana.publickey import PublicKey
from solana.transaction import Instruction

class DefaultAccountStateExtension:
    """Default Account State 扩展实现"""
    
    ACCOUNT_STATES = {
        "uninitialized": 0,
        "initialized": 1,
        "frozen": 2
    }
    
    @classmethod
    def create_initialize_instruction(
        cls,
        mint: PublicKey,
        state: str
    ) -> Instruction:
        """创建 Default Account State 初始化指令"""
        if state not in cls.ACCOUNT_STATES:
            raise ValueError(f"Invalid account state: {state}")
        
        # 实现具体的指令构造
        pass
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """验证 Default Account State 配置"""
        state = config.get("state")
        return state in cls.ACCOUNT_STATES
```

#### 3.3 更新 Instruction Builder

修改 `waas2/tokenization/utils/solana_instructions.py` 以支持扩展：

```python
from waas2.tokenization.utils.solana.extensions.transfer_fee import TransferFeeExtension
from waas2.tokenization.utils.solana.extensions.default_account_state import DefaultAccountStateExtension

class SolanaInstructionBuilder:
    # ... 现有代码 ...
    
    @classmethod
    def _create_extension_instruction(cls, mint: PublicKey, extension: Dict[str, Any]):
        """创建 Token Extension 指令"""
        ext_type = extension.get("type")
        config = extension.get("config", {})
        
        if ext_type == "TRANSFER_FEE":
            if not TransferFeeExtension.validate_config(config):
                raise ValueError("Invalid Transfer Fee config")
            return TransferFeeExtension.create_initialize_instruction(
                mint=mint,
                transfer_fee_config_authority=PublicKey(config["transfer_fee_config_authority"]),
                withdraw_withheld_authority=PublicKey(config["withdraw_withheld_authority"]),
                transfer_fee_basis_points=config["transfer_fee_basis_points"],
                maximum_fee=config["maximum_fee"]
            )
        elif ext_type == "DEFAULT_ACCOUNT_STATE":
            if not DefaultAccountStateExtension.validate_config(config):
                raise ValueError("Invalid Default Account State config")
            return DefaultAccountStateExtension.create_initialize_instruction(
                mint=mint,
                state=config["state"]
            )
        
        logger.warning(f"Unsupported extension type: {ext_type}")
        return None
```

### 验收标准

- [ ] Transfer Fee 扩展正常工作
- [ ] Default Account State 扩展正常工作
- [ ] 扩展配置验证正确
- [ ] 所有扩展测试通过

## 第四阶段：完善和测试

### 目标
完善整个系统，添加完整的测试覆盖，优化性能。

### 任务清单

#### 4.1 完善费用估算

```bash
# 创建 Solana 费用估算
touch waas2/tokenization/controllers/solana/estimate_fee.py
```

`waas2/tokenization/controllers/solana/estimate_fee.py`:
```python
import logging
from typing import Any
from solana.rpc.api import Client
from waas2.tokenization.utils.solana_contract import SolanaToken2022Helper

logger = logging.getLogger("waas2.tokenization")

class SolanaEstimateFeeController:
    """Solana 费用估算控制器"""
    
    @classmethod
    def estimate_deploy_fee(cls, params) -> Any:
        """估算部署费用"""
        chain_id = params.chain_id
        client = SolanaToken2022Helper.get_rpc_client(chain_id)
        
        # 1. 计算账户租金
        mint_space = cls._calculate_mint_space(params)
        rent_exempt_balance = client.get_minimum_balance_for_rent_exemption(mint_space)
        
        # 2. 计算交易费用
        # Solana 交易费用相对固定，约 5000 lamports
        transaction_fee = 5000
        
        # 3. 计算总费用
        total_fee = rent_exempt_balance["result"] + transaction_fee
        
        return {
            "fee_token": "SOL",
            "fee_amount": str(total_fee),
            "rent_exempt_balance": str(rent_exempt_balance["result"]),
            "transaction_fee": str(transaction_fee)
        }
    
    @classmethod
    def estimate_mint_fee(cls, params) -> Any:
        """估算铸造费用"""
        # Mint 操作只需要交易费用
        return {
            "fee_token": "SOL",
            "fee_amount": "5000",  # 约 5000 lamports
            "transaction_fee": "5000"
        }
    
    @classmethod
    def estimate_burn_fee(cls, params) -> Any:
        """估算销毁费用"""
        # Burn 操作只需要交易费用
        return {
            "fee_token": "SOL",
            "fee_amount": "5000",
            "transaction_fee": "5000"
        }
    
    @classmethod
    def _calculate_mint_space(cls, params) -> int:
        """计算 mint 账户所需空间"""
        base_space = 82  # 基础 mint 账户空间
        
        # 为每个扩展添加额外空间
        token_params = params.token_params.actual_instance
        extensions = getattr(token_params, 'extensions', [])
        
        for extension in extensions:
            ext_type = extension.get("type")
            if ext_type == "TRANSFER_FEE":
                base_space += 108
            elif ext_type == "DEFAULT_ACCOUNT_STATE":
                base_space += 1
            # 添加其他扩展的空间计算
        
        return base_space
```

#### 4.2 添加完整的测试套件

```bash
# 创建测试目录结构
mkdir -p waas2/tokenization/tests/solana
touch waas2/tokenization/tests/solana/__init__.py
touch waas2/tokenization/tests/solana/test_manager.py
touch waas2/tokenization/tests/solana/test_instructions.py
touch waas2/tokenization/tests/solana/test_extensions.py
touch waas2/tokenization/tests/solana/test_estimate_fee.py
```

#### 4.3 集成测试

```python
# waas2/tokenization/tests/test_integration_solana.py
class SolanaIntegrationTest(TestCase):
    """Solana 集成测试"""
    
    def setUp(self):
        self.chain_id = "SOL_DEVNET"
        self.org_id = "test_org"
        self.biz_org_id = 12345
    
    def test_full_token_lifecycle(self):
        """测试完整的代币生命周期"""
        # 1. 部署代币
        deploy_params = self._create_deploy_params()
        activity_id = self._deploy_token(deploy_params)
        
        # 2. 铸造代币
        mint_params = self._create_mint_params()
        mint_activity_id = self._mint_token(mint_params)
        
        # 3. 销毁代币
        burn_params = self._create_burn_params()
        burn_activity_id = self._burn_token(burn_params)
        
        # 验证所有操作成功
        self.assertIsNotNone(activity_id)
        self.assertIsNotNone(mint_activity_id)
        self.assertIsNotNone(burn_activity_id)
    
    def test_token_with_extensions(self):
        """测试带扩展的代币"""
        deploy_params = self._create_deploy_params_with_extensions()
        activity_id = self._deploy_token(deploy_params)
        self.assertIsNotNone(activity_id)
```

#### 4.4 性能优化

1. **连接池优化**：为 Solana RPC 客户端添加连接池
2. **缓存优化**：缓存常用的配置和计算结果
3. **异步处理**：对于非关键路径使用异步处理

#### 4.5 监控和日志

```python
# 添加详细的日志记录
import logging

logger = logging.getLogger("waas2.tokenization.solana")

class SolanaTokenizationManager:
    def deploy(self, params, org_id, biz_org_id, api_request_info, sign_info) -> str:
        logger.info(f"Starting Solana token deployment for org {org_id}")
        
        try:
            # 部署逻辑
            result = self._execute_deploy(params)
            logger.info(f"Solana token deployment successful: {result}")
            return result
        except Exception as e:
            logger.error(f"Solana token deployment failed: {str(e)}")
            raise
```

### 验收标准

- [ ] 代码覆盖率 > 90%
- [ ] 所有单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试满足要求
- [ ] 现有功能回归测试通过

## 部署和发布

### 预发布检查清单

- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 文档更新完成
- [ ] 配置文件更新
- [ ] 依赖项检查
- [ ] 安全审查完成

### 发布步骤

1. **灰度发布**：先在测试环境部署
2. **功能验证**：验证所有功能正常
3. **性能测试**：确保性能满足要求
4. **生产部署**：逐步在生产环境部署
5. **监控观察**：密切监控系统状态

### 回滚计划

如果发现问题，按以下步骤回滚：

1. **立即停止新功能**：通过配置开关禁用 Solana 功能
2. **数据一致性检查**：确保数据没有损坏
3. **代码回滚**：如有必要，回滚到上一个稳定版本
4. **问题分析**：分析问题原因并制定修复计划

## 维护和监控

### 关键指标监控

1. **功能指标**
   - Solana 交易成功率
   - API 响应时间
   - 错误率

2. **性能指标**
   - 系统吞吐量
   - 内存使用率
   - CPU 使用率

3. **业务指标**
   - 代币创建数量
   - 交易量
   - 用户活跃度

### 日常
维护任务

1. **定期检查**
   - 依赖库版本更新
   - Solana 网络状态
   - 配置文件有效性

2. **性能优化**
   - 定期分析性能瓶颈
   - 优化数据库查询
   - 调整缓存策略

3. **安全更新**
   - 及时更新安全补丁
   - 定期安全审计
   - 密钥轮换

### 故障处理

#### 常见问题及解决方案

1. **Solana RPC 连接失败**
   ```python
   # 检查网络连接和端点配置
   # 实现重试机制和备用端点
   ```

2. **交易失败**
   ```python
   # 检查账户余额
   # 验证指令参数
   # 重试机制
   ```

3. **扩展配置错误**
   ```python
   # 验证扩展配置格式
   # 检查权限设置
   ```

## 附录

### A. 配置文件模板

#### A.1 Solana 配置

```python
# waas2/tokenization/settings/solana.py
SOLANA_TOKEN_2022_CONFIG = {
    "SOL": {
        "program_id": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb",
        "rpc_endpoint": "https://api.mainnet-beta.solana.com",
        "commitment": "confirmed",
        "timeout": 30
    },
    "SOL_DEVNET": {
        "program_id": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb",
        "rpc_endpoint": "https://api.devnet.solana.com",
        "commitment": "confirmed",
        "timeout": 30
    },
    "SOL_TESTNET": {
        "program_id": "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb",
        "rpc_endpoint": "https://api.testnet.solana.com",
        "commitment": "confirmed",
        "timeout": 30
    }
}

# 扩展配置
SOLANA_EXTENSIONS_CONFIG = {
    "TRANSFER_FEE": {
        "max_basis_points": 10000,  # 100%
        "max_fee": **********,      # 1 SOL
    },
    "DEFAULT_ACCOUNT_STATE": {
        "allowed_states": ["uninitialized", "initialized", "frozen"]
    }
}
```

### B. API 接口示例

#### B.1 部署 Solana Token 2022

```json
POST /tokenization/tokens

{
  "chain_id": "SOL_DEVNET",
  "token_params": {
    "standard": "SPL_TOKEN_2022",
    "name": "My Solana Token",
    "symbol": "MST",
    "decimals": 9,
    "permissions": {
      "mint_authority": "********************************",
      "freeze_authority": "********************************"
    },
    "extensions": [
      {
        "type": "TRANSFER_FEE",
        "config": {
          "transfer_fee_basis_points": 100,
          "maximum_fee": 1000000,
          "withdraw_withheld_authority": "********************************"
        }
      }
    ]
  },
  "source": {
    "source_type": "ORG_CONTROLLED",
    "wallet_id": "wallet_123",
    "address": "********************************"
  }
}
```

#### B.2 铸造代币

```json
POST /tokenization/tokens/{token_id}/mint

{
  "amount": "**********",
  "destination": "22222222222222222222222222222222",
  "source": {
    "source_type": "ORG_CONTROLLED",
    "wallet_id": "wallet_123",
    "address": "********************************"
  }
}
```

### C. 测试数据

#### C.1 测试用的 Solana 地址

```python
# 测试用地址（Devnet）
TEST_ADDRESSES = {
    "mint_authority": "********************************",
    "freeze_authority": "22222222222222222222222222222222",
    "test_wallet": "********************************",
    "destination": "44444444444444444444444444444444"
}
```

#### C.2 测试用扩展配置

```python
# Transfer Fee 扩展测试配置
TRANSFER_FEE_TEST_CONFIG = {
    "type": "TRANSFER_FEE",
    "config": {
        "transfer_fee_basis_points": 100,  # 1%
        "maximum_fee": 1000000,            # 0.001 SOL
        "withdraw_withheld_authority": "********************************"
    }
}

# Default Account State 扩展测试配置
DEFAULT_ACCOUNT_STATE_TEST_CONFIG = {
    "type": "DEFAULT_ACCOUNT_STATE",
    "config": {
        "state": "frozen"
    }
}
```

### D. 故障排除指南

#### D.1 常见错误代码

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| `InvalidInstruction` | 指令格式错误 | 检查指令参数和格式 |
| `InsufficientFunds` | 余额不足 | 检查账户余额 |
| `InvalidAccountData` | 账户数据无效 | 验证账户状态 |
| `ExtensionNotSupported` | 不支持的扩展 | 检查扩展配置 |

#### D.2 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.getLogger("waas2.tokenization.solana").setLevel(logging.DEBUG)
   ```

2. **使用 Solana CLI 验证**
   ```bash
   solana account <mint_address> --url devnet
   ```

3. **检查交易状态**
   ```bash
   solana confirm <transaction_signature> --url devnet
   ```

### E. 性能基准

#### E.1 预期性能指标

| 操作 | 响应时间 | 吞吐量 |
|------|----------|--------|
| 部署代币 | < 3s | 10 TPS |
| 铸造代币 | < 2s | 50 TPS |
| 销毁代币 | < 2s | 50 TPS |
| 费用估算 | < 500ms | 100 TPS |

#### E.2 性能测试脚本

```python
# waas2/tokenization/tests/performance/test_solana_performance.py
import time
import concurrent.futures
from django.test import TestCase

class SolanaPerformanceTest(TestCase):
    def test_concurrent_mint_operations(self):
        """测试并发铸造操作性能"""
        def mint_operation():
            start_time = time.time()
            # 执行铸造操作
            result = self._execute_mint()
            end_time = time.time()
            return end_time - start_time, result
        
        # 并发执行 10 个铸造操作
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(mint_operation) for _ in range(10)]
            results = [future.result() for future in futures]
        
        # 验证性能指标
        avg_time = sum(r[0] for r in results) / len(results)
        self.assertLess(avg_time, 2.0)  # 平均响应时间 < 2s
```

## 总结

本实施指南提供了完整的 Solana Token 2022 支持实施路径，包括：

1. **分阶段实施**：降低风险，确保每个阶段都能独立验证
2. **详细的代码示例**：提供具体的实现参考
3. **完整的测试策略**：确保代码质量和功能正确性
4. **运维和监控**：保障系统稳定运行
5. **故障处理**：快速定位和解决问题

通过遵循这个指南，开发团队可以安全、高效地实施 Solana Token 2022 支持，同时保持现有系统的稳定性。

## 下一步行动

1. **组建实施团队**：分配开发、测试、运维角色
2. **制定时间计划**：根据团队资源制定详细的时间表
3. **准备开发环境**：搭建 Solana 开发和测试环境
4. **开始第一阶段**：从基础架构搭建开始实施

建议按照本指南的阶段顺序进行实施，确保每个阶段都经过充分的测试和验证后再进入下一阶段。