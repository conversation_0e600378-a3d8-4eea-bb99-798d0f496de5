import logging

import cobo_waas2
from cobo_libs.api.decorator.drf import cobo_extend_schema
from cobo_libs.api.decorator.drf.drf import nowrap_serializer
from django.db import transaction

from waas2.authentications.universal_access_control.bo.target_info import (
    InstanceParmType,
)
from waas2.authentications.universal_access_control.decorators.check_permission_scope import (
    check_permission_scope,
)
from waas2.authentications.universal_access_control.helpers.org_ids import get_org_ids
from waas2.authentications.universal_access_control.permissions.operations import (
    PermissionOperation,
)
from waas2.authentications.universal_access_control.permissions.resources import (
    PermissionResource,
)
from waas2.devapi.uac_base_view2 import UacBaseAPIViewV2
from waas2.tokenization.controllers.allowlist import TokenizationAllowlistController
from waas2.tokenization.data.params import ListAllowlistAddressesParam
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationAllowlistView(UacBaseAPIViewV2):
    @cobo_extend_schema(
        tags=["Tokenization"],
        parameter=ListAllowlistAddressesParam,
        responses=cobo_waas2.TokenizationAllowlistAddressesResponse,
        response_schema_wrapper=nowrap_serializer,
    )
    def get(self, request, token_id):
        """获取代币白名单地址列表"""
        params: ListAllowlistAddressesParam = request.validated_data
        org_id, biz_org_id = get_org_ids(request)

        return TokenizationAllowlistController.list_allowlist_addresses(
            token_id=token_id,
            org_id=org_id,
            biz_org_id=biz_org_id,
            params=params,
        )

    @cobo_extend_schema(
        tags=["Tokenization"],
        request=cobo_waas2.TokenizationUpdateAllowlistAddressesRequest,
        responses=cobo_waas2.TokenizationOperationResponse,
        response_schema_wrapper=nowrap_serializer,
    )
    @check_permission_scope(
        permission_resource=PermissionResource.TRANSACTION,
        permission_operation=PermissionOperation.CONTRACT_CALL,
        instance_parm_type=InstanceParmType.WALLET_ID,
        get_instance_parm_value_func=TokenizationUtils.get_source_wallet_id_func,
    )
    @transaction.atomic()
    def post(self, request, token_id):
        """更新代币白名单地址"""
        params: cobo_waas2.TokenizationUpdateAllowlistAddressesRequest = (
            request.validated_data
        )
        org_id, biz_org_id = get_org_ids(request)
        sign_info = getattr(request, "sign_info", None)
        api_request_info = {
            "headers": dict(getattr(request, "headers", {})),
            "meta": dict(getattr(request, "META", {})),
        }

        return TokenizationAllowlistController.update_allowlist_addresses(
            token_id=token_id,
            org_id=org_id,
            biz_org_id=biz_org_id,
            params=params,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )


class TokenizationAllowlistActivationView(UacBaseAPIViewV2):
    @cobo_extend_schema(
        tags=["Tokenization"],
        responses=cobo_waas2.GetTokenizationAllowlistActivation200Response,
        response_schema_wrapper=nowrap_serializer,
    )
    def get(self, request, token_id):
        """获取代币白名单开关配置"""
        org_id, biz_org_id = get_org_ids(request)
        return TokenizationAllowlistController.get_token_access_activation(
            token_id=token_id,
            org_id=org_id,
            biz_org_id=biz_org_id,
        )

    @cobo_extend_schema(
        tags=["Tokenization"],
        request=cobo_waas2.TokenizationAllowlistActivationRequest,
        responses={201: cobo_waas2.TokenizationOperationResponse},
        response_schema_wrapper=nowrap_serializer,
    )
    @check_permission_scope(
        permission_resource=PermissionResource.TRANSACTION,
        permission_operation=PermissionOperation.CONTRACT_CALL,
        instance_parm_type=InstanceParmType.WALLET_ID,
        get_instance_parm_value_func=TokenizationUtils.get_source_wallet_id_func,
    )
    @transaction.atomic()
    def post(self, request, token_id):
        """
        更新白名单开关
        """
        params: cobo_waas2.TokenizationAllowlistActivationRequest = (
            request.validated_data
        )
        sign_info = getattr(request, "sign_info", None)
        api_request_info = {
            "headers": dict(getattr(request, "headers", {})),
            "meta": dict(getattr(request, "META", {})),
        }
        org_id, biz_org_id = get_org_ids(request)

        return TokenizationAllowlistController.update_token_access_activation(
            token_id=token_id,
            org_id=org_id,
            biz_org_id=biz_org_id,
            params=params,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )
