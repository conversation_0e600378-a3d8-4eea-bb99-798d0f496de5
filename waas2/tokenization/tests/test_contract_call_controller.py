import unittest
import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.controllers.contract_call import (
    TokenizationContractCallController,
)
from waas2.tokenization.dao.tokenization import (
    ActivityDao,
    TokenBlocklistDao,
    TokenDao,
    TokenRoleDao,
)
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.tests.test_base import TokenizationTestBase
from waas2.tokenization.utils.selector import CoboERC20SelectorUtils


class TestTokenizationContractCallController(TokenizationTestBase):
    """测试合约调用控制器"""

    def setUp(self):
        super().setUp()
        # 创建真实的测试数据
        self.token = self.create_mock_token()
        self.token_role = self.create_mock_token_role()

    def create_mock_token(self):
        """创建mock Token对象"""
        token = TokenDao.create(
            uuid=str(uuid.uuid4()),
            org_id=self.org_id,
            token_id=self.token_id,
            chain_id=self.chain_id,
            token_address=self.address,
            status=cobo_waas2.TokenizationStatus.ACTIVE.value,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            extra={"token_access_activated": False},
        )
        return token

    def create_mock_token_role(self):
        """创建mock TokenRole对象"""
        role = TokenRoleDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address=self.address,
            roles=TokenizationRole.ADMIN.value,
        )
        return role

    def create_contract_call_request(self, calldata: str):
        """创建合约调用请求"""
        return cobo_waas2.TokenizationContractCallRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            type="EVM_Contract",  # 使用正确的字符串值
            data=cobo_waas2.TokenizationContractCallParamsData(
                actual_instance=cobo_waas2.TokenizationEvmContractCallParams(
                    calldata=calldata, value="0"  # 默认值为0
                )
            ),
            fee=self.create_test_fee(),
        )

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.contract_call_for_dev_api"
    )
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_view_method_success(
        self,
        mock_get_contract,
        mock_contract_call,
        mock_query_wallet,
        mock_validate_source,
    ):
        """测试成功调用只读方法"""
        # 创建只读方法调用请求 (name方法)
        calldata = f"0x{CoboERC20SelectorUtils.COBO_ERC20_SELECTORS['name']}"
        contract_call_request = self.create_contract_call_request(calldata)

        # Mock 钱包验证
        mock_validate_source.return_value = None
        mock_query_wallet.return_value = self.create_test_wallet()

        # Mock 合约调用
        mock_tx = MagicMock()
        mock_tx.request_id = "test_request_id"
        mock_tx.transaction_id = "test_tx_id"
        mock_contract_call.return_value = mock_tx

        # Mock 合约解码 - 确保能正确解码 name() 方法
        mock_contract = MagicMock()
        mock_contract.decode_function_input.return_value = (
            MagicMock(),  # function
            {},  # params - name() 方法没有参数
        )
        mock_get_contract.return_value = mock_contract

        # 调用方法
        response = TokenizationContractCallController.contract_call(
            self.token_id,
            self.org_id,
            self.biz_org_id,
            contract_call_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertIsNotNone(response.activity_id)

        # 验证数据库中创建了Activity记录
        activities = ActivityDao.list_by_token_and_org(self.token_id, self.org_id)
        self.assertGreater(len(activities), 0)

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.contract_call_for_dev_api"
    )
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_mint_success(
        self,
        mock_get_contract,
        mock_contract_call,
        mock_query_wallet,
        mock_validate_source,
    ):
        """测试成功调用mint方法"""
        # 创建具有MINTER权限的角色，使用不同的地址避免重复
        minter_address = "******************************************"
        TokenRoleDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address=minter_address,
            roles=TokenizationRole.MINTER.value,
        )

        # Mock 钱包验证
        mock_validate_source.return_value = None
        mock_query_wallet.return_value = self.create_test_wallet()

        # Mock 合约调用
        mock_tx = MagicMock()
        mock_tx.request_id = "test_request_id"
        mock_tx.transaction_id = "test_tx_id"
        mock_contract_call.return_value = mock_tx

        # Mock 合约解码
        mock_contract = MagicMock()
        mock_contract.decode_function_input.return_value = (
            MagicMock(),  # function
            {"to": minter_address, "amount": 1000},  # params
        )
        mock_get_contract.return_value = mock_contract

        # 创建mint方法调用请求，使用有权限的地址
        # mint(address to, uint256 amount) 的ABI编码
        address_hex = minter_address[2:].lower().zfill(64)  # 地址需要填充到64位
        amount_hex = hex(1000)[2:].zfill(64)  # 数量1000转换为hex并填充到64位
        calldata = f"0x{CoboERC20SelectorUtils.COBO_ERC20_SELECTORS['mint']}{address_hex}{amount_hex}"
        contract_call_request = cobo_waas2.TokenizationContractCallRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=cobo_waas2.TokenizationMpcOperationSource(
                    source_type=cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED,
                    wallet_id=self.wallet_id,
                    address=minter_address,  # 使用有权限的地址
                )
            ),
            type="EVM_Contract",
            data=cobo_waas2.TokenizationContractCallParamsData(
                actual_instance=cobo_waas2.TokenizationEvmContractCallParams(
                    calldata=calldata, value="0"
                )
            ),
            fee=self.create_test_fee(),
        )

        # 调用方法
        response = TokenizationContractCallController.contract_call(
            self.token_id,
            self.org_id,
            self.biz_org_id,
            contract_call_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertIsNotNone(response.activity_id)

    def test_contract_call_token_not_found(self):
        """测试Token不存在的情况"""
        calldata = f"0x{CoboERC20SelectorUtils.COBO_ERC20_SELECTORS['name']}"
        contract_call_request = self.create_contract_call_request(calldata)

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationContractCallController.contract_call(
                "non_existent_token",
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("is not issued", str(context.exception))

    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_invalid_calldata_format(self, mock_get_contract):
        """测试无效的calldata格式"""
        # Mock 合约
        mock_contract = MagicMock()
        mock_get_contract.return_value = mock_contract

        # 测试无效格式的calldata
        invalid_calldatas = [
            "invalid",  # 不是hex格式
            "0x123",  # 长度不足
            "",  # 空字符串
        ]

        for invalid_calldata in invalid_calldatas:
            contract_call_request = self.create_contract_call_request(invalid_calldata)

            with self.assertRaises(InvalidParamException) as context:
                TokenizationContractCallController.contract_call(
                    self.token_id,
                    self.org_id,
                    self.biz_org_id,
                    contract_call_request,
                    sign_info=dict(key="test_api_key", signature="test_signature"),
                    api_request_info=dict(request_id="test_request"),
                )

            self.assertIn("Invalid calldata format", str(context.exception))

    @unittest.skip("deprecated")
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_unsupported_selector(self, mock_get_contract):
        """测试不支持的方法选择器"""
        # Mock 合约
        mock_contract = MagicMock()
        mock_get_contract.return_value = mock_contract

        # 使用不支持的selector
        unsupported_calldata = "0x12345678"  # 不在允许列表中的selector
        contract_call_request = self.create_contract_call_request(unsupported_calldata)

        with self.assertRaises(InvalidParamException) as context:
            TokenizationContractCallController.contract_call(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("is not allowed for token contracts", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_insufficient_permissions(
        self, mock_get_contract, mock_validate_source
    ):
        """测试权限不足的情况"""
        # Mock validate_source
        mock_validate_source.return_value = None

        # Mock 合约解码 - 必须在calldata构造之前设置
        mock_contract = MagicMock()
        mock_contract.decode_function_input.return_value = (
            MagicMock(),  # function
            {"to": self.address, "amount": 1000},  # params
        )
        mock_get_contract.return_value = mock_contract

        # 尝试调用需要MINTER权限的mint方法，但用户只有ADMIN权限
        # 使用与成功测试相同的calldata格式
        calldata = f"0x{CoboERC20SelectorUtils.COBO_ERC20_SELECTORS['mint']}000000000000000000000000{self.address[2:]}00000000000000000000000000000000000000000000000000000000000003e8"
        contract_call_request = self.create_contract_call_request(calldata)

        with self.assertRaises(InvalidParamException) as context:
            TokenizationContractCallController.contract_call(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("does not have required role", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_transfer_with_blocklist(
        self, mock_get_contract, mock_validate_source
    ):
        """测试转账时黑名单限制"""
        # Mock validate_source
        mock_validate_source.return_value = None

        # 将目标地址加入黑名单
        target_address = "******************************************"
        TokenBlocklistDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address=target_address,
            valid=True,
        )

        # Mock 合约解码
        mock_contract = MagicMock()
        mock_contract.decode_function_input.return_value = (
            MagicMock(),  # function
            {"to": target_address, "amount": 1000},  # params
        )
        mock_get_contract.return_value = mock_contract

        # 创建transfer调用请求
        # transfer(address to, uint256 amount) 的ABI编码
        address_hex = target_address[2:].lower().zfill(64)  # 地址需要填充到64位
        amount_hex = format(1000, "064x")  # 数量1000转换为hex并填充到64位
        transfer_selector = str(
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["transfer"]
        )  # 确保selector是字符串
        calldata = "0x" + transfer_selector + address_hex + amount_hex
        contract_call_request = self.create_contract_call_request(calldata)

        with self.assertRaises(InvalidParamException) as context:
            TokenizationContractCallController.contract_call(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("addresses are blocked", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_transfer_with_allowlist(
        self, mock_get_contract, mock_validate_source
    ):
        """测试转账时白名单限制"""
        # Mock validate_source
        mock_validate_source.return_value = None

        # 启用白名单 - 直接更新token的extra字段
        self.token.extra = {"token_access_activated": True}
        self.token.save()

        # 目标地址不在白名单中
        target_address = "******************************************"

        # Mock 合约解码
        mock_contract = MagicMock()
        mock_contract.decode_function_input.return_value = (
            MagicMock(),  # function
            {"to": target_address, "amount": 1000},  # params
        )
        mock_get_contract.return_value = mock_contract

        # 创建transfer调用请求
        # transfer(address to, uint256 amount) 的ABI编码
        address_hex = target_address[2:].lower().zfill(64)  # 地址需要填充到64位
        amount_hex = format(1000, "064x")  # 数量1000转换为hex并填充到64位
        transfer_selector = str(
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["transfer"]
        )  # 确保selector是字符串
        calldata = "0x" + transfer_selector + address_hex + amount_hex
        contract_call_request = self.create_contract_call_request(calldata)

        with self.assertRaises(InvalidParamException) as context:
            TokenizationContractCallController.contract_call(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("not in allowlist", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.contract_call_for_dev_api"
    )
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_multicall_success(
        self,
        mock_get_contract,
        mock_contract_call,
        mock_query_wallet,
        mock_validate_source,
    ):
        """测试成功调用multicall方法"""
        # 创建具有MANAGER和PAUSER权限的角色，使用不同地址
        manager_address = "******************************************"
        TokenRoleDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address=manager_address,
            roles=TokenizationRole.MANAGER.value | TokenizationRole.PAUSER.value,
        )

        # Mock 钱包验证
        mock_validate_source.return_value = None
        mock_query_wallet.return_value = self.create_test_wallet()

        # Mock 合约调用
        mock_tx = MagicMock()
        mock_tx.request_id = "test_request_id"
        mock_tx.transaction_id = "test_tx_id"
        mock_contract_call.return_value = mock_tx

        # Mock 合约解码
        mock_contract = MagicMock()

        # 构造嵌套的pause调用
        nested_calldata = "0x" + str(
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["pause"]
        )

        # 第一次调用解码multicall
        mock_contract.decode_function_input.side_effect = [
            (MagicMock(), {"data": [nested_calldata]}),  # multicall解码，使用字符串而不是bytes
            (MagicMock(), {}),  # pause解码
        ]
        mock_get_contract.return_value = mock_contract

        # 创建multicall调用请求，使用有权限的地址
        calldata = "0x" + str(CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"])
        contract_call_request = cobo_waas2.TokenizationContractCallRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=cobo_waas2.TokenizationMpcOperationSource(
                    source_type=cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED,
                    wallet_id=self.wallet_id,
                    address=manager_address,  # 使用有权限的地址
                )
            ),
            type="EVM_Contract",
            data=cobo_waas2.TokenizationContractCallParamsData(
                actual_instance=cobo_waas2.TokenizationEvmContractCallParams(
                    calldata=calldata, value="0"
                )
            ),
            fee=self.create_test_fee(),
        )

        # 调用方法
        response = TokenizationContractCallController.contract_call(
            self.token_id,
            self.org_id,
            self.biz_org_id,
            contract_call_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertIsNotNone(response.activity_id)

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_multicall_nested_multicall_forbidden(
        self, mock_get_contract, mock_validate_source
    ):
        """测试multicall中嵌套multicall被禁止"""
        # Mock validate_source
        mock_validate_source.return_value = None

        # Mock 合约解码
        mock_contract = MagicMock()

        # 构造嵌套的multicall调用
        nested_calldata = "0x" + str(
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"]
        )

        # 第一次调用解码multicall
        mock_contract.decode_function_input.side_effect = [
            (MagicMock(), {"data": [nested_calldata]}),  # multicall解码，使用字符串
        ]
        mock_get_contract.return_value = mock_contract

        # 创建multicall调用请求
        calldata = "0x" + str(CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"])
        contract_call_request = self.create_contract_call_request(calldata)

        with self.assertRaises(InvalidParamException) as context:
            TokenizationContractCallController.contract_call(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("Nested multicall is not allowed", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.contract_call_for_dev_api"
    )
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_with_web3_source(
        self,
        mock_get_contract,
        mock_contract_call,
        mock_query_wallet,
        mock_validate_source,
    ):
        """测试使用Web3源进行合约调用"""
        # 创建Web3源的合约调用请求
        calldata = "0x" + str(CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["name"])

        # Mock 钱包验证
        mock_validate_source.return_value = None
        mock_query_wallet.return_value = self.create_test_wallet()

        # Mock 合约调用
        mock_tx = MagicMock()
        mock_tx.request_id = "test_request_id"
        mock_tx.transaction_id = "test_tx_id"
        mock_contract_call.return_value = mock_tx

        # Mock 合约解码 - 确保能正确解码 name() 方法
        mock_contract = MagicMock()
        mock_contract.decode_function_input.return_value = (
            MagicMock(),  # function
            {},  # params - name() 方法没有参数
        )
        mock_get_contract.return_value = mock_contract
        contract_call_request = cobo_waas2.TokenizationContractCallRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source(
                    cobo_waas2.TokenizationOperationSourceType.WEB3
                )
            ),
            type="EVM_Contract",
            data=cobo_waas2.TokenizationContractCallParamsData(
                actual_instance=cobo_waas2.TokenizationEvmContractCallParams(
                    calldata=calldata, value="0"
                )
            ),
            fee=self.create_test_fee(),
        )

        # 调用方法
        response = TokenizationContractCallController.contract_call(
            self.token_id,
            self.org_id,
            self.biz_org_id,
            contract_call_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertIsNotNone(response.activity_id)

    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_decode_error(self, mock_get_contract):
        """测试calldata解码错误"""
        # Mock 合约解码抛出异常
        mock_contract = MagicMock()
        mock_contract.decode_function_input.side_effect = Exception("Decode error")
        mock_get_contract.return_value = mock_contract

        # 创建调用请求
        calldata = (
            f"0x{CoboERC20SelectorUtils.COBO_ERC20_SELECTORS['name']}invalid_data"
        )
        contract_call_request = self.create_contract_call_request(calldata)

        with self.assertRaises(InvalidParamException) as context:
            TokenizationContractCallController.contract_call(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("Invalid calldata format", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch("waas2.tokenization.utils.contract.CoboERC20ContractHelper.get_contract")
    def test_contract_call_wallet_validation_failure(
        self, mock_get_contract, mock_query_wallet, mock_validate_source
    ):
        """测试钱包验证失败"""
        # Mock validate_source通过
        mock_validate_source.return_value = None

        # Mock 合约解码成功，这样才能到达钱包验证阶段
        mock_contract = MagicMock()
        mock_contract.decode_function_input.return_value = (
            MagicMock(),  # function
            {},  # params - name() 方法没有参数
        )
        mock_get_contract.return_value = mock_contract

        # Mock query_and_check_wallet抛出异常
        from cobo_libs.api.restful.exceptions import ApiInvalidParamException

        mock_query_wallet.side_effect = ApiInvalidParamException("Wallet not found")

        calldata = f"0x{CoboERC20SelectorUtils.COBO_ERC20_SELECTORS['name']}"
        contract_call_request = self.create_contract_call_request(calldata)

        with self.assertRaises(
            (InvalidParamException, ApiInvalidParamException)
        ) as context:
            TokenizationContractCallController.contract_call(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                contract_call_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("Wallet not found", str(context.exception))
