import uuid
from unittest.mock import Mock, patch

import cobo_waas2

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.controllers.deploy import TokenizationDeployController
from waas2.tokenization.dao.tokenization import TokenDao
from waas2.tokenization.managers.deploy import TokenizationDeployManager
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestTokenizationDeployController(TokenizationTestBase):
    """测试部署控制器"""

    def setUp(self):
        """重写setUp，因为部署测试不需要预先存在的代币"""
        super().setUp()  # 调用父类的setUp方法
        # 可以覆盖父类的设置
        self.org_id = "test_org_123"
        self.biz_org_id = 12345
        self.chain_id = "SETH"
        self.wallet_id = "wallet_123"
        self.address = "******************************************"

        # 创建测试钱包数据
        self.create_test_wallet()

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.managers.deploy.TokenizationDeployManager.deploy")
    def test_deploy_token_success(self, mock_deploy, mock_validate_source):
        """测试成功部署代币"""
        # Mock 钱包验证
        mock_validate_source.return_value = None
        # Mock 部署管理器返回活动 ID
        mock_deploy.return_value = "test_activity_id"

        # 创建代币参数（暂时不包含permissions以便测试基本功能）
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["0xadmin123456789012345678901234567890123456"],
                minter=[],
                burner=[],
            ),
        )

        # 创建部署请求
        deploy_request = cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=cobo_waas2.TransactionRequestFee(
                fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
                token_id="ETH",
            ),
        )

        # 调用方法
        response = TokenizationDeployController.deploy_token(
            self.org_id,
            self.biz_org_id,
            deploy_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertEqual(response.activity_id, "test_activity_id")

        # 验证部署管理器被调用
        mock_deploy.assert_called_once()

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    def test_deploy_token_invalid_wallet(self, mock_validate_source):
        """测试使用无效钱包部署代币"""
        # Mock validate_source
        mock_validate_source.side_effect = InvalidParamException("Wallet not found")

        # 创建代币参数
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["0xadmin123456789012345678901234567890123456"],
                minter=[],
                burner=[],
            ),
        )

        # 创建部署请求
        deploy_request = cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=cobo_waas2.TransactionRequestFee(
                fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
                token_id="ETH",
            ),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationDeployController.deploy_token(
                self.org_id,
                self.biz_org_id,
                deploy_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("Wallet not found", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    def test_deploy_token_invalid_wallet_type(self, mock_validate_source):
        """测试使用不支持的钱包类型部署代币"""
        # Mock validate_source抛出钱包类型错误
        mock_validate_source.side_effect = InvalidParamException("not an MPC wallet")

        # 创建代币参数
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["0xadmin123456789012345678901234567890123456"],
                minter=[],
                burner=[],
            ),
        )

        # 创建部署请求
        deploy_request = cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=cobo_waas2.TransactionRequestFee(
                fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
                token_id="ETH",
            ),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationDeployController.deploy_token(
                self.org_id,
                self.biz_org_id,
                deploy_request,
                sign_info=dict(key="test_api_key", signature="test_signature"),
                api_request_info=dict(request_id="test_request"),
            )

        self.assertIn("not an MPC wallet", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.managers.deploy.TokenizationDeployManager.deploy")
    def test_deploy_token_duplicate_symbol(self, mock_deploy, mock_validate_source):
        """测试部署重复符号的代币"""
        # 先创建一个代币
        TokenDao.create(
            uuid=str(uuid.uuid4()),
            org_id=self.org_id,
            chain_id=self.chain_id,
            symbol="TEST",
            name="Existing Token",
            decimals=18,
            status=cobo_waas2.TokenizationStatus.ACTIVE.value,
            extra={},
        )

        # Mock 钱包验证
        mock_validate_source.return_value = None
        # Mock 部署管理器返回活动 ID
        mock_deploy.return_value = "test_activity_id_duplicate"

        # 创建重复符号的代币参数
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token 2",
            symbol="TEST",  # 重复的符号
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["0xadmin123456789012345678901234567890123456"],
                minter=[],
                burner=[],
            ),
        )

        # 创建部署请求
        deploy_request = cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=cobo_waas2.TransactionRequestFee(
                fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
                token_id="ETH",
            ),
        )

        # 调用方法 - 应该成功（因为会自动添加后缀）
        response = TokenizationDeployController.deploy_token(
            self.org_id,
            self.biz_org_id,
            deploy_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果 - 应该成功创建
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertEqual(response.activity_id, "test_activity_id_duplicate")

        # 验证部署管理器被调用
        mock_deploy.assert_called_once()

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.managers.deploy.TokenizationDeployManager.deploy")
    def test_deploy_token_no_admin(self, mock_deploy, mock_validate_source):
        """测试部署没有管理员的代币"""
        # Mock 钱包验证
        mock_validate_source.return_value = None
        # Mock 部署管理器返回活动 ID
        mock_deploy.return_value = "test_activity_id_no_admin"

        # 创建没有管理员的代币参数
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=[],  # 没有管理员
                minter=[],
                burner=[],
            ),
        )

        # 创建部署请求
        deploy_request = cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=cobo_waas2.TransactionRequestFee(
                fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
                token_id="ETH",
            ),
        )

        # 调用方法 - 应该成功（因为admin可以为空，会使用默认值）
        response = TokenizationDeployController.deploy_token(
            self.org_id,
            self.biz_org_id,
            deploy_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertEqual(response.activity_id, "test_activity_id_no_admin")

        # 验证部署管理器被调用
        mock_deploy.assert_called_once()

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.managers.deploy.TokenizationDeployManager.deploy")
    def test_deploy_token_invalid_address_format(
        self, mock_deploy, mock_validate_source
    ):
        """测试部署包含无效地址格式的代币"""
        # Mock 钱包验证
        mock_validate_source.return_value = None
        # Mock 部署管理器返回活动 ID
        mock_deploy.return_value = "test_activity_id_invalid_address"

        # 创建包含无效地址的代币参数
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["short"],  # 无效地址 - 太短
                minter=[],
                burner=[],
            ),
        )

        # 创建部署请求
        deploy_request = cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=cobo_waas2.TransactionRequestFee(
                fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
                token_id="ETH",
            ),
        )

        # 调用方法 - 应该成功（地址验证在合约层面进行）
        response = TokenizationDeployController.deploy_token(
            self.org_id,
            self.biz_org_id,
            deploy_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertEqual(response.activity_id, "test_activity_id_invalid_address")

        # 验证部署管理器被调用
        mock_deploy.assert_called_once()

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.managers.deploy.TokenizationDeployManager.deploy")
    def test_deploy_token_web3_source(self, mock_deploy, mock_validate_source):
        """测试使用Web3钱包部署代币"""
        # Mock 钱包验证
        mock_validate_source.return_value = None
        # Mock 部署管理器返回活动 ID
        mock_deploy.return_value = "test_activity_id_web3"

        # 创建代币参数
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["0xadmin123456789012345678901234567890123456"],
                minter=[],
                burner=[],
            ),
        )

        # 创建部署请求（使用Web3源）
        deploy_request = cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source(
                    cobo_waas2.TokenizationOperationSourceType.WEB3
                )
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=cobo_waas2.TransactionRequestFee(
                fee_type=cobo_waas2.FeeType.EVM_EIP_1559,
                token_id="ETH",
            ),
        )

        # 调用方法
        response = TokenizationDeployController.deploy_token(
            self.org_id,
            self.biz_org_id,
            deploy_request,
            sign_info=dict(key="test_api_key", signature="test_signature"),
            api_request_info=dict(request_id="test_request"),
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertEqual(response.activity_id, "test_activity_id_web3")

        # 验证部署管理器被调用
        mock_deploy.assert_called_once()

    def test_deploy_token_unsupported_source_type(self):
        """测试使用不支持的操作源类型部署代币"""
        # 这个测试由于Pydantic验证的限制，暂时跳过
        # TokenizationTokenOperationSource会在创建时验证类型
        self.skipTest(
            "Pydantic validation prevents unsupported source types at object creation"
        )


class TestGasSponsorSignalHandling(TokenizationTestBase):
    """测试Gas Sponsor信号处理相关功能"""

    def setUp(self):
        """设置测试数据"""
        super().setUp()
        self.org_id = "test_org_123"
        self.biz_org_id = 12345
        self.chain_id = "SETH"
        self.wallet_id = "wallet_123"
        self.address = "******************************************"

    def create_test_deploy_request(self, source_type=None):
        """创建测试部署请求"""
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["0xadmin123456789012345678901234567890123456"],
                minter=[],
                burner=[],
            ),
        )

        return cobo_waas2.TokenizationIssuedTokenRequest(
            chain_id=self.chain_id,
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source(source_type)
            ),
            token_params=cobo_waas2.TokenizationIssueTokenParamsTokenParams(
                actual_instance=token_params
            ),
            fee=self.create_test_fee(),
        )

    def create_mock_activity_transaction(self, action="GAS_SPONSOR", **kwargs):
        """创建Mock活动交易"""
        from waas2.tokenization.models.activity import ActivityTransaction

        tx = Mock(spec=ActivityTransaction)
        tx.id = kwargs.get("id", 1)
        tx.org_id = kwargs.get("org_id", self.org_id)
        tx.activity_id = kwargs.get("activity_id", "activity_123")
        tx.request_id = kwargs.get("request_id", "request_123")
        tx.transaction_id = kwargs.get("transaction_id", "tx_123")
        tx.action = action
        tx.status = kwargs.get("status", 1)
        tx.token_uuid = kwargs.get("token_uuid", "token_123")
        tx.fee = kwargs.get(
            "fee",
            {
                "fee_type": "EVM_EIP_1559",
                "token_id": "ETH",
                "max_fee_per_gas": "25000000000",
                "gas_limit": "21000",
            },
        )
        return tx

    @patch("waas2.tokenization.dao.tokenization.ActivityDao.update_by_id")
    @patch(
        "waas2.tokenization.managers.deploy.TokenizationDeployManager.on_gas_sponsor_on_chained"
    )
    @patch("waas2.tokenization.dao.tokenization.ActivityTransactionDao.update_by_id")
    def test_signal_handler_gas_sponsor_on_chained(
        self, mock_update_tx, mock_on_gas_sponsor, mock_update_activity
    ):
        """测试Gas Sponsor交易上链信号处理"""
        from custody.web3.models.transaction_request import TransactionRequest
        from waas2.tokenization.managers.gas_sponsor import GasSponsorManager
        from waas2.tokenization.signals.tokenization import TokenizationSignalHandler

        # 创建Mock对象
        activity_tx = self.create_mock_activity_transaction(
            action=GasSponsorManager.TRANSACTION_ACTION
        )
        activity = self.create_activity_record()
        activity.type = cobo_waas2.TokenizationOperationType.ISSUE

        tx_request = Mock(spec=TransactionRequest)
        tx_request.org_id = self.biz_org_id
        tx_request.status = 1  # 设置一个状态值

        # 创建token记录 - gas sponsor交易应该有对应的token
        token = self.create_test_token()
        activity.token_uuid = token.uuid

        # 创建信号处理器
        handler = TokenizationSignalHandler(
            tx_request=tx_request,
            eoa_tx=None,
            activity_transaction=activity_tx,
            activity=activity,
            token=token,
        )

        # 调用处理方法
        handler.handle_tx_on_chained()

        # 验证调用
        mock_update_tx.assert_called_once()
        mock_update_activity.assert_called_once()
        # Gas sponsor交易不需要回调，逻辑在sync_gas_sponsor_tx_status.py中处理
        mock_on_gas_sponsor.assert_not_called()

    @patch("waas2.tokenization.dao.tokenization.ActivityDao.update_by_id")
    @patch(
        "waas2.tokenization.managers.deploy.TokenizationDeployManager.on_deploy_on_chained"
    )
    @patch("waas2.tokenization.dao.tokenization.TokenDao.get_by_id_or_raise")
    @patch(
        "waas2.tokenization.managers.auto_listing.AutoListingManager.list_token_for_org"
    )
    @patch("waas2.tokenization.dao.tokenization.ActivityTransactionDao.update_by_id")
    def test_signal_handler_deploy_on_chained(
        self,
        mock_update_tx,
        mock_auto_listing,
        mock_get_token,
        mock_on_deploy,
        mock_update_activity,
    ):
        """测试部署交易上链信号处理"""
        from custody.web3.models.transaction_request import TransactionRequest
        from waas2.tokenization.signals.tokenization import TokenizationSignalHandler

        # 创建Mock对象
        activity_tx = self.create_mock_activity_transaction(
            action=cobo_waas2.TokenizationOperationType.ISSUE
        )
        activity = self.create_activity_record()
        activity.type = cobo_waas2.TokenizationOperationType.ISSUE

        token = self.create_test_token()
        token.chain_id = self.chain_id
        token.token_id = "TEST_TOKEN_TKZ"
        token.token_address = "0xtoken123"

        tx_request = Mock(spec=TransactionRequest)
        tx_request.org_id = self.biz_org_id
        tx_request.status = 1  # 设置一个状态值

        mock_get_token.return_value = token

        # 创建信号处理器 - 修复初始化参数
        handler = TokenizationSignalHandler(
            tx_request=tx_request,
            eoa_tx=None,
            activity_transaction=activity_tx,
            activity=activity,
            token=token,
        )

        # 调用处理方法
        handler.handle_tx_on_chained()

        # 验证调用
        mock_update_tx.assert_called_once()
        mock_update_activity.assert_called_once()
        mock_on_deploy.assert_called_once_with(activity_id=str(activity.uuid))
        mock_get_token.assert_called_once()
        mock_auto_listing.assert_called_once()

    @patch(
        "waas2.tokenization.dao.tokenization.ActivityTransactionDao.list_by_org_id_and_action_and_status"
    )
    def test_gas_sponsor_count_limit_boundary_conditions(self, mock_list_txs):
        """测试Gas Sponsor次数限制的边界条件"""
        from custody.custody.models.custody_wallet import CustodyWallet
        from waas2.tokenization.enums.tokenization import GasSponsorType
        from waas2.tokenization.managers.gas_sponsor import GasSponsorManager

        # 测试边界条件：正好达到限制
        with patch(
            "waas2.tokenization.dao.tokenization.GasSponsorConfigDao.get_by_chain_id"
        ) as mock_get_config:
            config = Mock()
            config.type = GasSponsorType.COUNT
            config.config = {"max_count": 3}
            mock_get_config.return_value = config

            mock_wallet = Mock(spec=CustodyWallet)
            mock_wallet.org.uuid = self.org_id

            fee = self.create_test_fee()

            # 测试正好达到限制（3次）
            mock_list_txs.return_value = [Mock(), Mock(), Mock()]
            result = GasSponsorManager.need_sponsor(self.chain_id, mock_wallet, fee)
            self.assertFalse(result)

            # 测试少于限制（2次）
            mock_list_txs.return_value = [Mock(), Mock()]
            result = GasSponsorManager.need_sponsor(self.chain_id, mock_wallet, fee)
            self.assertTrue(result)

            # 测试超过限制（4次）
            mock_list_txs.return_value = [Mock(), Mock(), Mock(), Mock()]
            result = GasSponsorManager.need_sponsor(self.chain_id, mock_wallet, fee)
            self.assertFalse(result)

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.managers.gas_sponsor.GasSponsorManager.sponsor")
    @patch("custody.wallet.dao.wallet.TokenAssetDao.list_by_custody_wallet_and_asset")
    def test_deploy_gas_sponsor_balance_edge_cases(
        self, mock_list_assets, mock_sponsor, mock_validate_source
    ):
        """测试Gas Sponsor余额边界情况"""
        from custody.custody.dao.custody_wallet import CustodyWalletDao
        from waas2.tokenization.managers.deploy import TokenizationDeployManager

        # Mock验证通过
        mock_validate_source.return_value = None

        # Mock垫付成功
        mock_sponsor_tx = Mock()
        mock_sponsor_tx.request_id = "sponsor_tx_123"
        mock_sponsor_tx.transaction_id = "tx_123"  # 添加transaction_id属性
        mock_sponsor.return_value = mock_sponsor_tx

        # Mock钱包
        mock_wallet = Mock()
        mock_wallet.id = 1
        mock_wallet.org.uuid = self.org_id

        deploy_request = self.create_test_deploy_request()

        # 测试余额为空的情况
        mock_list_assets.return_value = []

        with patch.object(
            CustodyWalletDao, "get_by_uuid", return_value=mock_wallet
        ), patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager._create_token_record"
        ) as mock_create_token, patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager.build_deploy_calldata"
        ) as mock_build_calldata, patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager._predict_contract_address"
        ) as mock_predict_addr, patch(
            "waas2.tokenization.utils.tokenization.TokenizationUtils.create_activity"
        ) as mock_create_activity, patch(
            "waas2.tokenization.utils.tokenization.TokenizationUtils.create_activity_transaction"
        ) as mock_create_activity_tx, patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager._execute_contract_deployment"
        ), patch(
            "waas2.tokenization.dao.tokenization.TokenDao.update_by_id"
        ):
            mock_token = Mock()
            mock_token.id = 1
            mock_token.uuid = "token_123"
            mock_create_token.return_value = mock_token
            mock_build_calldata.return_value = "0x123456"
            mock_predict_addr.return_value = "0xcontract123"

            mock_activity = Mock()
            mock_activity.uuid = "activity_123"
            mock_create_activity.return_value = mock_activity

            # Mock activity transaction
            mock_activity_tx = Mock()
            mock_create_activity_tx.return_value = mock_activity_tx

            result = TokenizationDeployManager.deploy(
                params=deploy_request,
                org_id=self.org_id,
                biz_org_id=self.biz_org_id,
                api_request_info={},
                sign_info={},
            )

        # 验证结果 - 余额为空时应该直接部署（不延迟）
        self.assertEqual(result, "activity_123")

        # 验证垫付交易被创建 - 确保调用时包含了所有必需的参数
        mock_sponsor.assert_called_once()
        mock_create_activity.assert_called_once()
        # 不检查create_activity_transaction的调用次数，因为它可能被调用多次
        self.assertGreaterEqual(mock_create_activity_tx.call_count, 1)

    def test_gas_sponsor_concurrent_requests_simulation(self):
        """测试Gas Sponsor并发请求的模拟场景"""
        from custody.custody.models.custody_wallet import CustodyWallet
        from waas2.tokenization.enums.tokenization import GasSponsorType
        from waas2.tokenization.managers.gas_sponsor import GasSponsorManager

        # 模拟并发场景：多个请求同时检查垫付次数
        with patch(
            "waas2.tokenization.dao.tokenization.GasSponsorConfigDao.get_by_chain_id"
        ) as mock_get_config, patch(
            "waas2.tokenization.dao.tokenization.ActivityTransactionDao.list_by_org_id_and_action_and_status"
        ) as mock_list_txs:
            config = Mock()
            config.type = GasSponsorType.COUNT
            config.config = {"max_count": 5}
            mock_get_config.return_value = config

            mock_wallet = Mock(spec=CustodyWallet)
            mock_wallet.org.uuid = self.org_id

            fee = self.create_test_fee()

            # 模拟当前已有4次垫付记录
            mock_list_txs.return_value = [Mock() for _ in range(4)]

            # 第一个请求应该成功（4 < 5）
            result1 = GasSponsorManager.need_sponsor(self.chain_id, mock_wallet, fee)
            self.assertTrue(result1)

            # 模拟第一个请求创建了垫付记录后，现在有5次记录
            mock_list_txs.return_value = [Mock() for _ in range(5)]

            # 第二个请求应该失败（5 >= 5）
            result2 = GasSponsorManager.need_sponsor(self.chain_id, mock_wallet, fee)
            self.assertFalse(result2)

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch("waas2.tokenization.managers.gas_sponsor.GasSponsorManager.need_sponsor")
    @patch(
        "waas2.tokenization.managers.gas_sponsor.GasSponsorManager.get_gas_sponsor_wallet"
    )
    def test_deploy_integration_with_gas_sponsor_disabled(
        self, mock_get_wallet, mock_need_sponsor, mock_validate_source
    ):
        """测试禁用Gas Sponsor时的集成流程"""
        from waas2.tokenization.managers.deploy import TokenizationDeployManager

        # Mock验证通过
        mock_validate_source.return_value = None

        # Mock Gas Sponsor钱包未配置
        mock_get_wallet.return_value = None
        mock_need_sponsor.return_value = False

        deploy_request = self.create_test_deploy_request()

        with patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager._create_token_record"
        ) as mock_create_token, patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager.build_deploy_calldata"
        ) as mock_build_calldata, patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager._predict_contract_address"
        ) as mock_predict_addr, patch(
            "waas2.tokenization.managers.deploy.TokenizationDeployManager._execute_contract_deployment"
        ) as mock_execute_deploy, patch(
            "waas2.tokenization.utils.tokenization.TokenizationUtils.create_activity"
        ) as mock_create_activity, patch(
            "custody.custody.dao.custody_wallet.CustodyWalletDao.get_by_uuid"
        ) as mock_get_custody_wallet, patch(
            "waas2.tokenization.managers.gas_sponsor.GasSponsorManager.sponsor"
        ) as mock_sponsor, patch(
            "waas2.tokenization.utils.tokenization.TokenizationUtils.create_activity_transaction"
        ) as mock_create_activity_tx, patch(
            "waas2.tokenization.dao.tokenization.TokenDao.update_by_id"
        ):
            mock_token = Mock()
            mock_token.id = 1
            mock_token.uuid = "token_123"
            mock_create_token.return_value = mock_token
            mock_build_calldata.return_value = "0x123456"
            mock_predict_addr.return_value = "0xcontract123"
            mock_get_custody_wallet.return_value = Mock()

            mock_activity = Mock()
            mock_activity.uuid = "activity_123"
            mock_create_activity.return_value = mock_activity

            mock_deploy_tx = Mock()
            mock_deploy_tx.request_id = "deploy_tx_123"
            mock_execute_deploy.return_value = mock_deploy_tx

            # Mock sponsor返回None表示不需要垫付
            mock_sponsor.return_value = None

            # Mock activity transaction
            mock_activity_tx = Mock()
            mock_create_activity_tx.return_value = mock_activity_tx

            result = TokenizationDeployManager.deploy(
                params=deploy_request,
                org_id=self.org_id,
                biz_org_id=self.biz_org_id,
                api_request_info={},
                sign_info={},
            )

            # 验证结果 - 应该直接部署，不进行垫付
            self.assertEqual(result, "activity_123")
            mock_execute_deploy.assert_called_once()
            mock_create_activity.assert_called_once()
            mock_create_activity_tx.assert_called_once()

    def test_gas_sponsor_manager_callback_methods(self):
        """测试Gas Sponsor Manager的回调方法"""
        from waas2.tokenization.managers.gas_sponsor import GasSponsorManager

        # 测试成功回调（当前为空实现）
        mock_request = Mock()
        mock_request.request_id = "test_request_123"

        # 这些方法当前为空实现，测试它们不会抛出异常
        try:
            GasSponsorManager.on_sponsor_request_success(mock_request)
            GasSponsorManager.on_sponsor_request_failed(mock_request)
        except Exception as e:
            self.fail(f"Callback methods should not raise exceptions: {e}")

    def test_gas_sponsor_transaction_action_constant(self):
        """测试Gas Sponsor交易动作常量"""
        from waas2.tokenization.managers.gas_sponsor import GasSponsorManager

        # 验证常量值
        self.assertEqual(GasSponsorManager.TRANSACTION_ACTION, "GasSponsor")

        # 验证常量在不同地方使用的一致性
        action = GasSponsorManager.TRANSACTION_ACTION
        self.assertIsInstance(action, str)
        self.assertTrue(len(action) > 0)


class TestTokenAccessActivatedDeployment(TokenizationTestBase):
    """测试 token_access_activated 参数在部署时的功能"""

    def test_token_access_activated_parameter_handling(self):
        """测试 token_access_activated 参数的处理逻辑"""
        # 测试 token_access_activated=True
        token_params_true = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            token_access_activated=True,
        )

        # 验证参数被正确设置
        self.assertTrue(getattr(token_params_true, "token_access_activated", False))

        # 测试 token_access_activated=False
        token_params_false = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            token_access_activated=False,
        )

        # 验证参数被正确设置
        self.assertFalse(getattr(token_params_false, "token_access_activated", False))

        # 测试未设置 token_access_activated（默认为 False）
        token_params_default = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
        )

        # 验证默认值为 False
        self.assertFalse(getattr(token_params_default, "token_access_activated", False))

    def test_multicall_data_construction_with_allowlist(self):
        """测试 multicall 数据构造中的 allowlist 逻辑"""
        # 使用 patch 来测试内部逻辑
        with patch.object(
            TokenizationDeployManager, "_encode_function_call"
        ) as mock_encode:
            mock_encode.return_value = b"\x12\x34\x56\x78"

            # 测试 token_access_activated=True 的情况
            token_params_true = cobo_waas2.TokenizationERC20TokenParams(
                standard=cobo_waas2.TokenizationTokenStandard.ERC20,
                name="Test Token",
                symbol="TEST",
                decimals=18,
                token_access_activated=True,
            )

            # 验证 getattr 调用返回 True
            token_access_activated = getattr(
                token_params_true, "token_access_activated", False
            )
            self.assertTrue(token_access_activated)

            # 测试 token_access_activated=False 的情况
            token_params_false = cobo_waas2.TokenizationERC20TokenParams(
                standard=cobo_waas2.TokenizationTokenStandard.ERC20,
                name="Test Token",
                symbol="TEST",
                decimals=18,
                token_access_activated=False,
            )

            # 验证 getattr 调用返回 False
            token_access_activated = getattr(
                token_params_false, "token_access_activated", False
            )
            self.assertFalse(token_access_activated)

    def test_encode_function_call_toggle_accesslist(self):
        """测试 toggleAccesslist 函数编码"""
        # 测试 toggleAccesslist(true)
        encoded_data = TokenizationDeployManager._encode_function_call(
            "toggleAccesslist(bool)", [True]
        )

        # 验证返回的是 bytes 类型
        self.assertIsInstance(encoded_data, bytes)

        # 验证长度（4字节选择器 + 32字节参数）
        self.assertEqual(len(encoded_data), 36)

        # 测试 toggleAccesslist(false)
        encoded_data_false = TokenizationDeployManager._encode_function_call(
            "toggleAccesslist(bool)", [False]
        )

        # 验证返回的是 bytes 类型
        self.assertIsInstance(encoded_data_false, bytes)

        # 验证长度
        self.assertEqual(len(encoded_data_false), 36)

        # 验证 true 和 false 编码不同
        self.assertNotEqual(encoded_data, encoded_data_false)
