import uuid
from unittest.mock import MagicMock, patch

import cobo_waas2

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.controllers.mint import TokenizationMintController
from waas2.tokenization.dao.tokenization import (
    ActivityDao,
    TokenAllowlistDao,
    TokenBlocklistDao,
    TokenDao,
    TokenRoleDao,
)
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.tests.test_base import TokenizationTestBase


class TestTokenizationMintController(TokenizationTestBase):
    """Tokenization铸币控制器测试"""

    def setUp(self):
        super().setUp()
        # 创建真实的测试数据
        self.token = self.create_mock_token()
        self.token_role = self.create_mock_token_role()

    def create_mock_token(self):
        """创建mock Token对象"""
        token = TokenDao.create(
            uuid=str(uuid.uuid4()),
            org_id=self.org_id,
            token_id=self.token_id,
            chain_id=self.chain_id,
            token_address=self.address,
            status=cobo_waas2.TokenizationStatus.ACTIVE.value,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            extra={},
        )
        return token

    def create_mock_token_role(self):
        """创建mock TokenRole对象"""
        role = TokenRoleDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address=self.address,
            roles=TokenizationRole.MINTER.value,
        )
        return role

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.contract_call_for_dev_api"
    )
    def test_mint_tokens_success(
        self, mock_contract_call, mock_query_wallet, mock_validate_source
    ):
        """测试成功铸币代币"""
        # Mock 钱包验证
        mock_query_wallet.return_value = self.create_test_wallet()
        mock_validate_source.return_value = None

        # Mock 合约调用
        mock_tx = MagicMock()
        mock_tx.request_id = "test_request_id"
        mock_tx.transaction_id = "test_tx_id"
        mock_contract_call.return_value = mock_tx

        # 创建铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",  # 1000 tokens
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法
        response = TokenizationMintController.mint_tokens(
            self.token_id,
            self.org_id,
            self.biz_org_id,
            mint_request,
            {"signature": "test_signature"},
            {"request_id": "test_request"},
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertIsNotNone(response.activity_id)

        # 验证数据库中创建了Activity记录
        activities = ActivityDao.list_by_token_and_org(self.token_id, self.org_id)
        self.assertGreater(len(activities), 0)

    def test_mint_tokens_token_not_found(self):
        """测试铸币不存在的代币"""
        # 创建铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationMintController.mint_tokens(
                "non_existent_token",
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("is not issued", str(context.exception))

    def test_mint_tokens_inactive_token(self):
        """测试铸币非活跃代币"""
        # 创建非活跃代币
        inactive_token = TokenDao.create(
            uuid=str(uuid.uuid4()),
            org_id=self.org_id,
            token_id="inactive_token",
            chain_id=self.chain_id,
            token_address=self.address,
            status=cobo_waas2.TokenizationStatus.PROCESSING.value,
            name="Inactive Token",
            symbol="INACTIVE",
            decimals=18,
            extra={},
        )

        # 创建铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationMintController.mint_tokens(
                inactive_token.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("not issued", str(context.exception))

    def test_mint_tokens_no_minter_role(self):
        """测试没有铸币权限的地址进行铸币"""
        # 创建只有管理员权限的角色
        different_address = "0x9999999999999999999999999999999999999999"
        TokenRoleDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address=different_address,
            roles=TokenizationRole.ADMIN.value,  # 只有管理员权限，没有铸币权限
        )

        # 创建铸币请求 - 使用没有权限的地址
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=cobo_waas2.TokenizationMpcOperationSource(
                    source_type=cobo_waas2.TokenizationOperationSourceType.ORG_CONTROLLED,
                    wallet_id=self.wallet_id,
                    address=different_address,  # 使用没有铸币权限的地址
                )
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("does not have minter role", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    def test_mint_tokens_invalid_wallet(self, mock_query_wallet, mock_validate_source):
        """测试使用无效钱包铸币"""
        # Mock validate_source通过
        mock_validate_source.return_value = None
        # Mock query_and_check_wallet抛出钱包未找到错误
        from cobo_libs.api.restful.exceptions import ApiInvalidParamException

        mock_query_wallet.side_effect = ApiInvalidParamException("Wallet not found")

        # 创建铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(
            (InvalidParamException, ApiInvalidParamException)
        ) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("Wallet not found", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    def test_mint_tokens_invalid_wallet_type(
        self, mock_query_wallet, mock_validate_source
    ):
        """测试使用不支持的钱包类型铸币"""
        # Mock validate_source通过
        mock_validate_source.return_value = None
        # Mock query_and_check_wallet抛出钱包类型错误
        from cobo_libs.api.restful.exceptions import ApiInvalidParamException

        mock_query_wallet.side_effect = ApiInvalidParamException("does not support")

        # 创建铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(
            (InvalidParamException, ApiInvalidParamException)
        ) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("does not support", str(context.exception))

    def test_mint_tokens_empty_mints_list(self):
        """测试空的铸币列表"""
        # 创建空铸币列表的请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("cannot be empty", str(context.exception))

    def test_mint_tokens_invalid_amount(self):
        """测试无效的铸币数量"""
        # 创建负数铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="-1000",  # 负数
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("Invalid mint amount", str(context.exception))

    def test_mint_tokens_invalid_amount_format(self):
        """测试无效的铸币数量格式"""
        # 创建非数字铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="invalid_number",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises((InvalidParamException, Exception)) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        # 验证异常信息包含相关内容
        self.assertTrue(
            "Invalid mint amount format" in str(context.exception)
            or "ConversionSyntax" in str(context.exception)
            or "invalid_number" in str(context.exception)
        )

    def test_mint_tokens_with_restrictions_blacklisted_address(self):
        """测试向黑名单地址铸币"""
        # 创建黑名单地址
        blacklisted_address = "******************************************"
        TokenBlocklistDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address=blacklisted_address,
            note="Test blacklist",
            valid=True,
        )

        # 创建向黑名单地址铸币的请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address=blacklisted_address, amount="1000"
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("blocked", str(context.exception))

    def test_mint_tokens_with_restrictions_not_in_allowlist(self):
        """测试向不在白名单的地址铸币"""
        # 更新token以启用白名单限制
        TokenDao.update_by_id(
            self.token.id,
            extra={"token_access_activated": True},
        )

        # 创建白名单地址（不包含目标地址）
        TokenAllowlistDao.create(
            uuid=str(uuid.uuid4()),
            token_id=self.token_id,
            address="0x3333333333333333333333333333333333333333",
            note="Test allowlist",
            valid=True,
        )

        # 创建向非白名单地址铸币的请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="0x4444444444444444444444444444444444444444",  # 不在白名单
                    amount="1000",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法应该抛出异常
        with self.assertRaises(InvalidParamException) as context:
            TokenizationMintController.mint_tokens(
                self.token_id,
                self.org_id,
                self.biz_org_id,
                mint_request,
                {"signature": "test_signature"},
                {"request_id": "test_request"},
            )

        self.assertIn("not in allowlist", str(context.exception))

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.contract_call_for_dev_api"
    )
    def test_mint_tokens_web3_source(
        self, mock_contract_call, mock_query_wallet, mock_validate_source
    ):
        """测试使用Web3源铸币"""
        # Mock 钱包验证
        mock_query_wallet.return_value = self.create_test_wallet()
        mock_validate_source.return_value = None

        # Mock 合约调用
        mock_tx = MagicMock()
        mock_tx.request_id = "test_request_id"
        mock_tx.transaction_id = "test_tx_id"
        mock_contract_call.return_value = mock_tx

        # 创建Web3源铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source(
                    cobo_waas2.TokenizationOperationSourceType.WEB3
                )
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",
                )
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法
        response = TokenizationMintController.mint_tokens(
            self.token_id,
            self.org_id,
            self.biz_org_id,
            mint_request,
            {"signature": "test_signature"},
            {"request_id": "test_request"},
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertIsNotNone(response.activity_id)

    @patch("waas2.tokenization.utils.tokenization.TokenizationUtils.validate_source")
    @patch(
        "waas2.base.processors.wallets.dev.wallets.WalletProcessorHelper.query_and_check_wallet"
    )
    @patch(
        "waas2.transactions.dev.controllers.transaction.TransactionController.contract_call_for_dev_api"
    )
    def test_mint_tokens_multiple_recipients(
        self, mock_contract_call, mock_query_wallet, mock_validate_source
    ):
        """测试向多个地址铸币"""
        # Mock 钱包验证
        mock_query_wallet.return_value = self.create_test_wallet()
        mock_validate_source.return_value = None

        # Mock 合约调用
        mock_tx = MagicMock()
        mock_tx.request_id = "test_request_id"
        mock_tx.transaction_id = "test_tx_id"
        mock_contract_call.return_value = mock_tx

        # 创建多地址铸币请求
        mint_request = cobo_waas2.TokenizationMintTokenRequest(
            source=cobo_waas2.TokenizationTokenOperationSource(
                actual_instance=self.create_test_source()
            ),
            mints=[
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="1000",
                ),
                cobo_waas2.TokenizationMintTokenParamsMintsInner(
                    to_address="******************************************",
                    amount="500",
                ),
            ],
            fee=self.create_test_fee(),
        )

        # 调用方法
        response = TokenizationMintController.mint_tokens(
            self.token_id,
            self.org_id,
            self.biz_org_id,
            mint_request,
            {"signature": "test_signature"},
            {"request_id": "test_request"},
        )

        # 验证结果
        self.assertIsInstance(response, cobo_waas2.TokenizationOperationResponse)
        self.assertIsNotNone(response.activity_id)
