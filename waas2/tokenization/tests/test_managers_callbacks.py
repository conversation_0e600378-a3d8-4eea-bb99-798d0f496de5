import uuid
from unittest.mock import <PERSON>Mock, patch

from django.test import TestCase
from web3 import Web3

from custody.web3.data.objects import MPCExtraParameters
from custody.web3.models.transaction_request import TransactionRequest
from waas2.tokenization.abis.CoboERC20 import COBO_ERC20_ABI
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.managers.allowlist import TokenizationAllowlistManager
from waas2.tokenization.managers.blocklist import TokenizationBlocklistManager
from waas2.tokenization.managers.contract_call import TokenizationContractCallManager
from waas2.tokenization.models.activity import Activity
from waas2.tokenization.models.tokenization import Token


class ManagerCallbacksTest(TestCase):
    def setUp(self):
        self.w3 = Web3()
        self.contract = self.w3.eth.contract(abi=COBO_ERC20_ABI)
        self.test_address_1 = Web3.to_checksum_address(
            "******************************************"
        )
        self.test_address_2 = Web3.to_checksum_address(
            "******************************************"
        )
        self.token_address = Web3.to_checksum_address(
            "******************************************"
        )
        self.chain_id = "ETH"

        # Mock objects
        self.mock_activity = MagicMock(spec=Activity)
        self.mock_activity.uuid = str(uuid.uuid4())
        self.mock_activity.token_id = "test_token_id"
        self.mock_activity.token_uuid = str(uuid.uuid4())

        self.mock_token = MagicMock(spec=Token)
        self.mock_token.id = 1
        self.mock_token.token_id = self.mock_activity.token_id
        self.mock_token.token_uuid = self.mock_activity.token_uuid
        self.mock_token.token_address = self.token_address
        self.mock_token.chain_id = self.chain_id
        self.mock_token.extra = TokenizationExtra(
            token_access_activated=False
        ).model_dump(mode="json")

        self.mock_tx_request = MagicMock(spec=TransactionRequest)

    def _get_calldata_and_mock_tx_request(self, fn_name, args):
        calldata = self.contract.encodeABI(fn_name, args)
        extra_params = MPCExtraParameters(calldata=calldata)
        self.mock_tx_request.extra_args = extra_params.to_json()
        return calldata

    @patch("waas2.tokenization.managers.allowlist.TokenDao")
    @patch("waas2.tokenization.managers.allowlist.ActivityDao")
    @patch("waas2.tokenization.managers.allowlist.CoboERC20ContractHelper")
    @patch("waas2.tokenization.managers.allowlist.TokenAllowlistDao")
    def test_on_allowlist_success(
        self,
        mock_allowlist_dao,
        mock_contract_helper,
        mock_activity_dao,
        mock_token_dao,
    ):
        mock_activity_dao.get_by_uuid.return_value = self.mock_activity
        mock_token_dao.get_by_uuid.return_value = self.mock_token
        mock_contract = MagicMock()
        mock_contract_helper.get_contract.return_value = mock_contract

        # Test accessListAdd
        addresses_to_add = [self.test_address_1, self.test_address_2]
        self._get_calldata_and_mock_tx_request("accessListAdd", [addresses_to_add])

        mock_func = MagicMock()
        mock_func.fn_name = "accessListAdd"
        mock_contract.decode_function_input.return_value = (
            mock_func,
            {"accounts": addresses_to_add},
        )

        TokenizationAllowlistManager.on_allowlist_success(
            self.mock_activity.uuid, self.mock_tx_request
        )

        mock_allowlist_dao.create_or_update_address.assert_any_call(
            token_id=self.mock_activity.token_id,
            address=self.test_address_1,
            valid=True,
        )
        mock_allowlist_dao.create_or_update_address.assert_any_call(
            token_id=self.mock_activity.token_id,
            address=self.test_address_2,
            valid=True,
        )
        self.assertEqual(mock_allowlist_dao.create_or_update_address.call_count, 2)

        # Test accessListRemove
        mock_allowlist_dao.create_or_update_address.reset_mock()
        addresses_to_remove = [self.test_address_1]
        self._get_calldata_and_mock_tx_request(
            "accessListRemove", [addresses_to_remove]
        )
        mock_func.fn_name = "accessListRemove"
        mock_contract.decode_function_input.return_value = (
            mock_func,
            {"accounts": addresses_to_remove},
        )
        TokenizationAllowlistManager.on_allowlist_success(
            self.mock_activity.uuid, self.mock_tx_request
        )
        mock_allowlist_dao.create_or_update_address.assert_called_once_with(
            token_id=self.mock_activity.token_id,
            address=self.test_address_1,
            valid=False,
        )

    @patch("waas2.tokenization.managers.blocklist.TokenDao")
    @patch("waas2.tokenization.managers.blocklist.ActivityDao")
    @patch("waas2.tokenization.managers.blocklist.CoboERC20ContractHelper")
    @patch("waas2.tokenization.managers.blocklist.TokenBlocklistDao")
    def test_on_block_success(
        self,
        mock_blocklist_dao,
        mock_contract_helper,
        mock_activity_dao,
        mock_token_dao,
    ):
        mock_activity_dao.get_by_uuid.return_value = self.mock_activity
        mock_token_dao.get_by_uuid.return_value = self.mock_token
        mock_contract = MagicMock()
        mock_contract_helper.get_contract.return_value = mock_contract

        # Test blockListAdd
        addresses_to_add = [self.test_address_1]
        self._get_calldata_and_mock_tx_request("blockListAdd", [addresses_to_add])
        mock_func = MagicMock()
        mock_func.fn_name = "blockListAdd"
        mock_contract.decode_function_input.return_value = (
            mock_func,
            {"accounts": addresses_to_add},
        )
        TokenizationBlocklistManager.on_block_success(
            self.mock_activity.uuid, self.mock_tx_request
        )
        mock_blocklist_dao.create_or_update_address.assert_called_once_with(
            token_id=self.mock_activity.token_id,
            address=self.test_address_1,
            valid=True,
        )

    @patch("waas2.tokenization.managers.allowlist.TokenDao")
    @patch("waas2.tokenization.managers.allowlist.ActivityDao")
    @patch("waas2.tokenization.managers.allowlist.CoboERC20ContractHelper")
    def test_on_token_access_activation_success(
        self, mock_contract_helper, mock_activity_dao, mock_token_dao
    ):
        mock_activity_dao.get_by_uuid.return_value = self.mock_activity
        mock_token_dao.get_by_uuid.return_value = self.mock_token
        mock_contract = MagicMock()
        mock_contract_helper.get_contract.return_value = mock_contract

        # Test toggleAccesslist(true)
        self._get_calldata_and_mock_tx_request("toggleAccesslist", [True])
        mock_func = MagicMock()
        mock_func.fn_name = "toggleAccesslist"
        mock_contract.decode_function_input.return_value = (
            mock_func,
            {"enabled": True},
        )
        TokenizationAllowlistManager.on_token_access_activation_success(
            self.mock_activity.uuid, self.mock_tx_request
        )

        mock_token_dao.update_by_id.assert_called_once()
        updated_extra = TokenizationExtra.model_validate(
            mock_token_dao.update_by_id.call_args[1]["extra"]
        )
        self.assertTrue(updated_extra.token_access_activated)

    @patch("waas2.tokenization.managers.contract_call.TokenDao")
    @patch("waas2.tokenization.managers.contract_call.ActivityDao")
    @patch("waas2.tokenization.managers.contract_call.CoboERC20ContractHelper")
    @patch(
        "waas2.tokenization.managers.contract_call.TokenizationContractCallManager._sync_contract_call_state"
    )
    def test_on_contract_call_success(
        self, mock_sync, mock_contract_helper, mock_activity_dao, mock_token_dao
    ):
        mock_activity_dao.get_by_uuid.return_value = self.mock_activity
        mock_token_dao.get_by_uuid.return_value = self.mock_token
        mock_contract = MagicMock()
        mock_contract_helper.get_contract.return_value = mock_contract

        calldata = self._get_calldata_and_mock_tx_request("pause", [])

        TokenizationContractCallManager.on_contract_call_success(
            self.mock_activity.uuid, self.mock_tx_request
        )

        mock_sync.assert_called_once_with(self.mock_token, calldata)

    @patch("waas2.tokenization.managers.contract_call.TokenDao")
    @patch("waas2.tokenization.dao.tokenization.TokenRoleDao")
    @patch("waas2.tokenization.managers.contract_call.CoboERC20ContractHelper")
    def test_sync_contract_call_grant_role_state(
        self, mock_contract_helper, mock_role_dao, mock_token_dao
    ):
        mock_token_dao.get_by_token_id.return_value = self.mock_token
        mock_contract = MagicMock()
        mock_contract_helper.get_contract.return_value = mock_contract
        mock_role_dao.get_by_token_and_address.return_value = None

        role_hash_hex = (
            "0x9f2df0fed2c77648de5860a4cc508cd0818c85b8b8a1ab4ceeef8d981c8956a6"
        )
        role_hash_bytes = bytes.fromhex(role_hash_hex[2:])
        account = self.test_address_1

        calldata = self._get_calldata_and_mock_tx_request(
            "grantRole", [role_hash_hex, account]
        )
        mock_func = MagicMock()
        mock_func.fn_name = "grantRole"
        mock_contract.decode_function_input.return_value = (
            mock_func,
            {"role": role_hash_bytes, "account": account},
        )

        TokenizationContractCallManager._sync_contract_call_state(
            self.mock_token, calldata
        )

        from waas2.tokenization.enums.tokenization import TokenizationRole

        mock_role_dao.create.assert_called_once_with(
            token_id=self.mock_token.token_id,
            address=account,
            roles=TokenizationRole.MINTER.value,
        )
