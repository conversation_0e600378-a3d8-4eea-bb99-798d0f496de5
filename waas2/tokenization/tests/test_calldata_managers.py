"""
Calldata管理器测试
"""
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase
import cobo_waas2

from waas2.tokenization.managers.calldata.evm.cobo_erc20 import CoboERC20CalldataManager
from waas2.tokenization.managers.calldata.solana.token_2022 import SolanaToken2022CalldataManager
from waas2.tokenization.managers.deploy import TokenizationDeployManager


class TestCoboERC20CalldataManager(TestCase):
    """CoboERC20 Calldata管理器测试"""
    
    def setUp(self):
        self.chain_id = "ETH"
        self.execute_address = "******************************************"
        self.token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            permissions=cobo_waas2.TokenizationTokenPermissionParams(
                admin=["******************************************"]
            )
        )
    
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20ContractHelper.get_factory_contract')
    def test_build_deploy_calldata(self, mock_get_factory):
        """测试ERC20部署calldata构建"""
        # 模拟工厂合约
        mock_factory = MagicMock()
        mock_factory.address = "******************************************"
        mock_factory.functions.deployAndInit.return_value._encode_transaction_data.return_value = "0x123456"
        mock_get_factory.return_value = mock_factory
        
        # 执行测试
        calldata = CoboERC20CalldataManager.build_deploy_calldata(
            self.chain_id, self.execute_address, self.token_params
        )
        
        # 验证结果
        self.assertEqual(calldata, "0x123456")
        mock_get_factory.assert_called_once_with(self.chain_id)
    
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20ContractHelper.get_factory_contract')
    def test_predict_contract_address(self, mock_get_factory):
        """测试ERC20合约地址预测"""
        # 模拟工厂合约和web3
        mock_w3 = MagicMock()
        mock_w3.eth.call.return_value = b'\x00' * 12 + b'\x12\x34\x56\x78\x90\x12\x34\x56\x78\x90\x12\x34\x56\x78\x90\x12\x34\x56\x78\x90'
        mock_w3.codec.decode.return_value = ["******************************************"]
        
        mock_factory = MagicMock()
        mock_factory.w3 = mock_w3
        mock_factory.address = "******************************************"
        mock_get_factory.return_value = mock_factory
        
        # 执行测试
        address = CoboERC20CalldataManager.predict_contract_address(
            self.chain_id, self.execute_address, "0x123456"
        )
        
        # 验证结果
        self.assertEqual(address, "******************************************")


class TestSolanaToken2022CalldataManager(TestCase):
    """Solana Token 2022 Calldata管理器测试"""
    
    def setUp(self):
        self.chain_id = "SOL"
        self.execute_address = "SolanaAddress123456789"
        self.token_params = {
            "name": "Solana Test Token",
            "symbol": "SOLTEST",
            "decimals": 9,
            "mint_authority": "MintAuthority123456789",
            "freeze_authority": "FreezeAuthority123456789",
            "extensions": [
                {
                    "type": "TRANSFER_FEE",
                    "transfer_fee_basis_points": 100,
                    "maximum_fee": 1000000,
                    "transfer_fee_config_authority": "FeeAuthority123456789",
                    "withdraw_withheld_authority": "WithdrawAuthority123456789"
                }
            ]
        }
    
    def test_build_deploy_calldata(self):
        """测试Solana Token 2022部署calldata构建"""
        calldata = SolanaToken2022CalldataManager.build_deploy_calldata(
            self.chain_id, self.execute_address, self.token_params
        )
        
        # 解析返回的JSON
        instructions = json.loads(calldata)
        
        # 验证基本结构
        self.assertEqual(instructions["type"], "solana_token_2022_deploy")
        self.assertEqual(instructions["chain_id"], self.chain_id)
        self.assertEqual(instructions["payer"], self.execute_address)
        self.assertIn("mint_address", instructions)
        self.assertIn("instructions", instructions)
        
        # 验证代币参数
        token_params = instructions["token_params"]
        self.assertEqual(token_params["name"], "Solana Test Token")
        self.assertEqual(token_params["symbol"], "SOLTEST")
        self.assertEqual(token_params["decimals"], 9)
        
        # 验证指令序列
        instruction_list = instructions["instructions"]
        self.assertGreater(len(instruction_list), 0)
        
        # 验证包含创建账户指令
        create_account_found = any(
            inst["type"] == "create_account" for inst in instruction_list
        )
        self.assertTrue(create_account_found)
        
        # 验证包含初始化mint指令
        init_mint_found = any(
            inst["type"] == "initialize_mint" for inst in instruction_list
        )
        self.assertTrue(init_mint_found)
    
    def test_predict_contract_address(self):
        """测试Solana mint地址预测"""
        # 构建calldata
        calldata = SolanaToken2022CalldataManager.build_deploy_calldata(
            self.chain_id, self.execute_address, self.token_params
        )
        
        # 预测地址
        address = SolanaToken2022CalldataManager.predict_contract_address(
            self.chain_id, self.execute_address, calldata
        )
        
        # 验证地址格式（应该是base58编码的字符串）
        self.assertIsInstance(address, str)
        self.assertGreater(len(address), 0)
    
    def test_extension_instruction_building(self):
        """测试扩展指令构建"""
        mint_address = "TestMintAddress123456789"
        
        # 测试Transfer Fee扩展
        transfer_fee_ext = {
            "type": "TRANSFER_FEE",
            "transfer_fee_basis_points": 100,
            "maximum_fee": 1000000,
            "transfer_fee_config_authority": "FeeAuthority123456789",
            "withdraw_withheld_authority": "WithdrawAuthority123456789"
        }
        
        instruction = SolanaToken2022CalldataManager._build_extension_instruction(
            transfer_fee_ext, mint_address
        )
        
        self.assertIsNotNone(instruction)
        self.assertEqual(instruction["type"], "initialize_transfer_fee_config")
        self.assertEqual(instruction["accounts"]["mint"], mint_address)
        self.assertEqual(instruction["data"]["transfer_fee_basis_points"], 100)
        
        # 测试不支持的扩展
        unsupported_ext = {"type": "UNSUPPORTED_EXTENSION"}
        instruction = SolanaToken2022CalldataManager._build_extension_instruction(
            unsupported_ext, mint_address
        )
        self.assertIsNone(instruction)


class TestTokenizationDeployManager(TestCase):
    """代币部署管理器测试"""
    
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.build_deploy_calldata')
    def test_build_deploy_calldata_evm(self, mock_evm_calldata):
        """测试EVM链calldata构建路由"""
        mock_evm_calldata.return_value = "0x123456"
        
        token_params = cobo_waas2.TokenizationERC20TokenParams(
            standard=cobo_waas2.TokenizationTokenStandard.ERC20,
            name="Test Token",
            symbol="TEST",
            decimals=18
        )
        
        result = TokenizationDeployManager.build_deploy_calldata(
            "ETH", "0x1234", token_params
        )
        
        self.assertEqual(result, "0x123456")
        mock_evm_calldata.assert_called_once()
    
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.SolanaToken2022CalldataManager.build_deploy_calldata')
    def test_build_deploy_calldata_solana(self, mock_solana_calldata):
        """测试Solana链calldata构建路由"""
        mock_solana_calldata.return_value = '{"type": "solana_deploy"}'
        
        token_params = {
            "name": "Solana Test",
            "symbol": "SOLTEST",
            "decimals": 9,
            "mint_authority": "authority123"
        }
        
        result = TokenizationDeployManager.build_deploy_calldata(
            "SOL", "solana_address", token_params
        )
        
        self.assertEqual(result, '{"type": "solana_deploy"}')
        mock_solana_calldata.assert_called_once()
    
    @patch('waas2.tokenization.managers.calldata.evm.cobo_erc20.CoboERC20CalldataManager.predict_contract_address')
    def test_predict_contract_address_evm(self, mock_evm_predict):
        """测试EVM链地址预测路由"""
        mock_evm_predict.return_value = "0xcontract123"
        
        result = TokenizationDeployManager._predict_contract_address(
            "ETH", "0x1234", "0x123456"
        )
        
        self.assertEqual(result, "0xcontract123")
        mock_evm_predict.assert_called_once()
    
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.SolanaToken2022CalldataManager.predict_contract_address')
    def test_predict_contract_address_solana(self, mock_solana_predict):
        """测试Solana链地址预测路由"""
        mock_solana_predict.return_value = "solana_mint_address123"
        
        result = TokenizationDeployManager._predict_contract_address(
            "SOL", "solana_address", '{"type": "solana_deploy"}'
        )
        
        self.assertEqual(result, "solana_mint_address123")
        mock_solana_predict.assert_called_once()
    
    def test_unsupported_chain(self):
        """测试不支持的链"""
        # 修改链检测器，让UNSUPPORTED返回一个不存在的链类型
        from waas2.tokenization.utils.chain_detector import ChainDetector, ChainType

        # 暂时修改链检测逻辑
        original_get_chain_type = ChainDetector.get_chain_type

        def mock_get_chain_type(chain_id):
            if chain_id == "UNSUPPORTED":
                # 返回一个不存在的链类型，这会导致ValueError
                return "UNKNOWN_CHAIN_TYPE"
            return original_get_chain_type(chain_id)

        ChainDetector.get_chain_type = mock_get_chain_type

        try:
            with self.assertRaises(ValueError):
                TokenizationDeployManager.build_deploy_calldata(
                    "UNSUPPORTED", "address", {}
                )

            with self.assertRaises(ValueError):
                TokenizationDeployManager._predict_contract_address(
                    "UNSUPPORTED", "address", "calldata"
                )
        finally:
            # 恢复原始方法
            ChainDetector.get_chain_type = original_get_chain_type
