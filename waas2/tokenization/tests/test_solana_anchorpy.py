"""
Solana Token 2022 anchorpy 实现测试
"""
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase

from waas2.tokenization.managers.calldata.solana.token_2022 import SolanaToken2022CalldataManager


class TestSolanaToken2022AnchorpyImplementation(TestCase):
    """Solana Token 2022 anchorpy实现测试"""
    
    def setUp(self):
        self.chain_id = "SOL_DEVNET"
        self.execute_address = "SolanaAddress123456789012345678901234567890123"
        self.token_params = {
            "name": "Anchorpy Test Token",
            "symbol": "ANCHOR",
            "decimals": 9,
            "mint_authority": "MintAuthority123456789012345678901234567890123",
            "freeze_authority": "FreezeAuthority123456789012345678901234567890123",
            "extensions": [
                {
                    "type": "TRANSFER_FEE",
                    "transfer_fee_basis_points": 100,
                    "maximum_fee": 1000000,
                    "transfer_fee_config_authority": "FeeAuthority123456789012345678901234567890123",
                    "withdraw_withheld_authority": "WithdrawAuthority123456789012345678901234567890123"
                },
                {
                    "type": "DEFAULT_ACCOUNT_STATE",
                    "state": "initialized"
                }
            ]
        }
    
    def test_cluster_detection(self):
        """测试集群检测"""
        # 测试不同的chain_id映射
        test_cases = [
            ("SOL", "mainnet-beta"),
            ("SOL_DEVNET", "devnet"),
            ("SOL_TESTNET", "testnet"),
            ("UNKNOWN", "devnet")  # 默认值
        ]
        
        for chain_id, expected_cluster in test_cases:
            cluster = SolanaToken2022CalldataManager._get_cluster_from_chain_id(chain_id)
            self.assertEqual(cluster, expected_cluster)
    
    def test_mint_keypair_generation(self):
        """测试mint密钥对生成"""
        keypair = SolanaToken2022CalldataManager._generate_mint_keypair(self.token_params)
        
        self.assertIn("public_key", keypair)
        self.assertIn("private_key", keypair)
        self.assertIsInstance(keypair["public_key"], str)
        self.assertGreater(len(keypair["public_key"]), 0)
    
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Program')
    @patch('waas2.tokenization.managers.calldata.solana.token_2022.Pubkey')
    def test_build_deploy_calldata_with_anchorpy(self, mock_pubkey, mock_program):
        """测试使用anchorpy构建部署calldata"""
        # 模拟Pubkey
        mock_pubkey.from_string.return_value = MagicMock()
        mock_pubkey.from_string.return_value.__str__ = lambda: "MockedPubkey123456789"
        
        # 模拟Program
        mock_program_instance = MagicMock()
        mock_program_instance.program_id = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"
        mock_program_instance.instruction = {
            "initialize_mint": MagicMock(),
            "initialize_transfer_fee_config": MagicMock(),
            "initialize_default_account_state": MagicMock()
        }
        
        # 模拟指令返回值
        mock_ix = MagicMock()
        mock_ix.data = b'\x01\x02\x03\x04'
        mock_program_instance.instruction["initialize_mint"].return_value = mock_ix
        mock_program_instance.instruction["initialize_transfer_fee_config"].return_value = mock_ix
        mock_program_instance.instruction["initialize_default_account_state"].return_value = mock_ix
        
        mock_program.return_value = mock_program_instance
        
        # 执行测试
        calldata = SolanaToken2022CalldataManager.build_deploy_calldata(
            self.chain_id, self.execute_address, self.token_params
        )
        
        # 验证结果
        self.assertIsInstance(calldata, str)
        
        # 解析JSON
        instructions = json.loads(calldata)
        
        # 验证基本结构
        if instructions["type"] == "solana_token_2022_deploy_anchorpy":
            # anchorpy模式
            self.assertEqual(instructions["chain_id"], self.chain_id)
            self.assertEqual(instructions["cluster"], "devnet")
            self.assertIn("mint_address", instructions)
            self.assertIn("instructions", instructions)
            self.assertIn("idl_version", instructions)
        else:
            # fallback模式
            self.assertEqual(instructions["type"], "solana_token_2022_deploy_fallback")
    
    def test_build_deploy_calldata_fallback_mode(self):
        """测试降级模式的calldata构建"""
        # 直接调用降级方法
        calldata = SolanaToken2022CalldataManager._build_fallback_instructions(
            self.chain_id, self.execute_address, self.token_params
        )
        
        # 解析JSON
        instructions = json.loads(calldata)
        
        # 验证降级模式结构
        self.assertEqual(instructions["type"], "solana_token_2022_deploy_fallback")
        self.assertEqual(instructions["chain_id"], self.chain_id)
        self.assertEqual(instructions["payer"], self.execute_address)
        self.assertIn("mint_address", instructions)
        self.assertIn("note", instructions)
        self.assertEqual(instructions["note"], "Fallback mode - anchorpy not available")
    
    def test_extension_instruction_building_fallback(self):
        """测试降级模式的扩展指令构建"""
        mint_address = "TestMintAddress123456789"
        
        # 测试Transfer Fee扩展
        transfer_fee_ext = {
            "type": "TRANSFER_FEE",
            "transfer_fee_basis_points": 100,
            "maximum_fee": 1000000,
            "transfer_fee_config_authority": "FeeAuthority123456789",
            "withdraw_withheld_authority": "WithdrawAuthority123456789"
        }
        
        instruction = SolanaToken2022CalldataManager._build_extension_instruction_fallback(
            transfer_fee_ext, mint_address
        )
        
        self.assertIsNotNone(instruction)
        self.assertEqual(instruction["type"], "initialize_transfer_fee_config")
        self.assertEqual(instruction["accounts"]["mint"], mint_address)
        self.assertEqual(instruction["data"]["transfer_fee_basis_points"], 100)
        self.assertEqual(instruction["mode"], "fallback")
        
        # 测试Default Account State扩展
        default_state_ext = {
            "type": "DEFAULT_ACCOUNT_STATE",
            "state": "frozen"
        }
        
        instruction = SolanaToken2022CalldataManager._build_extension_instruction_fallback(
            default_state_ext, mint_address
        )
        
        self.assertIsNotNone(instruction)
        self.assertEqual(instruction["type"], "initialize_default_account_state")
        self.assertEqual(instruction["data"]["state"], "frozen")
        
        # 测试不支持的扩展
        unsupported_ext = {"type": "UNSUPPORTED_EXTENSION"}
        instruction = SolanaToken2022CalldataManager._build_extension_instruction_fallback(
            unsupported_ext, mint_address
        )
        self.assertIsNone(instruction)
    
    def test_create_account_instruction(self):
        """测试创建账户指令构建"""
        from unittest.mock import MagicMock
        
        # 模拟Pubkey对象
        mock_payer = MagicMock()
        mock_payer.__str__ = lambda: "PayerAddress123456789"
        
        mock_mint = MagicMock()
        mock_mint.__str__ = lambda: "MintAddress123456789"
        
        instruction = SolanaToken2022CalldataManager._build_create_account_instruction(
            mock_payer, mock_mint, self.token_params
        )
        
        self.assertEqual(instruction["type"], "create_account")
        self.assertIn("accounts", instruction)
        self.assertIn("data", instruction)
        self.assertIn("lamports", instruction["data"])
        self.assertIn("space", instruction["data"])
        self.assertEqual(instruction["data"]["owner"], SolanaToken2022CalldataManager.TOKEN_2022_PROGRAM_ID)
    
    def test_metadata_instruction_building(self):
        """测试元数据指令构建"""
        from unittest.mock import MagicMock
        
        mock_mint = MagicMock()
        mock_mint.__str__ = lambda: "MintAddress123456789"
        
        # 有名称和符号的情况
        instruction = SolanaToken2022CalldataManager._build_metadata_instruction(
            mock_mint, self.token_params
        )
        
        self.assertIsNotNone(instruction)
        self.assertEqual(instruction["type"], "initialize_metadata")
        self.assertEqual(instruction["data"]["name"], "Anchorpy Test Token")
        self.assertEqual(instruction["data"]["symbol"], "ANCHOR")
        
        # 没有名称和符号的情况
        empty_params = {"decimals": 9}
        instruction = SolanaToken2022CalldataManager._build_metadata_instruction(
            mock_mint, empty_params
        )
        
        self.assertIsNone(instruction)
    
    def test_idl_structure(self):
        """测试IDL结构"""
        idl = SolanaToken2022CalldataManager.TOKEN_2022_IDL
        
        # 验证IDL基本结构
        self.assertIn("version", idl)
        self.assertIn("name", idl)
        self.assertIn("instructions", idl)
        self.assertIn("accounts", idl)
        
        # 验证指令
        instruction_names = [inst["name"] for inst in idl["instructions"]]
        expected_instructions = [
            "initializeMint",
            "initializeTransferFeeConfig", 
            "initializeDefaultAccountState",
            "initializeImmutableOwner"
        ]
        
        for expected in expected_instructions:
            self.assertIn(expected, instruction_names)
        
        # 验证账户结构
        account_names = [acc["name"] for acc in idl["accounts"]]
        self.assertIn("Mint", account_names)
