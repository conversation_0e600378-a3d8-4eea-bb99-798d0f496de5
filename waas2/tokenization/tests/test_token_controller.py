from decimal import Decimal
from unittest.mock import Mock, patch

from cobo_waas2 import Token<PERSON><PERSON>tat<PERSON>, TokenizationTokenStandard
from django.test import TestCase

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.controllers.token import TokenizationTokenController
from waas2.tokenization.dao.tokenization import TokenDao
from waas2.tokenization.data.params import (
    ListIssuedTokensParam,
    ListTokenizationHoldingsParam,
)
from waas2.tokenization.data.tokenization import TokenizationExtra


class TestTokenizationTokenController(TestCase):
    def setUp(self):
        # 使用Mock对象避免数据库操作
        self.organization = Mock()
        self.organization.uuid = "test-org-uuid"
        self.organization.id = 1
        self.org_id = self.organization.uuid
        self.biz_org_id = self.organization.id

        # 使用Mock对象避免数据库操作
        self.user1 = Mock()
        self.user1.id = 1
        self.user1.username = "testuser1"

        self.user2 = Mock()
        self.user2.id = 2
        self.user2.username = "testuser2"

        self.account1 = Mock()
        self.account1.id = 1
        self.account1.user = self.user1

        self.account2 = Mock()
        self.account2.id = 2
        self.account2.user = self.user2

        self.token_id = "TEST_TOKEN"
        self.chain_id = "ETH"
        self.address = "******************************************"
        self.wallet_id = "test-wallet-id"

        # 创建真实的 Token
        self.token = TokenDao.create(
            org_id=self.org_id,
            token_id=self.token_id,
            name="Test Token",
            symbol="TEST",
            decimals=18,
            chain_id=self.chain_id,
            token_address=self.address,
            status=TokenizationStatus.ACTIVE.value,
            extra=TokenizationExtra(token_access_activated=True).model_dump(
                mode="json"
            ),
        )

    def tearDown(self):
        # 使用Mock对象，不需要清理数据库
        pass

    @patch(
        "waas2.tokenization.controllers.token.TokenizationTokenController._get_org_token_holdings",
        return_value="1000",
    )
    @patch(
        "waas2.tokenization.controllers.token.TokenizationTokenController._get_token_total_supply",
        return_value="2000",
    )
    def test_convert_token_to_response(self, mock_get_total_supply, mock_get_holdings):
        """测试将Token模型转换为API响应格式"""
        response = TokenizationTokenController._convert_token_to_response(
            self.token, self.org_id, self.biz_org_id
        )

        self.assertEqual(response.token_id, self.token_id)
        self.assertEqual(response.total_supply, "2000")
        self.assertEqual(response.holdings, "1000")
        mock_get_total_supply.assert_called_once_with(self.token)
        mock_get_holdings.assert_called_once_with(
            self.token, self.org_id, self.biz_org_id
        )

    def test_list_issued_tokens_success(self):
        """测试成功获取已发行代币列表"""
        params = ListIssuedTokensParam(chain_id="ETH")

        # Mock 外部依赖
        with patch.object(
            TokenizationTokenController, "_get_token_total_supply", return_value="2000"
        ), patch.object(
            TokenizationTokenController, "_get_org_token_holdings", return_value="1000"
        ):
            result = TokenizationTokenController.list_issued_tokens(
                self.org_id, self.biz_org_id, params
            )

            # 验证结果
            self.assertEqual(len(result.data), 1)
            token_info = result.data[0]
            self.assertEqual(token_info.token_id, self.token_id)
            self.assertEqual(token_info.chain_id, self.chain_id)
            self.assertEqual(token_info.token_address, self.address)
            self.assertEqual(token_info.token_symbol, "TEST")
            self.assertEqual(token_info.decimals, 18)
            self.assertEqual(token_info.total_supply, "2000")
            self.assertEqual(token_info.holdings, "1000")

    def test_list_issued_tokens_empty_result(self):
        """测试获取空的已发行代币列表"""
        # 创建一个不同的 org_id，这样就不会找到任何 token
        empty_org_id = "empty-org-id"
        params = ListIssuedTokensParam(limit=10)

        result = TokenizationTokenController.list_issued_tokens(
            empty_org_id, self.biz_org_id, params
        )
        self.assertEqual(len(result.data), 0)

    def test_list_issued_tokens_with_filters(self):
        """测试带筛选条件的代币列表获取"""
        params = ListIssuedTokensParam(
            chain_id="ETH",
            token_id="TEST_TOKEN",  # 使用实际的 token_id
            token_standard=TokenizationTokenStandard.ERC20,
            status=TokenizationStatus.ACTIVE,
        )

        # Mock 外部依赖
        with patch.object(
            TokenizationTokenController, "_get_token_total_supply", return_value="2000"
        ), patch.object(
            TokenizationTokenController, "_get_org_token_holdings", return_value="1000"
        ):
            result = TokenizationTokenController.list_issued_tokens(
                self.org_id, self.biz_org_id, params
            )

            # 验证结果：应该找到匹配的 token
            self.assertEqual(len(result.data), 1)
            token_info = result.data[0]
            self.assertEqual(token_info.token_id, self.token_id)
            self.assertEqual(token_info.chain_id, self.chain_id)

    @patch(
        "waas2.tokenization.controllers.token.TokenAssetDao.list_by_org_asset_coin_with_balance_limit"
    )
    def test_list_token_holdings_success(self, mock_list_assets):
        """测试成功获取代币持仓信息"""
        # Mock TokenAsset
        mock_token_asset = Mock()
        mock_token_asset.balance = Decimal("100.5")
        mock_token_asset.custody_wallet_id = 1
        mock_token_asset.address = "0x123"
        mock_list_assets.return_value = [mock_token_asset]

        # Mock wallet
        with patch(
            "waas2.tokenization.controllers.token.CustodyWalletDao.list_by_ids"
        ) as mock_list_wallets:
            mock_wallet = Mock()
            mock_wallet.id = 1
            mock_wallet.uuid = "test-wallet-uuid-1"
            mock_wallet.name = "Test Wallet"
            mock_list_wallets.return_value = [mock_wallet]

            # Mock AddressBookBizProcessor
            with patch(
                "waas2.tokenization.controllers.token.AddressBookBizProcessor.get_by_addresses_and_chain"
            ) as mock_address_book:
                mock_address_book.return_value = []

                # Mock process_db_pagination
                with patch(
                    "waas2.tokenization.controllers.token.process_db_pagination"
                ) as mock_pagination:
                    from waas2.devapi.pagination import Pagination

                    mock_pagination.return_value = ([mock_token_asset], Pagination())

                    params = ListTokenizationHoldingsParam(limit=10)
                    result = TokenizationTokenController.list_token_holdings(
                        self.token_id, self.org_id, self.biz_org_id, params
                    )
                    self.assertEqual(len(result.data), 1)
                    self.assertEqual(result.data[0].balance, "0.0000000000000001005")

    def test_list_token_holdings_token_not_found(self):
        """测试获取代币持仓信息 - 代币不存在"""
        params = ListTokenizationHoldingsParam(limit=10)
        with self.assertRaises(InvalidParamException):
            TokenizationTokenController.list_token_holdings(
                "NON_EXISTENT_TOKEN", self.org_id, self.biz_org_id, params
            )

    def test_list_token_holdings_no_wallets(self):
        """测试获取代币持仓信息 - 没有钱包持有该代币"""
        # 使用一个不存在的 token_id，这样就不会找到任何资产
        non_existent_token_id = "NON_EXISTENT_TOKEN"
        params = ListTokenizationHoldingsParam(limit=10)

        # 这应该会抛出 InvalidParamException，因为 token 不存在
        with self.assertRaises(InvalidParamException):
            TokenizationTokenController.list_token_holdings(
                non_existent_token_id, self.org_id, self.biz_org_id, params
            )

    @patch("waas2.tokenization.controllers.token.TokenAssetDao.list_by_org_asset_coin")
    def test_get_org_token_holdings_success(self, mock_list_assets):
        """测试成功获取组织代币持仓总量"""
        # Mock TokenAssets
        mock_token_asset1 = Mock()
        mock_token_asset1.balance = Decimal("100.0")

        mock_token_asset2 = Mock()
        mock_token_asset2.balance = Decimal("200.0")

        mock_list_assets.return_value = [mock_token_asset1, mock_token_asset2]

        result = TokenizationTokenController._get_org_token_holdings(
            self.token, self.org_id, self.biz_org_id
        )
        self.assertEqual(result, "0.0000000000000003")
