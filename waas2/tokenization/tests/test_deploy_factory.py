"""
部署工厂测试
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase
import cobo_waas2

from waas2.tokenization.managers.deploy_factory import DeployManagerFactory
from waas2.tokenization.managers.evm.deploy_manager import ERC20DeployManager
from waas2.tokenization.managers.solana.deploy_manager import SolanaToken2022DeployManager
from waas2.tokenization.enums.chain_type import ChainType


class TestDeployManagerFactory(TestCase):
    """部署管理器工厂测试"""
    
    def test_evm_deploy_manager_creation(self):
        """测试EVM部署管理器创建"""
        manager = DeployManagerFactory.get_manager("ETH")
        self.assertIsInstance(manager, ERC20DeployManager)
        
        # 测试不同EVM链都返回同一实例
        manager2 = DeployManagerFactory.get_manager("BSC")
        self.assertIs(manager, manager2)
    
    def test_solana_deploy_manager_creation(self):
        """测试Solana部署管理器创建"""
        manager = DeployManagerFactory.get_manager("SOL")
        self.assertIsInstance(manager, SolanaToken2022DeployManager)
    
    def test_deploy_manager_by_token_standard(self):
        """测试根据代币标准获取管理器"""
        # ERC20标准
        evm_manager = DeployManagerFactory.get_manager_by_token_standard(
            cobo_waas2.TokenizationTokenStandard.ERC20
        )
        self.assertIsInstance(evm_manager, ERC20DeployManager)
        
        # Solana Token 2022标准
        solana_manager = DeployManagerFactory.get_manager_by_token_standard("SPL_TOKEN_2022")
        self.assertIsInstance(solana_manager, SolanaToken2022DeployManager)
    
    def test_unsupported_token_standard(self):
        """测试不支持的代币标准"""
        with self.assertRaises(ValueError):
            DeployManagerFactory.get_manager_by_token_standard("UNSUPPORTED_STANDARD")
    
    def test_is_supported(self):
        """测试链支持检查"""
        # EVM链应该支持
        self.assertTrue(DeployManagerFactory.is_supported("ETH"))
        self.assertTrue(DeployManagerFactory.is_supported("BSC"))
        
        # Solana链应该支持
        self.assertTrue(DeployManagerFactory.is_supported("SOL"))
        
        # 未知链不支持
        self.assertFalse(DeployManagerFactory.is_supported("UNKNOWN"))


class TestERC20DeployManager(TestCase):
    """ERC20部署管理器测试"""
    
    def setUp(self):
        self.manager = ERC20DeployManager()
    
    @patch('waas2.tokenization.managers.evm.deploy_manager.TokenizationUtils')
    @patch('waas2.tokenization.managers.evm.deploy_manager.TokenDao')
    @patch('waas2.tokenization.managers.evm.deploy_manager.CoboLock')
    def test_deploy_erc20_token(self, mock_lock, mock_token_dao, mock_utils):
        """测试ERC20代币部署"""
        # 模拟参数
        mock_params = MagicMock()
        mock_params.chain_id = "ETH"
        mock_params.token_params.actual_instance.symbol = "TEST"
        mock_params.token_params.actual_instance.name = "Test Token"
        mock_params.token_params.actual_instance.decimals = 18
        mock_params.source.actual_instance.address = "0x123"
        mock_params.source.actual_instance.wallet_id = "wallet123"
        
        # 模拟返回值
        mock_token = MagicMock()
        mock_token.id = 1
        mock_activity = MagicMock()
        mock_activity.uuid = "activity123"
        
        mock_token_dao.create.return_value = mock_token
        mock_utils.create_activity.return_value = mock_activity
        mock_utils.validate_token_id.return_value = None
        
        # 模拟锁
        mock_lock.return_value.__enter__ = MagicMock()
        mock_lock.return_value.__exit__ = MagicMock()
        
        # 执行部署
        with patch.object(self.manager, 'build_deploy_calldata', return_value="0x123"):
            with patch.object(self.manager, 'predict_contract_address', return_value="0xcontract"):
                with patch.object(self.manager, 'execute_deployment', return_value=MagicMock()):
                    result = self.manager.deploy(
                        params=mock_params,
                        org_id="org123",
                        biz_org_id=1,
                        api_request_info={},
                        sign_info={}
                    )
        
        self.assertEqual(result, "activity123")
    
    def test_validate_deploy_params(self):
        """测试ERC20部署参数验证"""
        # 创建有效参数
        mock_params = MagicMock()
        mock_params.chain_id = "ETH"
        mock_params.token_params.actual_instance.standard = cobo_waas2.TokenizationTokenStandard.ERC20
        
        # 应该不抛出异常
        with patch.object(self.manager, '_is_supported_chain', return_value=True):
            self.manager.validate_deploy_params(mock_params)
        
        # 测试不支持的链
        with patch.object(self.manager, '_is_supported_chain', return_value=False):
            with self.assertRaises(ValueError):
                self.manager.validate_deploy_params(mock_params)


class TestSolanaToken2022DeployManager(TestCase):
    """Solana Token 2022部署管理器测试"""
    
    def setUp(self):
        self.manager = SolanaToken2022DeployManager()
    
    @patch('waas2.tokenization.managers.solana.deploy_manager.TokenizationUtils')
    @patch('waas2.tokenization.managers.solana.deploy_manager.TokenDao')
    def test_deploy_solana_token(self, mock_token_dao, mock_utils):
        """测试Solana Token 2022部署"""
        # 模拟参数
        mock_params = MagicMock()
        mock_params.chain_id = "SOL"
        mock_params.token_params.actual_instance.symbol = "SOLTEST"
        mock_params.token_params.actual_instance.name = "Solana Test Token"
        mock_params.token_params.actual_instance.decimals = 9
        mock_params.token_params.actual_instance.mint_authority = "mint_auth_address"
        mock_params.token_params.actual_instance.extensions = []
        mock_params.source.actual_instance.address = "sol_address"
        
        # 模拟返回值
        mock_token = MagicMock()
        mock_token.id = 1
        mock_activity = MagicMock()
        mock_activity.uuid = "sol_activity123"
        
        mock_token_dao.create.return_value = mock_token
        mock_utils.create_activity.return_value = mock_activity
        mock_utils.validate_token_id.return_value = None
        
        # 执行部署
        with patch.object(self.manager, 'build_deploy_calldata', return_value='{"type": "solana_deploy"}'):
            with patch.object(self.manager, 'predict_contract_address', return_value="sol_mint_address"):
                with patch.object(self.manager, 'execute_deployment', return_value=MagicMock()):
                    result = self.manager.deploy(
                        params=mock_params,
                        org_id="org123",
                        biz_org_id=1,
                        api_request_info={},
                        sign_info={}
                    )
        
        self.assertEqual(result, "sol_activity123")
    
    def test_validate_solana_params(self):
        """测试Solana参数验证"""
        from waas2.tokenization.data.solana import SolanaToken2022Params
        
        # 有效参数
        valid_params = SolanaToken2022Params(
            name="Test Token",
            symbol="TEST",
            decimals=9,
            mint_authority="mint_auth_address"
        )
        
        # 应该不抛出异常
        self.manager._validate_solana_params(valid_params)
        
        # 缺少mint_authority
        invalid_params = SolanaToken2022Params(
            name="Test Token",
            symbol="TEST",
            decimals=9,
            mint_authority=""
        )
        
        with self.assertRaises(ValueError):
            self.manager._validate_solana_params(invalid_params)
