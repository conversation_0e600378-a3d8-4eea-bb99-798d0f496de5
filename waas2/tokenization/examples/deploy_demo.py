"""
代币部署演示脚本
展示如何使用新的calldata管理器部署EVM和Solana代币
"""
import json
import cobo_waas2
from waas2.tokenization.managers.deploy import TokenizationDeployManager


def demo_evm_deployment():
    """演示EVM代币部署"""
    print("=== EVM代币部署演示 ===")
    
    # 创建EVM代币参数
    evm_params = cobo_waas2.TokenizationERC20TokenParams(
        standard=cobo_waas2.TokenizationTokenStandard.ERC20,
        name="Demo EVM Token",
        symbol="DEMO",
        decimals=18,
        permissions=cobo_waas2.TokenizationTokenPermissionParams(
            admin=["******************************************"]
        )
    )
    
    chain_id = "ETH"
    execute_address = "******************************************"
    
    try:
        # 构建calldata
        calldata = TokenizationDeployManager.build_deploy_calldata(
            chain_id, execute_address, evm_params
        )
        print(f"EVM Calldata: {calldata[:100]}...")
        
        # 预测合约地址
        contract_address = TokenizationDeployManager._predict_contract_address(
            chain_id, execute_address, calldata
        )
        print(f"预测的EVM合约地址: {contract_address}")
        
    except Exception as e:
        print(f"EVM部署演示失败: {e}")
    
    print()


def demo_solana_deployment():
    """演示Solana Token 2022部署"""
    print("=== Solana Token 2022部署演示 ===")
    
    # 创建Solana代币参数
    solana_params = {
        "name": "Demo Solana Token",
        "symbol": "SOLTEST",
        "decimals": 9,
        "mint_authority": "SolMintAuthority123456789",
        "freeze_authority": "SolFreezeAuthority123456789",
        "extensions": [
            {
                "type": "TRANSFER_FEE",
                "transfer_fee_basis_points": 100,  # 1%
                "maximum_fee": 1000000,  # 1 SOL
                "transfer_fee_config_authority": "SolFeeAuthority123456789",
                "withdraw_withheld_authority": "SolWithdrawAuthority123456789"
            },
            {
                "type": "DEFAULT_ACCOUNT_STATE",
                "state": "initialized"
            }
        ]
    }
    
    chain_id = "SOL"
    execute_address = "SolExecuteAddress123456789"
    
    try:
        # 构建calldata
        calldata = TokenizationDeployManager.build_deploy_calldata(
            chain_id, execute_address, solana_params
        )
        
        # 解析并美化显示
        instructions = json.loads(calldata)
        print("Solana指令结构:")
        print(f"  类型: {instructions['type']}")
        print(f"  链ID: {instructions['chain_id']}")
        print(f"  支付者: {instructions['payer']}")
        print(f"  Mint地址: {instructions['mint_address']}")
        print(f"  指令数量: {len(instructions['instructions'])}")
        
        # 显示指令列表
        print("  指令序列:")
        for i, instruction in enumerate(instructions['instructions']):
            print(f"    {i+1}. {instruction['type']}")
        
        # 预测mint地址
        mint_address = TokenizationDeployManager._predict_contract_address(
            chain_id, execute_address, calldata
        )
        print(f"预测的Solana Mint地址: {mint_address}")
        
    except Exception as e:
        print(f"Solana部署演示失败: {e}")
    
    print()


def demo_chain_detection():
    """演示链类型检测"""
    print("=== 链类型检测演示 ===")
    
    from waas2.tokenization.utils.chain_detector import ChainDetector
    
    test_chains = ["ETH", "BSC", "POLYGON", "SOL", "SOL_DEVNET", "SOL_TESTNET", "UNKNOWN"]
    
    for chain_id in test_chains:
        chain_type = ChainDetector.get_chain_type(chain_id)
        print(f"  {chain_id} -> {chain_type}")
    
    print()


def main():
    """主函数"""
    print("🚀 代币化模块多链支持演示")
    print("=" * 50)
    
    # 演示链类型检测
    demo_chain_detection()
    
    # 演示EVM部署
    demo_evm_deployment()
    
    # 演示Solana部署
    demo_solana_deployment()
    
    print("✅ 演示完成！")
    print("\n📝 总结:")
    print("1. ✅ 链类型自动检测 (EVM/Solana)")
    print("2. ✅ EVM calldata构建 (CoboERC20)")
    print("3. ✅ Solana指令构建 (Token 2022)")
    print("4. ✅ 合约地址预测")
    print("5. ✅ Token Extensions支持")
    print("6. ✅ 向后兼容性保持")


if __name__ == "__main__":
    main()
