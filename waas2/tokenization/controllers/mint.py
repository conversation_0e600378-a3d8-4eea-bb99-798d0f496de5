from decimal import Decimal
from typing import Any, Dict

import cobo_waas2

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.dao.tokenization import (
    TokenAllowlistDao,
    TokenBlocklistDao,
    TokenRoleDao,
)
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.managers.mint import TokenizationMintManager
from waas2.tokenization.utils.tokenization import TokenizationUtils


class TokenizationMintController:
    @classmethod
    def mint_tokens(
        cls,
        token_id: str,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationMintTokenRequest,
        sign_info: Dict[str, Any],
        api_request_info: Dict[str, Any],
    ) -> cobo_waas2.TokenizationOperationResponse:
        """
        执行代币铸造操作
        """
        # 获取并验证 token 信息
        token = TokenizationUtils.get_and_validate_token(token_id, org_id)

        # 验证操作权限和参数
        cls._validate_mint_request(token, params)

        # 调用 manager 执行具体的铸造逻辑
        activity_id = TokenizationMintManager.mint(
            token=token,
            params=params,
            org_id=org_id,
            biz_org_id=biz_org_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        return cobo_waas2.TokenizationOperationResponse(activity_id=str(activity_id))

    @classmethod
    def _validate_mint_request(
        cls, token, params: cobo_waas2.TokenizationMintTokenRequest
    ):
        """验证铸造请求的权限和参数"""
        # 验证操作权限
        source_data = params.source.actual_instance

        # 验证操作权限
        if not TokenRoleDao.has_role(
            token.token_id, source_data.address, TokenizationRole.MINTER
        ):
            raise InvalidParamException(
                f"Address {source_data.address} does not have minter role for token {token.token_id}"
            )

        # 验证铸造参数
        mints = params.mints
        if not mints:
            raise InvalidParamException("Mints list cannot be empty")

        mint_addresses = [x.to_address for x in mints]
        # 检查目标地址是否在黑名单中
        if TokenBlocklistDao.is_any_address_blocked(token.token_id, mint_addresses):
            raise InvalidParamException("Addresses is blocked")
        # 如果启用了白名单，检查目标地址是否在白名单中
        extra = TokenizationExtra.model_validate(token.extra)
        if extra.token_access_activated:
            if not TokenAllowlistDao.are_all_addresses_allowed(
                token.token_id, mint_addresses
            ):
                raise InvalidParamException("Addresses not in allowlist")

        # 验证铸造数量
        for mint in mints:
            try:
                amount = Decimal(mint.amount)
                if amount <= Decimal(0):
                    raise InvalidParamException(f"Invalid mint amount: {mint.amount}")
                # 验证精度
                exponent = amount.normalize().as_tuple().exponent
                if -exponent > token.decimals:
                    raise InvalidParamException(
                        f"Invalid mint amount: {mint.amount} has more decimal places than supported"
                    )
            except ValueError:
                raise InvalidParamException(
                    f"Invalid mint amount format: {mint.amount}"
                )
