import logging

import cobo_waas2
from django.db import transaction

from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.dao.tokenization import (
    TokenAllowlistDao,
    TokenBlocklistDao,
    TokenRoleDao,
)
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.managers.contract_call import TokenizationContractCallManager
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.tokenization.utils.selector import CoboERC20SelectorUtils
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationContractCallController:
    """代币化合约调用控制器"""

    # Selector 与所需角色的映射
    SELECTOR_REQUIRED_ROLES = {
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["mint"]: [TokenizationRole.MINTER],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["burn"]: [TokenizationRole.BURNER],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["burnFrom"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["pause"]: [TokenizationRole.PAUSER],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["unpause"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["accessListAdd"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["accessListRemove"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["toggleAccesslist"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["blockListAdd"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["blockListRemove"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["grantRole"]: [
            TokenizationRole.ADMIN
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["revokeRole"]: [
            TokenizationRole.ADMIN
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["renounceRole"]: [
            TokenizationRole.ADMIN
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["contractUriUpdate"]: [
            TokenizationRole.MANAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["salvageERC20"]: [
            TokenizationRole.SALVAGER
        ],
        CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["salvageNative"]: [
            TokenizationRole.SALVAGER
        ],
        # multicall 需要根据具体调用的方法来判断
    }

    @classmethod
    @transaction.atomic
    def contract_call(
        cls,
        token_id: str,
        org_id: str,
        biz_org_id: int,
        params: cobo_waas2.TokenizationContractCallRequest,
        sign_info: dict,
        api_request_info: dict,
    ) -> cobo_waas2.TokenizationOperationResponse:
        """执行代币合约调用"""

        # 获取并验证 token
        token = TokenizationUtils.get_and_validate_token(
            token_id, org_id, pause_enabled=True
        )

        # 验证 calldata 和权限
        cls._validate_calldata_and_permissions(token, params)

        # 使用 manager 执行合约调用
        activity_id = TokenizationContractCallManager.contract_call(
            token=token,
            params=params,
            org_id=org_id,
            biz_org_id=biz_org_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        return cobo_waas2.TokenizationOperationResponse(activity_id=str(activity_id))

    @classmethod
    def _validate_calldata_and_permissions(
        cls, token: Token, params: cobo_waas2.TokenizationContractCallRequest
    ) -> None:
        """验证 calldata 是否为合法的 token 合约方法调用，并检查权限"""
        try:
            # 获取合约实例
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )

            # 提取 selector
            calldata = params.data.actual_instance.calldata
            if not calldata.startswith("0x") or len(calldata) < 10:
                raise InvalidParamException("Invalid calldata format")

            selector = calldata[2:10].lower()  # 去掉 0x 前缀，取前4字节

            # 获取函数名用于日志
            function_name = CoboERC20SelectorUtils.get_name_by_selector(selector)
            logger.info(f"Validating function selector: {selector} ({function_name})")

            # 解码 calldata 以获取参数（用于权限检查）
            decoded = contract.decode_function_input(calldata)
            # function = decoded[0]
            function_params = decoded[1]

            # 检查权限
            source_address = params.source.actual_instance.address
            cls._check_selector_permission(
                token, source_address, selector, function_params
            )

            # 如果是 multicall，需要进一步验证嵌套的调用
            if selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"]:
                cls._validate_multicall_data(
                    token, contract, function_params, source_address
                )

        except Exception as e:
            if isinstance(e, InvalidParamException):
                raise
            logger.error("Failed to validate calldata: %s", str(e))
            raise InvalidParamException("Invalid calldata format")

    @classmethod
    def _check_selector_permission(
        cls, token: Token, source_address: str, selector: str, params: dict
    ) -> None:
        """检查 selector 调用权限"""
        # 对于 transfer 相关操作，需要特殊检查
        if selector in [
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["transfer"],
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["transferFrom"],
        ]:
            cls._check_transfer_permissions(token, source_address, selector, params)
            return

        # 检查是否需要特殊角色
        required_roles = cls.SELECTOR_REQUIRED_ROLES.get(selector, [])
        if required_roles:
            if not TokenRoleDao.has_any_role(
                token.token_id, source_address, required_roles
            ):
                role_names = [role.name for role in required_roles]
                function_name = CoboERC20SelectorUtils.get_name_by_selector(selector)
                raise InvalidParamException(
                    f"Address {source_address} does not have required role ({', '.join(role_names)}) for function '{function_name}'"
                )

        # 只读方法不需要特殊权限，直接返回

    @classmethod
    def _check_transfer_permissions(
        cls, token: Token, source_address: str, selector: str, params: dict
    ) -> None:
        """检查转账相关操作的权限"""
        # 检查调用者地址的黑白名单限制（合约中 transfer 和 transferFrom 都会检查 _msgSender()）
        cls._check_address_restrictions(token, [source_address])

        # 对于 transferFrom，检查 from 地址
        if selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["transferFrom"]:
            from_address = params.get("from")
            if from_address:
                # 检查 from 地址的黑白名单限制（合约中会检查 from 地址）
                cls._check_address_restrictions(token, [from_address])

                if from_address.lower() != source_address.lower():
                    # 检查是否有足够的授权额度（这里简化处理，实际应该查询链上数据）
                    logger.warning(
                        f"TransferFrom from different address: {from_address} vs {source_address}"
                    )

        # 检查目标地址的黑白名单限制
        to_address = params.get("to") or params.get("recipient")
        if to_address:
            cls._check_address_restrictions(token, [to_address])

    @classmethod
    def _check_address_restrictions(cls, token: Token, addresses: list) -> None:
        """检查地址是否受到限制"""
        # 检查黑名单
        if TokenBlocklistDao.is_any_address_blocked(token.token_id, addresses):
            raise InvalidParamException("One or more addresses are blocked")

        # 检查白名单（如果启用）
        extra = TokenizationExtra.model_validate(token.extra)
        if extra.token_access_activated:
            if not TokenAllowlistDao.are_all_addresses_allowed(
                token.token_id, addresses
            ):
                raise InvalidParamException(
                    "One or more addresses are not in allowlist"
                )

    @classmethod
    def _validate_multicall_data(
        cls, token: Token, contract, params: dict, source_address: str
    ) -> None:
        """验证 multicall 中的嵌套调用"""
        if "data" not in params:
            raise InvalidParamException("Multicall missing 'data' parameter")

        data_list = params["data"]
        if not isinstance(data_list, list):
            raise InvalidParamException("Multicall 'data' must be a list")

        for calldata_bytes in data_list:
            try:
                # 将 bytes 转换为 hex string
                if isinstance(calldata_bytes, bytes):
                    nested_calldata = "0x" + calldata_bytes.hex()
                else:
                    nested_calldata = calldata_bytes

                # 提取嵌套调用的 selector
                if not nested_calldata.startswith("0x") or len(nested_calldata) < 10:
                    raise InvalidParamException("Invalid nested calldata format")

                nested_selector = nested_calldata[2:10].lower()
                nested_function_name = CoboERC20SelectorUtils.get_name_by_selector(
                    nested_selector
                )

                logger.info(
                    f"Validating nested function selector: {nested_selector} ({nested_function_name})"
                )

                # 检查嵌套 selector 是否在允许列表中（不允许嵌套的 multicall）
                if (
                    nested_selector
                    == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"]
                ):
                    raise InvalidParamException("Nested multicall is not allowed")

                # 解码嵌套调用以检查权限
                nested_decoded = contract.decode_function_input(nested_calldata)
                nested_params = nested_decoded[1]

                # 检查嵌套方法的权限
                cls._check_selector_permission(
                    token, source_address, nested_selector, nested_params
                )

            except Exception as e:
                if isinstance(e, InvalidParamException):
                    raise
                logger.error("Failed to validate nested calldata: %s", str(e))
                raise InvalidParamException("Invalid nested calldata in multicall")
