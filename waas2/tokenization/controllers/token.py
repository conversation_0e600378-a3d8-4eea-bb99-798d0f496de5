import logging
from decimal import Decimal
from typing import List

import cobo_waas2

from custody.cobo.utils import decimal_to_string
from custody.custody.dao.custody_wallet import Custody<PERSON>alletDao
from custody.wallet.dao.wallet import TokenAssetDao
from waas2.custodial.processors.address import AddressBookBizProcessor
from waas2.devapi.pagination import process_db_pagination
from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.dao.tokenization import TokenDao, TokenRoleDao
from waas2.tokenization.data.params import (
    ListIssuedTokensParam,
    ListTokenizationHoldingsParam,
)
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.enums.tokenization import TokenizationRole
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.contract import CoboERC20ContractHelper

logger = logging.getLogger("waas2.tokenization")


class TokenizationTokenController:
    @classmethod
    def get_token_info(
        cls, token_id: str, org_id: str, biz_org_id: int
    ) -> cobo_waas2.TokenizationTokenDetailInfo:
        """获取代币信息"""
        token = TokenDao.get_by_token_id_and_org_id(token_id, org_id)
        if not token:
            raise InvalidParamException(f"Token {token_id} not found")

        token_info = cls._convert_token_to_detail_response(token, org_id, biz_org_id)
        return token_info

    @classmethod
    def list_issued_tokens(
        cls,
        org_id: str,
        biz_org_id: int,
        params: ListIssuedTokensParam,
    ) -> cobo_waas2.TokenizationListTokenInfoResponse:
        """获取已发行代币列表"""
        # 查询代币列表
        tokens = TokenDao.list_by_filters(
            org_id=org_id,
            chain_id=params.chain_id,
            token_id=params.token_id,
            status=params.status,
            standard=params.token_standard,
        )

        # 处理分页
        paged_tokens, pagination = process_db_pagination(
            tokens,
            limit=params.limit,
            before=params.before,
            after=params.after,
        )

        # 转换为响应格式
        token_infos = [
            cls._convert_token_to_response(token, org_id, biz_org_id)
            for token in paged_tokens
        ]

        return cobo_waas2.TokenizationListTokenInfoResponse(
            data=token_infos,
            pagination=cobo_waas2.Pagination.from_dict(pagination.to_dict()),
        )

    @classmethod
    def list_token_holdings(
        cls,
        token_id: str,
        org_id: str,
        biz_org_id: int,
        params: ListTokenizationHoldingsParam,
    ) -> cobo_waas2.TokenizationListHoldingsResponse:
        """获取代币持仓信息"""
        # 验证代币存在
        token = TokenDao.get_by_token_id_and_org_id(token_id, org_id)
        if not token:
            raise InvalidParamException(f"Token {token_id} not found")

        assets = TokenAssetDao.list_by_org_asset_coin_with_balance_limit(
            biz_org_id, token_id, 0
        )
        paged_assets, pagination = process_db_pagination(
            assets,
            limit=params.limit,
            before=params.before,
            after=params.after,
        )

        paged_holdings = []
        if paged_assets:
            wallets = CustodyWalletDao.list_by_ids(
                [x.custody_wallet_id for x in paged_assets]
            )
            wallet_id_to_wallet = {wallet.id: wallet for wallet in wallets}

            # 批量查询所有地址的 label
            address_books = AddressBookBizProcessor.get_by_addresses_and_chain(
                org_id=org_id,
                addresses=[x.address for x in paged_assets],
            )
            address_label_map = {book.address: book.label for book in address_books}

            for asset in assets:
                custody_wallet = wallet_id_to_wallet[asset.custody_wallet_id]
                paged_holdings.append(
                    cobo_waas2.TokenizationHoldingInfo(
                        wallet_id=custody_wallet.uuid,
                        wallet_name=custody_wallet.name,
                        address=asset.address,
                        balance=decimal_to_string(
                            Decimal(asset.balance) / Decimal(10**token.decimals)
                        ),
                        address_label=address_label_map.get(asset.address, ""),
                    )
                )

        return cobo_waas2.TokenizationListHoldingsResponse(
            data=paged_holdings,
            pagination=cobo_waas2.Pagination.from_dict(pagination.to_dict()),
        )

    @classmethod
    def _convert_token_to_detail_response(
        cls, token, org_id: str, biz_org_id: int = None
    ) -> cobo_waas2.TokenizationTokenDetailInfo:
        """将Token模型转换为详情API响应格式"""

        permissions = cls._get_token_permissions(token)
        resp = cls._convert_token_to_response(token, org_id, biz_org_id)

        return cobo_waas2.TokenizationTokenDetailInfo(
            **resp.__dict__,
            permissions=permissions,
        )

    @classmethod
    def _get_token_total_supply(cls, token: Token) -> str:
        """获取代币总供应量"""
        try:
            # 获取合约实例
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )

            # 调用 totalSupply() 方法
            total_supply_wei = contract.functions.totalSupply().call()

            # 转换为可读格式（考虑 decimals）
            total_supply = Decimal(total_supply_wei) / Decimal(10**token.decimals)

            # 返回字符串格式，保持精度
            return decimal_to_string(total_supply)

        except Exception as e:
            logger.error(f"Failed to get total supply for token {token.token_id}: {e}")
            return "0"

    @classmethod
    def _get_token_permissions(
        cls, token: Token
    ) -> List[cobo_waas2.TokenizationAddressPermission]:
        """获取代币权限信息"""
        address_roles = TokenRoleDao.list_by_token(token.token_id)

        def _get_permissions(
            roles: int,
        ) -> List[cobo_waas2.TokenizationTokenPermissionType]:
            permissions = []
            if TokenizationRole.has_role(roles, TokenizationRole.ADMIN):
                permissions.append(
                    cobo_waas2.TokenizationTokenPermissionType.MANAGEPERMISSIONS
                )
            if TokenizationRole.has_role(roles, TokenizationRole.MANAGER):
                permissions.append(
                    cobo_waas2.TokenizationTokenPermissionType.MANAGEACCESSANDCONTROLS
                )
            if TokenizationRole.has_role(roles, TokenizationRole.MINTER):
                permissions.append(
                    cobo_waas2.TokenizationTokenPermissionType.MINTTOKENS
                )
            if TokenizationRole.has_role(roles, TokenizationRole.BURNER):
                permissions.append(
                    cobo_waas2.TokenizationTokenPermissionType.BURNTOKENS
                )
            if TokenizationRole.has_role(roles, TokenizationRole.SALVAGER):
                permissions.append(
                    cobo_waas2.TokenizationTokenPermissionType.RECOVERTOKENS
                )
            if TokenizationRole.has_role(roles, TokenizationRole.UPGRADER):
                permissions.append(
                    cobo_waas2.TokenizationTokenPermissionType.UPGRADECONTRACT
                )
            if TokenizationRole.has_role(roles, TokenizationRole.PAUSER):
                permissions.append(
                    cobo_waas2.TokenizationTokenPermissionType.PAUSECONTRACT
                )
            return permissions

        results = []
        for x in address_roles:
            perms = _get_permissions(x.roles)
            if perms:
                results.append(
                    cobo_waas2.TokenizationAddressPermission(
                        execution_address=x.address,
                        permissions=perms,
                        created_timestamp=int(x.created_time.timestamp() * 1000),
                    )
                )

        return results

    @classmethod
    def _convert_token_to_response(
        cls, token: Token, org_id: str, biz_org_id: int = None
    ) -> cobo_waas2.TokenizationTokenInfo:
        """将Token模型转换为API响应格式"""
        extra = TokenizationExtra.model_validate(token.extra)

        # 获取代币总供应量
        total_supply = "0"
        if token.status in (
            cobo_waas2.TokenizationStatus.PAUSING,
            cobo_waas2.TokenizationStatus.ACTIVE,
        ):
            total_supply = cls._get_token_total_supply(token)

        # 获取组织内的代币持仓总量
        holdings = "0"
        if biz_org_id is not None:
            holdings = cls._get_org_token_holdings(token, org_id, biz_org_id)

        return cobo_waas2.TokenizationTokenInfo(
            token_id=token.token_id or "",
            chain_id=token.chain_id,
            token_name=token.name,
            token_address=token.token_address,
            token_symbol=token.symbol,
            decimals=token.decimals,
            status=cobo_waas2.TokenizationStatus(token.status),
            token_access_activated=extra.token_access_activated,
            total_supply=total_supply,
            holdings=holdings,
        )

    @classmethod
    def _get_org_token_holdings(cls, token: Token, org_id: str, biz_org_id: int) -> str:
        """获取组织内所有钱包的代币持仓总量"""
        assets = TokenAssetDao.list_by_org_asset_coin(biz_org_id, [token.token_id])
        total_holdings = Decimal(0)
        for asset in assets:
            total_holdings += asset.balance
        return decimal_to_string(
            Decimal(total_holdings) / Decimal(10**token.decimals)
        )
