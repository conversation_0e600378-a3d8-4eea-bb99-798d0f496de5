import functools
import logging

import cobo_waas2
from django.db import transaction

from custody.custody.dao.organization import OrganizationDao
from custody.custody.models.organization import Organization
from custody.custody.models.transaction import EOATransactionDetail
from custody.web3.data.enums import TransactionRequestStatus, TransactionRequestType
from custody.web3.models.transaction_request import TransactionRequest
from waas2.mpc.processors.transaction import MPCWalletTransactionProcessor
from waas2.tokenization.dao.tokenization import (
    ActivityDao,
    ActivityTransactionDao,
    TokenDao,
)
from waas2.tokenization.managers.activity_manager import TokenizationActivityManager
from waas2.tokenization.managers.allowlist import TokenizationAllowlistManager
from waas2.tokenization.managers.auto_listing import AutoListingManager
from waas2.tokenization.managers.blocklist import TokenizationBlocklistManager
from waas2.tokenization.managers.contract_call import TokenizationContractCallManager
from waas2.tokenization.managers.deploy import TokenizationDeployManager
from waas2.tokenization.managers.gas_sponsor import GasSponsorManager
from waas2.tokenization.managers.pause import TokenizationPauseManager
from waas2.tokenization.managers.unpause import TokenizationUnpauseManager
from waas2.tokenization.models.activity import Activity, ActivityTransaction
from waas2.tokenization.models.tokenization import Token
from waas2.transaction_query.dao.transaction_source import TransactionSourceDao
from waas2.transaction_query.enums import QueryTransactionSourceType

logger = logging.getLogger("waas2.tokenization")


def updates_transaction_record(func):
    @functools.wraps(func)
    def wrapper(self: "TokenizationSignalHandler", *args, **kwargs):
        self._update_transaction_status()
        return func(self, *args, **kwargs)

    return wrapper


class TokenizationSignalHandler:
    @classmethod
    def create(
        cls,
        tx_request: TransactionRequest,
        eoa_tx: EOATransactionDetail,
        ignore_status: bool,
    ) -> "TokenizationSignalHandler | None":
        org: Organization = OrganizationDao.get_by_id_or_raise(
            int(str(tx_request.org_id))
        )
        activity_transaction = ActivityTransactionDao.get_by_request_id(
            org_id=str(org.uuid),
            request_id=str(tx_request.request_id),
        )
        if not activity_transaction:
            return None
        if not ignore_status and activity_transaction.status == tx_request.status:
            return None
        activity = ActivityDao.get_by_uuid(str(activity_transaction.activity_id))
        if not activity:
            return None
        token = TokenDao.get_by_uuid(activity.token_uuid)
        if not token:
            return None
        logger.info(
            f"Created tokenization signal handler for {token.token_id}, request id: {tx_request.request_id}, status: {tx_request.status}"
        )
        return cls(tx_request, eoa_tx, activity_transaction, activity, token)

    def __init__(
        self,
        tx_request: TransactionRequest,
        eoa_tx: EOATransactionDetail,
        activity_transaction: ActivityTransaction,
        activity: Activity,
        token: Token,
    ):
        self.tx_request = tx_request
        self.eoa_tx = eoa_tx
        self.activity_transaction = activity_transaction
        self.activity = activity
        self.token = token

    def _is_speedup_tx(self, tx_request: TransactionRequest) -> bool:
        """判断是否为加速交易"""
        return (
            tx_request.type
            in TransactionRequestType.get_mpc_rbf_speed_up_request_type()
        )

    def _is_drop_tx(self, tx_request: TransactionRequest) -> bool:
        """判断是否为丢弃交易"""
        return tx_request.type in TransactionRequestType.get_mpc_rbf_drop_request_type()

    def _is_rbf_tx(self, tx_request: TransactionRequest) -> bool:
        """判断是否为RBF交易（包括加速和丢弃）"""
        return self._is_speedup_tx(tx_request) or self._is_drop_tx(tx_request)

    def _update_transaction_status(self):
        ActivityTransactionDao.update_by_id(
            int(str(self.activity_transaction.id)),
            status=self.tx_request.status,
        )

    def _fail_activity(self):
        ActivityDao.update_by_id(
            int(str(self.activity.id)),
            status=cobo_waas2.TokenizationActivityStatus.FAILED,
        )

        match self.activity.type:
            case cobo_waas2.TokenizationOperationType.ISSUE:
                TokenizationDeployManager.on_deploy_failed(
                    activity_id=str(self.activity.uuid),
                )
            case cobo_waas2.TokenizationOperationType.PAUSE:
                TokenizationPauseManager.on_pause_failed(
                    activity_id=str(self.activity.uuid),
                )
            case cobo_waas2.TokenizationOperationType.UNPAUSE:
                TokenizationUnpauseManager.on_unpause_failed(
                    activity_id=str(self.activity.uuid),
                )
            case cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES:
                TokenizationAllowlistManager.on_allowlist_failed(
                    activity_id=str(self.activity.uuid),
                )
            case cobo_waas2.TokenizationOperationType.UPDATEBLOCKLISTADDRESSES:
                TokenizationBlocklistManager.on_blocklist_failed(
                    activity_id=str(self.activity.uuid),
                )
            case cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST:
                # 白名单激活状态切换失败，无需特殊处理
                pass
            case cobo_waas2.TokenizationOperationType.CONTRACTCALL:
                # 合约调用失败，使用专门的处理器
                TokenizationContractCallManager.on_contract_call_failed(
                    activity_id=str(self.activity.uuid)
                )
            case _:
                pass

    @transaction.atomic
    @updates_transaction_record
    def handle_tx_failed(self):
        """处理交易失败事件

        参考staking模块的RBF处理逻辑：
        1. 对于RBF交易，只更新交易状态，不更新活动状态
        2. 对于原始交易，需要检查是否有成功的RBF交易
        3. 如果有成功的RBF交易且不是drop交易，则不标记活动失败
        """
        # 对于 rbf 交易, 我们不需要更新 activity status
        if self._is_rbf_tx(self.tx_request):
            return

        # 只处理原始交易的失败回调
        onchained_rbf_tx = (
            MPCWalletTransactionProcessor.get_last_success_rbf_transaction(
                self.tx_request.cobo_id
            )
        )
        logger.info(
            f"onchained_rbf_tx: {onchained_rbf_tx} original tx: {self.tx_request}"
        )
        if not onchained_rbf_tx:
            # 没有 rbf 交易/最终上链的是当前交易, 直接更新为失败
            self._fail_activity()
            return

        if self._is_drop_tx(onchained_rbf_tx) or (
            onchained_rbf_tx.status != TransactionRequestStatus.STATUS_SUCCESS
        ):
            self._fail_activity()
            return

        # 原始交易虽然失败了, 但是 rbf 交易成功了, 不做失败回调

    @transaction.atomic
    @updates_transaction_record
    def handle_tx_rejected(self):
        """处理交易拒绝事件

        对于RBF交易，不根据它们的失败来更新活动状态
        """
        if self._is_rbf_tx(self.tx_request):
            # 对于 rbf 交易, 我们不根据他们的失败来更新 activity
            return
        else:
            self._fail_activity()

    @transaction.atomic
    @updates_transaction_record
    def handle_tx_canceled(self):
        """处理交易取消事件

        对于RBF交易，不根据它们的失败来更新活动状态
        """
        if self._is_rbf_tx(self.tx_request):
            # 对于 rbf 交易, 我们不根据他们的失败来更新 activity
            return
        else:
            self._fail_activity()

    @transaction.atomic
    @updates_transaction_record
    def handle_tx_on_chained(self):
        """处理交易上链事件

        参考staking模块的RBF处理逻辑：
        只要不是drop交易，就代表原始交易成功了，调用成功的回调函数
        对于失败交易，统一在handle_tx_failed中进行处理
        """
        # 只要不是 drop 交易, 那就代表原始交易成功了, 调用成功的回调函数
        # 对于失败交易, 统一在 handle_tx_failed 中进行处理
        if not self._is_drop_tx(self.tx_request):
            self._handle_success()

    def _handle_success(self):
        """处理交易成功的逻辑"""
        ActivityDao.update_by_id(
            int(str(self.activity.id)),
            status=cobo_waas2.TokenizationActivityStatus.SUCCESS,
        )

        match self.activity.type:
            case cobo_waas2.TokenizationOperationType.ISSUE:
                if (
                    self.activity_transaction.action
                    == GasSponsorManager.TRANSACTION_ACTION
                ):
                    # Gas sponsor transactions don't need callback, logic is handled in sync_gas_sponsor_tx_status.py
                    pass
                else:
                    TokenizationDeployManager.on_deploy_on_chained(
                        activity_id=str(self.activity.uuid),
                    )
                    token = TokenDao.get_by_id_or_raise(
                        self.token.id,
                    )
                    # refresh token
                    try:
                        logger.info(
                            f"Auto listing token {token.token_id} for org {self.tx_request.org_id}"
                        )
                        AutoListingManager.list_token_for_org(
                            chain_id=str(token.chain_id),
                            token_id=str(token.token_id),
                            org_id=int(str(self.tx_request.org_id)),
                            token_address=str(token.token_address),
                        )
                    except Exception:
                        logger.error("Auto listing failed", exc_info=True)
                        raise
            case cobo_waas2.TokenizationOperationType.PAUSE:
                TokenizationPauseManager.on_pause_success(
                    activity_id=str(self.activity.uuid),
                )
            case cobo_waas2.TokenizationOperationType.UNPAUSE:
                TokenizationUnpauseManager.on_unpause_success(
                    activity_id=str(self.activity.uuid),
                )
            case cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES:
                TokenizationAllowlistManager.on_allowlist_success(
                    activity_id=str(self.activity.uuid),
                    transaction_request=self.tx_request,
                )
            case cobo_waas2.TokenizationOperationType.UPDATEBLOCKLISTADDRESSES:
                TokenizationBlocklistManager.on_block_success(
                    activity_id=str(self.activity.uuid),
                    transaction_request=self.tx_request,
                )
            case cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST:
                # 白名单激活状态切换成功，使用专门的处理方法
                TokenizationAllowlistManager.on_token_access_activation_success(
                    activity_id=str(self.activity.uuid),
                    transaction_request=self.tx_request,
                )
            case cobo_waas2.TokenizationOperationType.CONTRACTCALL:
                # 合约调用成功，使用专门的处理器
                TokenizationContractCallManager.on_contract_call_success(
                    activity_id=str(self.activity.uuid),
                    transaction_request=self.tx_request,
                )
            case _:
                pass

    def handle_tx_rbf_created(
        self, origin_request: TransactionRequest, rbf_request: TransactionRequest
    ):
        """处理 RBF 交易创建事件

        当原交易被RBF替换时，需要为新的RBF交易创建对应的ActivityTransaction记录，
        保持与原交易相同的activity_id和action，但使用新的request_id和transaction_id。

        Args:
            origin_request: 原始交易请求
            rbf_request: RBF替换交易请求
        """
        try:
            # 获取RBF交易对应的transaction记录
            tx_source = TransactionSourceDao.get_by_source(
                source=QueryTransactionSourceType.TransactionRequest.value,
                ref_id=str(rbf_request.id),
            )
            if not tx_source:
                logger.warning(
                    f"Transaction source not found for RBF request {rbf_request.id}"
                )
                return

            # 使用TokenizationActivityManager处理RBF交易
            TokenizationActivityManager.add_replace_transaction_to_activity(
                origin_tr=origin_request,
                rbf_tr=rbf_request,
                transaction_id=str(tx_source.uuid),
            )

        except Exception as e:
            logger.error(f"Error handling RBF transaction creation: {e}", exc_info=True)

    @updates_transaction_record
    def handle_tx_signed(self):
        pass

    @updates_transaction_record
    def handle_tx_broadcasted(self):
        pass

    @updates_transaction_record
    def handle_tx_broadcast_failed(self):
        pass

    @updates_transaction_record
    def handle_tx_reverting(self):
        pass

    @updates_transaction_record
    def handle_tx_success(self):
        pass
