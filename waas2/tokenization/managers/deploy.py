"""
代币部署管理器 - 重构为使用工厂模式
"""
import logging

# 导入新的工厂管理器
from waas2.tokenization.managers.deploy_factory import (
    TokenizationDeployManager as NewTokenizationDeployManager,
    DeployManagerFactory
)

logger = logging.getLogger("waas2.tokenization")


# 为了保持向后兼容性，将原有的类重定向到新的工厂管理器
class TokenizationDeployManager(NewTokenizationDeployManager):
    """
    代币部署管理器 - 兼容性包装器
    
    此类继承自新的工厂管理器，保持与现有代码的完全兼容性。
    所有方法都已在父类中实现，支持EVM和Solana链的自动路由。
    
    使用方法：
    - EVM链（ETH、BSC等）：自动路由到ERC20DeployManager
    - Solana链（SOL等）：自动路由到SolanaToken2022DeployManager
    
    示例：
        # 部署EVM代币
        activity_id = TokenizationDeployManager.deploy(evm_params, ...)
        
        # 部署Solana Token 2022
        activity_id = TokenizationDeployManager.deploy(solana_params, ...)
    """
    pass
