"""
代币部署管理器 - 简化版本，只处理calldata构建和地址预测
"""
import logging
from typing import Union, Dict, Any
import cobo_waas2

from waas2.tokenization.utils.chain_detector import ChainDetector, ChainType
from waas2.tokenization.managers.calldata.evm.cobo_erc20 import CoboERC20CalldataManager
from waas2.tokenization.managers.calldata.solana.token_2022 import SolanaToken2022CalldataManager

logger = logging.getLogger("waas2.tokenization")


class TokenizationDeployManager:
    """
    代币部署管理器 - 简化版本

    主要负责：
    1. build_deploy_calldata - 构建部署调用数据
    2. _predict_contract_address - 预测合约地址

    根据链类型自动路由到对应的calldata管理器：
    - EVM链：使用CoboERC20CalldataManager
    - Solana链：使用SolanaToken2022CalldataManager
    """

    @classmethod
    def build_deploy_calldata(
        cls,
        chain_id: str,
        execute_address: str,
        token_params: Union[cobo_waas2.TokenizationERC20TokenParams, Dict[str, Any]],
    ) -> str:
        """
        构造部署调用数据

        Args:
            chain_id: 链ID
            execute_address: 执行地址
            token_params: 代币参数

        Returns:
            调用数据
        """
        chain_type = ChainDetector.get_chain_type(chain_id)

        if chain_type == ChainType.EVM:
            # EVM链使用CoboERC20CalldataManager
            return CoboERC20CalldataManager.build_deploy_calldata(
                chain_id, execute_address, token_params
            )
        elif chain_type == ChainType.SOLANA:
            # Solana链使用SolanaToken2022CalldataManager
            # 如果token_params是cobo_waas2对象，需要转换为字典
            if hasattr(token_params, '__dict__'):
                params_dict = cls._convert_solana_params_to_dict(token_params)
            else:
                params_dict = token_params

            return SolanaToken2022CalldataManager.build_deploy_calldata(
                chain_id, execute_address, params_dict
            )
        else:
            raise ValueError(f"Unsupported chain type: {chain_type} for chain_id: {chain_id}")

    @classmethod
    def _predict_contract_address(
        cls,
        chain_id: str,
        from_address: str,
        calldata: str,
    ) -> str:
        """
        预测合约地址

        Args:
            chain_id: 链ID
            from_address: 发送地址
            calldata: 调用数据

        Returns:
            预测的合约地址
        """
        chain_type = ChainDetector.get_chain_type(chain_id)

        if chain_type == ChainType.EVM:
            # EVM链使用CoboERC20CalldataManager
            return CoboERC20CalldataManager.predict_contract_address(
                chain_id, from_address, calldata
            )
        elif chain_type == ChainType.SOLANA:
            # Solana链使用SolanaToken2022CalldataManager
            return SolanaToken2022CalldataManager.predict_contract_address(
                chain_id, from_address, calldata
            )
        else:
            raise ValueError(f"Unsupported chain type: {chain_type} for chain_id: {chain_id}")

    @classmethod
    def _convert_solana_params_to_dict(cls, token_params) -> Dict[str, Any]:
        """将Solana代币参数转换为字典格式"""
        # 这里需要根据实际的Solana参数结构进行转换
        # 目前先返回一个基础的转换
        return {
            "name": getattr(token_params, 'name', ''),
            "symbol": getattr(token_params, 'symbol', ''),
            "decimals": getattr(token_params, 'decimals', 9),
            "mint_authority": getattr(token_params, 'mint_authority', ''),
            "freeze_authority": getattr(token_params, 'freeze_authority', None),
            "close_authority": getattr(token_params, 'close_authority', None),
            "extensions": getattr(token_params, 'extensions', []),
            "uri": getattr(token_params, 'uri', ''),
        }

    # 为了保持向后兼容性，添加原有的方法
    @classmethod
    def deploy(
        cls,
        params: cobo_waas2.TokenizationIssuedTokenRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        部署代币 - 兼容性方法

        注意：这个方法只是为了保持测试兼容性，实际部署逻辑应该在控制器中处理
        """
        # 这里只是一个占位符实现，实际的部署逻辑在控制器中
        raise NotImplementedError("This method is for compatibility only. Use controllers for actual deployment.")

    @classmethod
    def _create_token_record(cls, params, org_id, contract_address, token_id):
        """创建代币记录 - 兼容性方法"""
        # 这里只是一个占位符实现
        raise NotImplementedError("This method is for compatibility only.")

    @classmethod
    def _encode_function_call(cls, function_signature: str, params: list) -> bytes:
        """编码函数调用 - 兼容性方法，委托给EVM calldata管理器"""
        return CoboERC20CalldataManager._encode_function_call(function_signature, params)

    @classmethod
    def on_deploy_on_chained(cls, activity_id: str):
        """部署成功回调 - 兼容性方法"""
        pass

    @classmethod
    def on_gas_sponsor_on_chained(cls, deploy_activity, gas_sponsor_transaction):
        """手续费垫付成功回调 - 兼容性方法"""
        pass

    @classmethod
    def on_deploy_success(cls, token):
        """部署成功回调 - 兼容性方法"""
        pass

    @classmethod
    def on_deploy_failed(cls, activity_id: str):
        """部署失败回调 - 兼容性方法"""
        pass

    @classmethod
    def _generate_token_id(cls, chain_id: str, symbol: str):
        """生成代币ID - 兼容性方法"""
        from waas2.tokenization.dao.tokenization import TokenDao

        symbol_count = TokenDao.count_by_chain_id_and_symbol_and_statuses(
            chain_id,
            symbol,
        )
        symbol_suffix = str(symbol_count) if symbol_count > 0 else ""
        chain_prefix = chain_id if "_" in chain_id else chain_id.split("_")[0]
        return f"{chain_prefix}_{symbol}_TKZ_{symbol_suffix}".upper()
