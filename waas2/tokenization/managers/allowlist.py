import logging
from typing import List

import cobo_waas2
from django.db import transaction
from web3 import Web3

from custody.web3.data.objects import MPCExtraParameters
from custody.web3.models.transaction_request import TransactionRequest
from waas2.tokenization.dao.tokenization import ActivityDao, TokenAllowlistDao, TokenDao
from waas2.tokenization.data.tokenization import TokenizationExtra
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.tokenization.utils.token_id import generate_request_id
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationAllowlistManager:
    @classmethod
    @transaction.atomic
    def update_allowlist(
        cls,
        token: Token,
        biz_org_id: int,
        org_id: str,
        params: cobo_waas2.TokenizationUpdateAllowlistAddressesRequest,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """更新代币白名单地址"""
        # # 检查是否有冲突的正在处理中的活动
        # cls._check_conflicting_activities(token.token_id, org_id)

        # 生成 request_id
        operation_type = cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES
        request_id = generate_request_id(token.symbol, operation_type)

        logger.info(f"Starting allowlist update operation for token {token.token_id}")

        addresses = [x.address for x in params.addresses]
        calldata = cls._build_allowlist_calldata(
            token.chain_id, token.token_address, addresses, params.action
        )
        # 执行合约调用
        tx = TokenizationUtils.execute_contract_call(
            biz_org_id=biz_org_id,
            org_id=org_id,
            tokenization_source=params.source,
            token=token,
            contract_address=token.token_address,
            calldata=calldata,
            fee=params.fee,
            request_id=request_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        # 创建活动记录，保存完整的地址信息（包含 note）
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            tx=tx,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        if params.action == cobo_waas2.TokenizationUpdateAddressAction.GRANT:
            for addr in params.addresses:
                TokenAllowlistDao.create_or_update_address(
                    token_id=token.token_id,
                    address=addr.address,
                    note=addr.note,
                )

        logger.info(
            f"Allowlist update operation completed successfully, activity_id: {activity.uuid}, request_id: {request_id}"
        )
        return str(activity.uuid)

    @classmethod
    def on_allowlist_success(
        cls, activity_id: str, transaction_request: TransactionRequest
    ):
        """白名单更新成功后的处理"""
        activity = ActivityDao.get_by_uuid(activity_id)
        if not activity:
            raise ValueError(f"Activity not found: {activity_id}")

        token = TokenDao.get_by_uuid(activity.token_uuid)
        if not token:
            raise ValueError(f"Token not found for activity: {activity_id}")

        extra_args = MPCExtraParameters.from_json(transaction_request.extra_args)
        calldata = extra_args.calldata

        contract = CoboERC20ContractHelper.get_contract(
            token.chain_id, token.token_address
        )
        try:
            func_obj, func_params = contract.decode_function_input(calldata)
            addresses = func_params.get("accounts", [])
        except ValueError:
            logger.error(f"Failed to decode calldata for activity {activity_id}")
            return

        operation_name = func_obj.fn_name

        if operation_name == "accessListAdd":
            valid = True
        elif operation_name == "accessListRemove":
            valid = False
        else:
            logger.warning(
                f"Unknown operation '{operation_name}' for activity {activity_id}"
            )
            return

        for address in addresses:
            TokenAllowlistDao.create_or_update_address(
                token_id=activity.token_id,
                address=address,
                valid=valid,
            )

        logger.info(f"Allowlist update operation succeeded for activity {activity_id}")

    @classmethod
    def on_allowlist_failed(cls, activity_id: str):
        pass

    @classmethod
    def on_token_access_activation_success(
        cls, activity_id: str, transaction_request: TransactionRequest
    ):
        """白名单激活状态更新成功后的处理"""
        activity = ActivityDao.get_by_uuid(activity_id)
        if not activity:
            raise ValueError(f"Activity not found: {activity_id}")

        token = TokenDao.get_by_uuid(activity.token_uuid)
        if not token:
            raise ValueError(f"Token not found for activity: {activity_id}")

        extra_args = MPCExtraParameters.from_json(transaction_request.extra_args)
        calldata = extra_args.calldata

        contract = CoboERC20ContractHelper.get_contract(
            token.chain_id, token.token_address
        )
        try:
            func_obj, func_params = contract.decode_function_input(calldata)
            activation = func_params.get("enabled", None)
        except ValueError:
            logger.error(
                f"Failed to decode calldata for allowlist activation activity {activity_id}"
            )
            return

        if activation is None:
            logger.error(
                f"Could not find 'enabled' parameter in calldata for activity {activity_id}"
            )
            return

        # 更新数据库中的白名单激活状态
        extra = TokenizationExtra.model_validate(token.extra)
        extra.token_access_activated = activation
        TokenDao.update_by_id(token.id, extra=extra.model_dump(mode="json"))
        logger.info(
            f"Token {activity.token_id} token_access_activated updated to {extra.token_access_activated}"
        )

        logger.info(
            f"Allowlist activation operation succeeded for activity {activity_id}"
        )

    @classmethod
    def _build_allowlist_calldata(
        cls, chain_id: str, token_address: str, addresses: List[str], operation: str
    ) -> str:
        """构造白名单更新的合约调用数据"""
        contract = CoboERC20ContractHelper.get_contract(chain_id, token_address)
        checksum_addresses = [Web3.to_checksum_address(addr) for addr in addresses]

        if operation == cobo_waas2.TokenizationUpdateAddressAction.GRANT:
            return contract.functions.accessListAdd(
                checksum_addresses
            )._encode_transaction_data()
        else:  # remove
            return contract.functions.accessListRemove(
                checksum_addresses
            )._encode_transaction_data()

    @classmethod
    def get_token_access_activation(cls, token: Token) -> bool:
        extra = TokenizationExtra.model_validate(token.extra)
        return extra.token_access_activated

    @classmethod
    @transaction.atomic
    def update_token_access_activation(
        cls,
        token: Token,
        params: cobo_waas2.TokenizationAllowlistActivationRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        构造和发起代币限制更新交易
        """
        # # 检查是否有冲突的正在处理中的活动
        # cls._check_conflicting_activities(token.token_id, org_id)

        # 生成 request_id
        operation_type = cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST
        request_id = generate_request_id(token.symbol, operation_type)

        logger.info(f"Starting restriction update operation for token {token.token_id}")

        calldata = cls._build_toggle_allowlist_calldata(
            token.chain_id, token.token_address, params.activation
        )

        tx = TokenizationUtils.execute_contract_call(
            biz_org_id=biz_org_id,
            org_id=org_id,
            tokenization_source=params.source,
            token=token,
            calldata=calldata,
            fee=params.fee,
            request_id=request_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        # 创建活动记录
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            tx=tx,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        logger.info(
            f"Restriction update operation completed successfully, activity_uuid: {activity.uuid}, request_id: {tx.request_id}"
        )
        return str(activity.uuid)

    @classmethod
    def _build_toggle_allowlist_calldata(
        cls, chain_id: str, token_address: str, enabled: bool
    ) -> str:
        contract = CoboERC20ContractHelper.get_contract(chain_id, token_address)
        return contract.functions.toggleAccesslist(enabled)._encode_transaction_data()

    @classmethod
    def _check_conflicting_activities(cls, token_id: str, org_id: str):
        """检查是否有冲突的正在处理中的白名单相关活动"""
        from waas2.developers.exceptions import InvalidParamException

        conflicting_operations = [
            cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES.value,
            cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST.value,
            cobo_waas2.TokenizationOperationType.CONTRACTCALL.value,
        ]

        processing_activities = ActivityDao.query_activities(
            org_id=org_id,
            token_id=token_id,
            activity_status=cobo_waas2.TokenizationActivityStatus.PROCESSING.value,
        ).filter(type__in=conflicting_operations)

        if processing_activities.exists():
            conflicting_activity = processing_activities.first()
            raise InvalidParamException(
                f"Cannot execute allowlist update operation: "
                f"There is already a processing {conflicting_activity.type} activity "
                f"(ID: {conflicting_activity.uuid}) for this token. "
                f"Please wait for the previous operation to complete."
            )
