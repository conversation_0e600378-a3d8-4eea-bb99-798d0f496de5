import logging

import cobo_waas2
from django.db import transaction
from web3 import Web3

from custody.web3.data.objects import MPCExtraParameters
from custody.web3.models.transaction_request import TransactionRequest
from waas2.developers.exceptions import InvalidParamException
from waas2.tokenization.dao.tokenization import ActivityDao, TokenDao
from waas2.tokenization.models.tokenization import Token
from waas2.tokenization.utils.contract import CoboERC20ContractHelper
from waas2.tokenization.utils.selector import CoboERC20SelectorUtils
from waas2.tokenization.utils.token_id import generate_request_id
from waas2.tokenization.utils.tokenization import TokenizationUtils

logger = logging.getLogger("waas2.tokenization")


class TokenizationContractCallManager:
    """代币化合约调用管理器"""

    @classmethod
    @transaction.atomic
    def contract_call(
        cls,
        token: Token,
        params: cobo_waas2.TokenizationContractCallRequest,
        org_id: str,
        biz_org_id: int,
        api_request_info: dict,
        sign_info: dict,
    ) -> str:
        """
        执行代币合约调用
        """
        # 生成 request_id
        operation_type = cobo_waas2.TokenizationOperationType.CONTRACTCALL
        request_id = generate_request_id(token.symbol, operation_type)

        logger.info(f"Starting contract call operation for token {token.token_id}")

        # # 检查是否有冲突的正在处理中的活动
        # cls._check_conflicting_activities(
        #     token.token_id, params.data.actual_instance.calldata, org_id
        # )

        # 执行合约调用
        tx = TokenizationUtils.execute_contract_call(
            biz_org_id=biz_org_id,
            org_id=org_id,
            tokenization_source=params.source,
            token=token,
            calldata=params.data.actual_instance.calldata,
            fee=params.fee,
            request_id=request_id,
            api_request_info=api_request_info,
            sign_info=sign_info,
        )

        # 交易成功后创建活动记录
        activity = TokenizationUtils.create_activity(
            token=token,
            operation_type=operation_type,
            params=params,
            org_id=org_id,
            tx=tx,
            sign_info=sign_info,
            api_request_info=api_request_info,
        )

        logger.info(
            f"Contract call operation completed successfully, activity_uuid: {activity.uuid}, request_id: {tx.request_id}"
        )
        return str(activity.uuid)

    @classmethod
    def _check_conflicting_activities(cls, token_id: str, calldata: str, org_id: str):
        """检查是否有冲突的正在处理中的活动"""
        try:
            # 解析 calldata 获取方法选择器
            if len(calldata) < 10:  # 0x + 8位选择器
                return

            selector = calldata[:10]
            function_name = CoboERC20SelectorUtils.get_name_by_selector(selector)

            if not function_name:
                return

            # 特殊处理 multicall
            if selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"]:
                conflicting_operations = cls._get_multicall_conflicting_operations(
                    calldata
                )
            else:
                # 定义需要检查冲突的操作类型映射
                conflicting_operations = cls._get_conflicting_operations(
                    selector, function_name
                )

            if not conflicting_operations:
                return

            # 查询正在处理中的相关活动
            processing_activities = ActivityDao.query_activities(
                org_id=org_id,
                token_id=token_id,
                activity_status=cobo_waas2.TokenizationActivityStatus.PROCESSING.value,
            ).filter(type__in=conflicting_operations)

            if processing_activities.exists():
                conflicting_activity = processing_activities.first()

                # 为 multicall 提供更详细的错误信息
                if selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"]:
                    nested_selectors = cls._parse_multicall_nested_operations(calldata)
                    nested_functions = [
                        CoboERC20SelectorUtils.get_name_by_selector(s)
                        for s in nested_selectors
                        if CoboERC20SelectorUtils.get_name_by_selector(s)
                    ]
                    nested_info = (
                        f" (contains: {', '.join(nested_functions)})"
                        if nested_functions
                        else ""
                    )

                    raise InvalidParamException(
                        f"Cannot execute multicall operation{nested_info}: "
                        f"There is already a processing {conflicting_activity.type} activity "
                        f"(ID: {conflicting_activity.uuid}) for this token. "
                        f"Please wait for the previous operation to complete."
                    )
                else:
                    raise InvalidParamException(
                        f"Cannot execute {function_name} operation: "
                        f"There is already a processing {conflicting_activity.type} activity "
                        f"(ID: {conflicting_activity.uuid}) for this token. "
                        f"Please wait for the previous operation to complete."
                    )

        except Exception as e:
            if isinstance(e, InvalidParamException):
                raise
            logger.warning(f"Failed to check conflicting activities: {e}")

    @classmethod
    def _get_conflicting_operations(cls, selector: str, function_name: str) -> list:
        """获取与当前操作冲突的活动类型列表"""
        # 白名单相关操作
        allowlist_selectors = {
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["accessListAdd"],
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["accessListRemove"],
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["toggleAccesslist"],
        }

        # 黑名单相关操作
        blocklist_selectors = {
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["blockListAdd"],
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["blockListRemove"],
        }

        # 权限相关操作
        role_selectors = {
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["grantRole"],
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["revokeRole"],
        }

        # 暂停相关操作
        pause_selectors = {
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["pause"],
            CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["unpause"],
        }

        # 根据选择器确定冲突的操作类型
        if selector in allowlist_selectors:
            return [
                cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES.value,
                cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST.value,
                cobo_waas2.TokenizationOperationType.CONTRACTCALL.value,
            ]
        elif selector in blocklist_selectors:
            return [
                cobo_waas2.TokenizationOperationType.UPDATEBLOCKLISTADDRESSES.value,
                cobo_waas2.TokenizationOperationType.CONTRACTCALL.value,
            ]
        elif selector in role_selectors:
            return [
                cobo_waas2.TokenizationOperationType.CONTRACTCALL.value,
            ]
        elif selector in pause_selectors:
            return [
                cobo_waas2.TokenizationOperationType.PAUSE.value,
                cobo_waas2.TokenizationOperationType.UNPAUSE.value,
                cobo_waas2.TokenizationOperationType.CONTRACTCALL.value,
            ]

        return []

    @classmethod
    def _parse_multicall_nested_operations(cls, calldata: str) -> set:
        """解析 multicall 中的嵌套操作，返回所有嵌套调用的选择器集合"""
        try:
            # 解析 multicall 的 calldata
            if len(calldata) < 10:
                return set()

            # 使用 Web3 的 ABI 解码功能
            from web3 import Web3

            # multicall 的函数签名: multicall(bytes[] calldata data)
            multicall_abi = [
                {
                    "name": "multicall",
                    "type": "function",
                    "inputs": [{"name": "data", "type": "bytes[]"}],
                }
            ]

            # 创建一个临时合约实例用于解码
            w3 = Web3()
            contract = w3.eth.contract(abi=multicall_abi)

            # 解码 calldata
            decoded = contract.decode_function_input(calldata)
            function_obj, function_params = decoded
            data_list = function_params.get("data", [])

            nested_selectors = set()
            for calldata_bytes in data_list:
                try:
                    # 将 bytes 转换为 hex string
                    if isinstance(calldata_bytes, bytes):
                        nested_calldata = "0x" + calldata_bytes.hex()
                    else:
                        nested_calldata = calldata_bytes

                    # 提取嵌套调用的选择器（前4字节）
                    if len(nested_calldata) >= 10:  # 0x + 8位hex = 10字符
                        nested_selector = nested_calldata[:10]
                        nested_selectors.add(nested_selector)

                except Exception as e:
                    logger.warning(f"Failed to parse nested calldata in multicall: {e}")
                    continue

            return nested_selectors

        except Exception as e:
            logger.warning(f"Failed to parse multicall calldata: {e}")
            return set()

    @classmethod
    def _get_multicall_conflicting_operations(cls, calldata: str) -> list:
        """获取 multicall 中所有嵌套操作的冲突活动类型"""
        nested_selectors = cls._parse_multicall_nested_operations(calldata)

        if not nested_selectors:
            # 如果无法解析，为了安全起见，与所有操作冲突
            logger.warning(
                "Cannot parse multicall nested operations, defaulting to conflict with all operations"
            )
            return [
                cobo_waas2.TokenizationOperationType.UPDATEALLOWLISTADDRESSES.value,
                cobo_waas2.TokenizationOperationType.TOGGLEALLOWLIST.value,
                cobo_waas2.TokenizationOperationType.UPDATEBLOCKLISTADDRESSES.value,
                cobo_waas2.TokenizationOperationType.PAUSE.value,
                cobo_waas2.TokenizationOperationType.UNPAUSE.value,
                cobo_waas2.TokenizationOperationType.CONTRACTCALL.value,
            ]

        # 收集所有嵌套操作的冲突类型
        all_conflicting_operations = set()
        all_conflicting_operations.add(
            cobo_waas2.TokenizationOperationType.CONTRACTCALL.value
        )  # 总是与其他 contract call 冲突

        for nested_selector in nested_selectors:
            nested_conflicts = cls._get_conflicting_operations(nested_selector, "")
            all_conflicting_operations.update(nested_conflicts)

        return list(all_conflicting_operations)

    @classmethod
    @transaction.atomic
    def on_contract_call_success(
        cls, activity_id: str, transaction_request: TransactionRequest
    ):
        """合约调用成功后的处理"""
        activity = ActivityDao.get_by_uuid(activity_id)
        if not activity:
            raise ValueError(f"Activity not found: {activity_id}")

        token = TokenDao.get_by_uuid(activity.token_uuid)
        if not token:
            raise ValueError(f"Token not found for activity: {activity_id}")

        extra_args = MPCExtraParameters.from_json(transaction_request.extra_args)
        calldata = extra_args.calldata

        try:
            cls._sync_contract_call_state(token, calldata)
            logger.info(f"Contract call state synced for activity {activity_id}")
        except Exception as e:
            logger.error(
                f"Failed to sync contract call state for activity {activity_id}: {e}"
            )

    @classmethod
    @transaction.atomic
    def on_contract_call_failed(cls, activity_id: str):
        """合约调用失败后的处理"""
        logger.info(f"Contract call failed for activity {activity_id}")

    @classmethod
    def _sync_contract_call_state(cls, token: Token, calldata: str):
        """同步 contract call 的状态变更"""
        if not calldata or not calldata.startswith("0x") or len(calldata) < 10:
            return

        # 提取 selector
        selector = calldata[2:10].lower()
        function_name = CoboERC20SelectorUtils.get_name_by_selector(selector)

        logger.info(
            f"Syncing contract call state for function: {function_name} (selector: {selector})"
        )

        try:
            # 获取 token 信息
            token = TokenDao.get_by_token_id(token.token_id)
            if not token:
                logger.error(f"Token not found: {token.token_id}")
                return

            # 根据不同的函数类型同步状态
            if selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["pause"]:
                cls._sync_pause_state(token.token_id)
            elif selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["unpause"]:
                cls._sync_unpause_state(token.token_id)
            elif (
                selector
                == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["toggleAccesslist"]
            ):
                cls._sync_token_access_activation_from_calldata(token, calldata)
            elif selector in [
                CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["accessListAdd"],
                CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["accessListRemove"],
            ]:
                cls._sync_allowlist_addresses_from_calldata(
                    token.token_id, calldata, selector
                )
            elif selector in [
                CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["blockListAdd"],
                CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["blockListRemove"],
            ]:
                cls._sync_blocklist_addresses_from_calldata(
                    token.token_id, calldata, selector
                )
            elif selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["grantRole"]:
                cls._sync_role_grant_from_calldata(token.token_id, calldata)
            elif selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["revokeRole"]:
                cls._sync_role_revoke_from_calldata(token.token_id, calldata)
            elif (
                selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["renounceRole"]
            ):
                cls._sync_role_revoke_from_calldata(token.token_id, calldata)
            elif selector == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["multicall"]:
                cls._sync_multicall_state(token, calldata)
            else:
                logger.debug(f"No state sync needed for function: {function_name}")

        except Exception as e:
            logger.error(
                f"Failed to sync contract call state for selector {selector}: {e}"
            )

    @classmethod
    def _sync_pause_state(cls, token_id: str):
        """同步暂停状态"""
        token = TokenDao.get_by_token_id(token_id)
        if not token:
            logger.error(f"Token not found: {token_id}")
            return

        if token.status == cobo_waas2.TokenizationStatus.ACTIVE:
            TokenDao.update_by_id(
                token.id, status=cobo_waas2.TokenizationStatus.PAUSING
            )
            logger.info(f"Token {token_id} status updated to PAUSED")

    @classmethod
    def _sync_unpause_state(cls, token_id: str):
        """同步恢复状态"""
        token = TokenDao.get_by_token_id(token_id)
        if not token:
            logger.error(f"Token not found: {token_id}")
            return

        if token.status == cobo_waas2.TokenizationStatus.PAUSING:
            TokenDao.update_by_id(token.id, status=cobo_waas2.TokenizationStatus.ACTIVE)
            logger.info(f"Token {token_id} status updated to ACTIVE")

    @classmethod
    def _sync_token_access_activation_from_calldata(cls, token: Token, calldata: str):
        """从 calldata 同步白名单激活状态"""
        try:
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )
            decoded = contract.decode_function_input(calldata)
            function_params = decoded[1]
            enabled = function_params.get("enabled", False)

            # 复用现有的同步逻辑
            from waas2.tokenization.data.tokenization import TokenizationExtra

            extra = TokenizationExtra.model_validate(token.extra)
            if extra.token_access_activated != enabled:
                extra.token_access_activated = enabled
                TokenDao.update_by_id(token.id, extra=extra.model_dump(mode="json"))
                logger.info(
                    f"Token {token.token_id} token_access_activated updated to {enabled}"
                )

        except Exception as e:
            logger.error(f"Failed to sync allowlist activation: {e}")

    @classmethod
    def _sync_allowlist_addresses_from_calldata(
        cls, token_id: str, calldata: str, selector: str
    ):
        """从 calldata 同步白名单地址"""
        try:
            token = TokenDao.get_by_token_id(token_id)
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )
            decoded = contract.decode_function_input(calldata)
            function_params = decoded[1]
            addresses = function_params.get("accounts", [])

            operation = (
                cobo_waas2.TokenizationUpdateAddressAction.GRANT
                if selector
                == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["accessListAdd"]
                else cobo_waas2.TokenizationUpdateAddressAction.REVOKE
            )

            # 使用新的 DAO 方法
            from waas2.tokenization.dao.tokenization import TokenAllowlistDao

            for address in addresses:
                address = Web3.to_checksum_address(address)

                if operation == cobo_waas2.TokenizationUpdateAddressAction.GRANT:
                    TokenAllowlistDao.create_or_update_address(
                        token_id=token_id,
                        address=address,
                        note="",  # 从合约调用无法获取 note，使用空字符串
                        valid=True,
                    )
                    logger.info(
                        f"Added address {address} to allowlist for token {token_id}"
                    )

                elif operation == cobo_waas2.TokenizationUpdateAddressAction.REVOKE:
                    TokenAllowlistDao.create_or_update_address(
                        token_id=token_id,
                        address=address,
                        note="",  # 从合约调用无法获取 note，使用空字符串
                        valid=False,
                    )
                    logger.info(
                        f"Removed address {address} from allowlist for token {token_id}"
                    )

        except Exception as e:
            logger.error(f"Failed to sync allowlist addresses: {e}")

    @classmethod
    def _sync_blocklist_addresses_from_calldata(
        cls, token_id: str, calldata: str, selector: str
    ):
        """从 calldata 同步黑名单地址"""
        try:
            token = TokenDao.get_by_token_id(token_id)
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )
            decoded = contract.decode_function_input(calldata)
            function_params = decoded[1]
            addresses = function_params.get("accounts", [])

            operation = (
                cobo_waas2.TokenizationUpdateAddressAction.GRANT
                if selector
                == CoboERC20SelectorUtils.COBO_ERC20_SELECTORS["blockListAdd"]
                else cobo_waas2.TokenizationUpdateAddressAction.REVOKE
            )

            # 使用新的 DAO 方法
            from waas2.tokenization.dao.tokenization import TokenBlocklistDao

            for address in addresses:
                address = Web3.to_checksum_address(address)

                if operation == cobo_waas2.TokenizationUpdateAddressAction.GRANT:
                    TokenBlocklistDao.create_or_update_address(
                        token_id=token_id,
                        address=address,
                        note="",  # 从合约调用无法获取 note，使用空字符串
                        valid=True,
                    )
                    logger.info(
                        f"Added address {address} to blocklist for token {token_id}"
                    )

                elif operation == cobo_waas2.TokenizationUpdateAddressAction.REVOKE:
                    TokenBlocklistDao.create_or_update_address(
                        token_id=token_id,
                        address=address,
                        note="",  # 从合约调用无法获取 note，使用空字符串
                        valid=False,
                    )
                    logger.info(
                        f"Removed address {address} from blocklist for token {token_id}"
                    )

        except Exception as e:
            logger.error(f"Failed to sync blocklist addresses: {e}")

    @classmethod
    def _sync_role_grant_from_calldata(cls, token_id: str, calldata: str):
        """从 calldata 同步角色授予"""
        try:
            token = TokenDao.get_by_token_id(token_id)
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )
            decoded = contract.decode_function_input(calldata)
            function_params = decoded[1]

            role_hash = function_params.get("role")
            account = function_params.get("account")

            if isinstance(role_hash, bytes):
                role_hash = "0x" + role_hash.hex()

            if not role_hash or not account:
                return

            # 复用现有的角色同步逻辑
            from waas2.tokenization.dao.tokenization import TokenRoleDao

            role = cls._hash_to_role(role_hash)
            if role:
                account = Web3.to_checksum_address(account)
                existing = TokenRoleDao.get_by_token_and_address(token_id, account)
                if existing:
                    new_roles = existing.roles | role.value
                    TokenRoleDao.update_by_id(existing.id, roles=new_roles)
                else:
                    TokenRoleDao.create(
                        token_id=token_id, address=account, roles=role.value
                    )
                logger.info(
                    f"Granted role {role.name} to address {account} for token {token_id}"
                )

        except Exception as e:
            logger.error(f"Failed to sync role grant: {e}")

    @classmethod
    def _sync_role_revoke_from_calldata(cls, token_id: str, calldata: str):
        """从 calldata 同步角色撤销"""
        try:
            token = TokenDao.get_by_token_id(token_id)
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )
            decoded = contract.decode_function_input(calldata)
            function_params = decoded[1]

            role_hash = function_params.get("role")
            account = function_params.get("account")

            if isinstance(role_hash, bytes):
                role_hash = "0x" + role_hash.hex()

            if not role_hash or not account:
                return

            # 复用现有的角色同步逻辑
            from waas2.tokenization.dao.tokenization import TokenRoleDao

            role = cls._hash_to_role(role_hash)
            if role:
                account = Web3.to_checksum_address(account)

                existing = TokenRoleDao.get_by_token_and_address(token_id, account)
                if existing:
                    new_roles = existing.roles & ~role.value
                    if new_roles == 0:
                        TokenRoleDao.update_by_id(existing.id, roles=0)
                    else:
                        TokenRoleDao.update_by_id(existing.id, roles=new_roles)
                    logger.info(
                        f"Revoked role {role.name} from address {account} for token {token_id}"
                    )

        except Exception as e:
            logger.error(f"Failed to sync role revoke: {e}")

    @classmethod
    def _sync_multicall_state(cls, token: Token, calldata: str):
        """同步 multicall 中的嵌套调用状态"""
        try:
            contract = CoboERC20ContractHelper.get_contract(
                token.chain_id, token.token_address
            )
            decoded = contract.decode_function_input(calldata)
            function_params = decoded[1]
            data_list = function_params.get("data", [])

            for calldata_bytes in data_list:
                try:
                    # 将 bytes 转换为 hex string
                    if isinstance(calldata_bytes, bytes):
                        nested_calldata = "0x" + calldata_bytes.hex()
                    else:
                        nested_calldata = calldata_bytes

                    # 递归处理嵌套调用
                    cls._sync_contract_call_state(token, nested_calldata)

                except Exception as e:
                    logger.error(f"Failed to sync nested calldata in multicall: {e}")

        except Exception as e:
            logger.error(f"Failed to sync multicall state: {e}")

    @classmethod
    def _hash_to_role(cls, role_hash: str):
        """将角色哈希转换为角色枚举"""
        from waas2.tokenization.enums.tokenization import TokenizationRole

        role_mapping = {
            "0x9f2df0fed2c77648de5860a4cc508cd0818c85b8b8a1ab4ceeef8d981c8956a6": TokenizationRole.MINTER,
            "0x3c11d16cbaffd01df69ce1c404f6340ee057498f5f00246190ea54220576a848": TokenizationRole.BURNER,
            "0x65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a": TokenizationRole.PAUSER,
            "0x241ecf16d79d0f8dbfb92cbc07fe17840425976cf0667f022fe9877caa831b08": TokenizationRole.MANAGER,
            "0x9e16e6c596d2cf298b0b08786bf3a653eb1a9f723a5a76b814e3fbaa4f944609": TokenizationRole.SALVAGER,
            "0x189ab7a9244df0848122154315af71fe140f3db0fe014031783b0946b8c9d2e3": TokenizationRole.UPGRADER,
            "0x0000000000000000000000000000000000000000000000000000000000000000": TokenizationRole.ADMIN,
        }

        return role_mapping.get(role_hash.lower())
