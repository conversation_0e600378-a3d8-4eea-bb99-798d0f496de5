"""
Solana Token 2022 Calldata 管理器 - 使用 anchorpy + IDL
"""
import logging
import json
from typing import Dict, Any, Optional

logger = logging.getLogger("waas2.tokenization.solana.calldata")

# 尝试导入Solana相关库，如果失败则使用降级模式
try:
    from solders.pubkey import Pubkey
    from solders.keypair import Keypair
    from solders.system_program import ID as SYSTEM_PROGRAM_ID
    SOLANA_LIBS_AVAILABLE = True
except ImportError:
    logger.warning("solders library not available, using fallback mode")
    SOLANA_LIBS_AVAILABLE = False
    Pubkey = None
    Keypair = None
    SYSTEM_PROGRAM_ID = "********************************"

try:
    from anchorpy import Program, Provider, Wallet
    ANCHORPY_AVAILABLE = True
except ImportError:
    logger.warning("anchorpy library not available, using fallback mode")
    ANCHORPY_AVAILABLE = False
    Program = None
    Provider = None
    Wallet = None


class SolanaToken2022CalldataManager:
    """Solana Token 2022 部署Calldata管理器 - 使用 anchorpy + IDL"""

    # Token 2022 Program ID
    TOKEN_2022_PROGRAM_ID = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"

    # Token 2022 Program IDL (简化版本，实际应该从官方获取完整IDL)
    TOKEN_2022_IDL = {
        "version": "0.1.0",
        "name": "spl_token_2022",
        "instructions": [
            {
                "name": "initializeMint",
                "accounts": [
                    {"name": "mint", "isMut": True, "isSigner": False},
                    {"name": "rent", "isMut": False, "isSigner": False}
                ],
                "args": [
                    {"name": "decimals", "type": "u8"},
                    {"name": "mintAuthority", "type": "publicKey"},
                    {"name": "freezeAuthority", "type": {"option": "publicKey"}}
                ]
            },
            {
                "name": "initializeTransferFeeConfig",
                "accounts": [
                    {"name": "mint", "isMut": True, "isSigner": False}
                ],
                "args": [
                    {"name": "transferFeeConfigAuthority", "type": {"option": "publicKey"}},
                    {"name": "withdrawWithheldAuthority", "type": {"option": "publicKey"}},
                    {"name": "transferFeeBasisPoints", "type": "u16"},
                    {"name": "maximumFee", "type": "u64"}
                ]
            },
            {
                "name": "initializeDefaultAccountState",
                "accounts": [
                    {"name": "mint", "isMut": True, "isSigner": False}
                ],
                "args": [
                    {"name": "state", "type": "u8"}
                ]
            },
            {
                "name": "initializeImmutableOwner",
                "accounts": [
                    {"name": "account", "isMut": True, "isSigner": False}
                ],
                "args": []
            }
        ],
        "accounts": [
            {
                "name": "Mint",
                "type": {
                    "kind": "struct",
                    "fields": [
                        {"name": "mintAuthority", "type": {"option": "publicKey"}},
                        {"name": "supply", "type": "u64"},
                        {"name": "decimals", "type": "u8"},
                        {"name": "isInitialized", "type": "bool"},
                        {"name": "freezeAuthority", "type": {"option": "publicKey"}}
                    ]
                }
            }
        ]
    }

    @classmethod
    def build_deploy_calldata(
        cls,
        chain_id: str,
        execute_address: str,
        token_params: Dict[str, Any],
    ) -> str:
        """
        使用 anchorpy + IDL 构造Solana Token 2022部署指令数据

        Args:
            chain_id: 链ID (如 "SOL", "SOL_DEVNET")
            execute_address: 执行地址 (payer)
            token_params: 代币参数

        Returns:
            序列化的指令数据 (JSON格式)
        """
        logger.info(f"Building Solana Token 2022 deploy instructions using anchorpy for {token_params.get('symbol', 'UNKNOWN')}")

        try:
            # 检查依赖库是否可用
            if not ANCHORPY_AVAILABLE or not SOLANA_LIBS_AVAILABLE:
                logger.info("Anchorpy or solders not available, using fallback mode")
                return cls._build_fallback_instructions(chain_id, execute_address, token_params)

            # 生成mint密钥对
            mint_keypair = cls._generate_mint_keypair(token_params)
            mint_pubkey = Pubkey.from_string(mint_keypair["public_key"])
            payer_pubkey = Pubkey.from_string(execute_address)

            # 创建Provider和Program实例
            cluster = cls._get_cluster_from_chain_id(chain_id)
            provider = cls._create_provider(cluster, execute_address)

            if provider is None:
                logger.info("Provider creation failed, using fallback mode")
                return cls._build_fallback_instructions(chain_id, execute_address, token_params)

            program = Program(cls.TOKEN_2022_IDL, cls.TOKEN_2022_PROGRAM_ID, provider)

            instructions_data = []

            # 1. 创建mint账户指令 (使用System Program)
            create_account_ix = cls._build_create_account_instruction(
                payer_pubkey, mint_pubkey, token_params
            )
            instructions_data.append(create_account_ix)

            # 2. 初始化扩展指令（必须在InitializeMint之前）
            for extension in token_params.get("extensions", []):
                ext_ix = cls._build_extension_instruction_with_anchor(
                    program, mint_pubkey, extension
                )
                if ext_ix:
                    instructions_data.append(ext_ix)

            # 3. 初始化mint指令 (使用anchorpy)
            mint_authority = Pubkey.from_string(token_params.get("mint_authority"))
            freeze_authority = None
            if token_params.get("freeze_authority"):
                freeze_authority = Pubkey.from_string(token_params.get("freeze_authority"))

            initialize_mint_ix = program.instruction["initialize_mint"](
                decimals=token_params.get("decimals", 9),
                mint_authority=mint_authority,
                freeze_authority=freeze_authority,
                ctx=program.ctx(
                    accounts={
                        "mint": mint_pubkey,
                        "rent": Pubkey.from_string("SysvarRent111111111111111111111111111111111")
                    }
                )
            )

            instructions_data.append({
                "type": "initialize_mint",
                "program_id": str(program.program_id),
                "accounts": {
                    "mint": str(mint_pubkey),
                    "rent": "SysvarRent111111111111111111111111111111111"
                },
                "data": {
                    "decimals": token_params.get("decimals", 9),
                    "mint_authority": str(mint_authority),
                    "freeze_authority": str(freeze_authority) if freeze_authority else None,
                },
                "instruction_data": initialize_mint_ix.data.hex() if hasattr(initialize_mint_ix, 'data') else None
            })

            # 4. 设置元数据（如果支持）
            if token_params.get("name") or token_params.get("symbol"):
                metadata_ix = cls._build_metadata_instruction(mint_pubkey, token_params)
                if metadata_ix:
                    instructions_data.append(metadata_ix)

            # 构造最终的指令数据结构
            result = {
                "type": "solana_token_2022_deploy_anchorpy",
                "chain_id": chain_id,
                "cluster": cluster,
                "payer": execute_address,
                "mint_address": str(mint_pubkey),
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "token_params": {
                    "name": token_params.get("name", ""),
                    "symbol": token_params.get("symbol", ""),
                    "decimals": token_params.get("decimals", 9),
                    "mint_authority": token_params.get("mint_authority"),
                    "freeze_authority": token_params.get("freeze_authority"),
                    "close_authority": token_params.get("close_authority"),
                },
                "extensions": token_params.get("extensions", []),
                "instructions": instructions_data,
                "idl_version": cls.TOKEN_2022_IDL["version"]
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            logger.error(f"Failed to build Solana Token 2022 instructions with anchorpy: {e}")
            # 降级到简单模式
            return cls._build_fallback_instructions(chain_id, execute_address, token_params)

    @classmethod
    def predict_contract_address(
        cls,
        chain_id: str,
        from_address: str,
        calldata: str,
    ) -> str:
        """
        生成/预测Solana mint地址

        Args:
            chain_id: 链ID
            from_address: 发送地址
            calldata: 指令数据

        Returns:
            mint地址
        """
        try:
            # 解析calldata获取mint地址
            instructions_data = json.loads(calldata)
            mint_address = instructions_data.get("mint_address")

            if mint_address:
                logger.info(f"Using predefined mint address: {mint_address}")
                return mint_address

        except (json.JSONDecodeError, KeyError) as e:
            logger.warning(f"Failed to parse calldata for mint address: {e}")

        # 如果无法从calldata获取，生成一个确定性地址
        return cls._generate_deterministic_mint_address(chain_id, from_address, calldata)

    @classmethod
    def _get_cluster_from_chain_id(cls, chain_id: str) -> str:
        """根据chain_id获取Solana集群"""
        if chain_id == "SOL":
            return "mainnet-beta"
        elif chain_id == "SOL_DEVNET":
            return "devnet"
        elif chain_id == "SOL_TESTNET":
            return "testnet"
        else:
            return "devnet"  # 默认使用devnet

    @classmethod
    def _create_provider(cls, cluster: str, payer_address: str):
        """创建anchorpy Provider"""
        if not ANCHORPY_AVAILABLE or not SOLANA_LIBS_AVAILABLE:
            logger.warning("Anchorpy or solders not available, returning None")
            return None

        try:
            # 创建一个虚拟的钱包（实际使用中应该使用真实的钱包）
            dummy_keypair = Keypair()
            wallet = Wallet(dummy_keypair)

            # 简化的Provider创建（不使用Cluster枚举）
            # 实际实现中应该根据cluster字符串创建正确的连接
            provider = Provider.local(wallet)  # 使用本地连接作为示例

            return provider
        except Exception as e:
            logger.warning(f"Failed to create provider: {e}, using mock provider")
            return None

    @classmethod
    def _generate_mint_keypair(cls, token_params: Dict[str, Any]) -> Dict[str, str]:
        """生成mint密钥对"""
        try:
            # 使用Solana的Keypair生成真实的密钥对
            keypair = Keypair()
            return {
                "public_key": str(keypair.pubkey()),
                "private_key": str(keypair)  # 实际使用中应该安全存储
            }
        except Exception as e:
            logger.warning(f"Failed to generate real keypair: {e}, using deterministic fallback")
            # 降级到确定性生成
            import hashlib
            import base58

            seed_data = f"{token_params.get('symbol', '')}_{token_params.get('name', '')}_{token_params.get('decimals', 9)}"
            seed_hash = hashlib.sha256(seed_data.encode()).digest()
            public_key = base58.b58encode(seed_hash).decode()

            return {
                "public_key": public_key,
                "private_key": "DETERMINISTIC_KEY_PLACEHOLDER"
            }

    @classmethod
    def _generate_deterministic_mint_address(cls, chain_id: str, from_address: str, calldata: str) -> str:
        """生成确定性的mint地址"""
        import hashlib
        import base58

        # 使用输入参数生成确定性的地址
        data = f"{chain_id}_{from_address}_{calldata}".encode()
        hash_result = hashlib.sha256(data).digest()

        # 模拟Solana地址格式（32字节）
        mock_address = base58.b58encode(hash_result).decode()
        logger.info(f"Generated deterministic Solana mint address: {mock_address}")
        return mock_address

    @classmethod
    def _calculate_mint_rent_exempt_amount(cls, token_params: Dict[str, Any]) -> int:
        """计算mint账户的租金豁免金额"""
        # 基础mint账户大小
        base_size = 82  # 基础Mint账户大小

        # 根据扩展计算额外空间
        extensions = token_params.get("extensions", [])
        extension_size = 0

        for extension in extensions:
            ext_type = extension.get("type", "")
            if ext_type == "TRANSFER_FEE":
                extension_size += 108  # TransferFeeConfig大小
            elif ext_type == "DEFAULT_ACCOUNT_STATE":
                extension_size += 1   # DefaultAccountState大小
            elif ext_type == "IMMUTABLE_OWNER":
                extension_size += 0   # ImmutableOwner无额外空间
            elif ext_type == "INTEREST_BEARING":
                extension_size += 18  # InterestBearingConfig大小
            # 可以添加更多扩展的大小计算

        total_size = base_size + extension_size

        # 模拟租金计算（实际需要查询Solana网络）
        # 假设每字节需要约6960 lamports的租金
        rent_per_byte = 6960
        return total_size * rent_per_byte

    @classmethod
    def _calculate_mint_account_size(cls, token_params: Dict[str, Any]) -> int:
        """计算mint账户大小"""
        base_size = 82
        extensions = token_params.get("extensions", [])
        extension_size = 0

        for extension in extensions:
            ext_type = extension.get("type", "")
            if ext_type == "TRANSFER_FEE":
                extension_size += 108
            elif ext_type == "DEFAULT_ACCOUNT_STATE":
                extension_size += 1
            elif ext_type == "IMMUTABLE_OWNER":
                extension_size += 0
            elif ext_type == "INTEREST_BEARING":
                extension_size += 18

        return base_size + extension_size

    @classmethod
    def _build_create_account_instruction(cls, payer, mint, token_params: Dict[str, Any]) -> Dict[str, Any]:
        """构建创建账户指令"""
        return {
            "type": "create_account",
            "program_id": str(SYSTEM_PROGRAM_ID),
            "accounts": {
                "from": str(payer),
                "to": str(mint),
            },
            "data": {
                "lamports": cls._calculate_mint_rent_exempt_amount(token_params),
                "space": cls._calculate_mint_account_size(token_params),
                "owner": cls.TOKEN_2022_PROGRAM_ID,
            }
        }

    @classmethod
    def _build_extension_instruction_with_anchor(cls, program, mint_pubkey, extension: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用anchorpy构建扩展指令"""
        ext_type = extension.get("type", "")

        try:
            if ext_type == "TRANSFER_FEE":
                # 使用anchorpy构建Transfer Fee指令
                transfer_fee_config_authority = None
                if extension.get("transfer_fee_config_authority"):
                    transfer_fee_config_authority = Pubkey.from_string(extension["transfer_fee_config_authority"])

                withdraw_withheld_authority = None
                if extension.get("withdraw_withheld_authority"):
                    withdraw_withheld_authority = Pubkey.from_string(extension["withdraw_withheld_authority"])

                ix = program.instruction["initialize_transfer_fee_config"](
                    transfer_fee_config_authority=transfer_fee_config_authority,
                    withdraw_withheld_authority=withdraw_withheld_authority,
                    transfer_fee_basis_points=extension.get("transfer_fee_basis_points", 0),
                    maximum_fee=extension.get("maximum_fee", 0),
                    ctx=program.ctx(
                        accounts={"mint": mint_pubkey}
                    )
                )

                return {
                    "type": "initialize_transfer_fee_config",
                    "program_id": str(program.program_id),
                    "accounts": {"mint": str(mint_pubkey)},
                    "data": {
                        "transfer_fee_config_authority": str(transfer_fee_config_authority) if transfer_fee_config_authority else None,
                        "withdraw_withheld_authority": str(withdraw_withheld_authority) if withdraw_withheld_authority else None,
                        "transfer_fee_basis_points": extension.get("transfer_fee_basis_points", 0),
                        "maximum_fee": extension.get("maximum_fee", 0),
                    },
                    "instruction_data": ix.data.hex() if hasattr(ix, 'data') else None
                }

            elif ext_type == "DEFAULT_ACCOUNT_STATE":
                # 账户状态映射
                state_map = {"uninitialized": 0, "initialized": 1, "frozen": 2}
                state_value = state_map.get(extension.get("state", "initialized"), 1)

                ix = program.instruction["initialize_default_account_state"](
                    state=state_value,
                    ctx=program.ctx(
                        accounts={"mint": mint_pubkey}
                    )
                )

                return {
                    "type": "initialize_default_account_state",
                    "program_id": str(program.program_id),
                    "accounts": {"mint": str(mint_pubkey)},
                    "data": {"state": state_value},
                    "instruction_data": ix.data.hex() if hasattr(ix, 'data') else None
                }

            elif ext_type == "IMMUTABLE_OWNER":
                ix = program.instruction["initialize_immutable_owner"](
                    ctx=program.ctx(
                        accounts={"account": mint_pubkey}
                    )
                )

                return {
                    "type": "initialize_immutable_owner",
                    "program_id": str(program.program_id),
                    "accounts": {"account": str(mint_pubkey)},
                    "data": {},
                    "instruction_data": ix.data.hex() if hasattr(ix, 'data') else None
                }

        except Exception as e:
            logger.warning(f"Failed to build extension instruction with anchorpy for {ext_type}: {e}")
            # 降级到简单模式
            return cls._build_extension_instruction_fallback(extension, str(mint_pubkey))

        return None

    @classmethod
    def _build_metadata_instruction(cls, mint_pubkey, token_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构建元数据指令"""
        # Token 2022的元数据扩展（如果支持）
        if token_params.get("name") or token_params.get("symbol"):
            return {
                "type": "initialize_metadata",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {
                    "mint": str(mint_pubkey),
                    "update_authority": token_params.get("mint_authority"),
                },
                "data": {
                    "name": token_params.get("name", ""),
                    "symbol": token_params.get("symbol", ""),
                    "uri": token_params.get("uri", ""),
                }
            }
        return None

    @classmethod
    def _build_fallback_instructions(cls, chain_id: str, execute_address: str, token_params: Dict[str, Any]) -> str:
        """降级到简单指令构建模式"""
        logger.info("Using fallback instruction building mode")

        # 生成mint地址
        mint_keypair = cls._generate_mint_keypair(token_params)

        # 构造简单的指令结构
        instructions = {
            "type": "solana_token_2022_deploy_fallback",
            "chain_id": chain_id,
            "payer": execute_address,
            "mint_address": mint_keypair["public_key"],
            "program_id": cls.TOKEN_2022_PROGRAM_ID,
            "token_params": token_params,
            "instructions": [
                {
                    "type": "create_account",
                    "note": "Create mint account with System Program"
                },
                {
                    "type": "initialize_extensions",
                    "note": "Initialize Token 2022 extensions"
                },
                {
                    "type": "initialize_mint",
                    "note": "Initialize mint with Token 2022 program"
                }
            ],
            "note": "Fallback mode - anchorpy not available"
        }

        return json.dumps(instructions, indent=2)

    @classmethod
    def _build_extension_instruction_fallback(cls, extension: Dict[str, Any], mint_address: str) -> Optional[Dict[str, Any]]:
        """降级模式的扩展指令构建"""
        ext_type = extension.get("type", "")

        if ext_type == "TRANSFER_FEE":
            return {
                "type": "initialize_transfer_fee_config",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {"mint": mint_address},
                "data": {
                    "transfer_fee_config_authority": extension.get("transfer_fee_config_authority"),
                    "withdraw_withheld_authority": extension.get("withdraw_withheld_authority"),
                    "transfer_fee_basis_points": extension.get("transfer_fee_basis_points", 0),
                    "maximum_fee": extension.get("maximum_fee", 0),
                },
                "mode": "fallback"
            }

        elif ext_type == "DEFAULT_ACCOUNT_STATE":
            return {
                "type": "initialize_default_account_state",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {"mint": mint_address},
                "data": {"state": extension.get("state", "initialized")},
                "mode": "fallback"
            }

        elif ext_type == "IMMUTABLE_OWNER":
            return {
                "type": "initialize_immutable_owner",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {"mint": mint_address},
                "data": {},
                "mode": "fallback"
            }

        else:
            logger.warning(f"Unsupported extension type in fallback mode: {ext_type}")
            return None

    # 保留原有方法以兼容旧测试
    @classmethod
    def _build_extension_instruction(cls, extension: Dict[str, Any], mint_address: str) -> Optional[Dict[str, Any]]:
        """构建扩展初始化指令 - 兼容性方法"""
        return cls._build_extension_instruction_fallback(extension, mint_address)


# 为了兼容性，保留原有的类名
class SolToken2022InstrustionsManager(SolanaToken2022CalldataManager):
    """兼容性别名"""
    pass
