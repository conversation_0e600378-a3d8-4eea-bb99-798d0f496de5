"""
Solana Token 2022 Calldata 管理器
"""
import logging
import json
import hashlib
import base58
from typing import Dict, Any, List, Optional

logger = logging.getLogger("waas2.tokenization.solana.calldata")


class SolanaToken2022CalldataManager:
    """Solana Token 2022 部署Calldata管理器"""

    # Token 2022 Program ID
    TOKEN_2022_PROGRAM_ID = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"

    @classmethod
    def build_deploy_calldata(
        cls,
        chain_id: str,
        execute_address: str,
        token_params: Dict[str, Any],
    ) -> str:
        """
        构造Solana Token 2022部署指令数据

        Args:
            chain_id: 链ID (如 "SOL", "SOL_DEVNET")
            execute_address: 执行地址 (payer)
            token_params: 代币参数

        Returns:
            序列化的指令数据 (JSON格式)
        """
        logger.info(f"Building Solana Token 2022 deploy instructions for {token_params.get('symbol', 'UNKNOWN')}")

        # 生成mint地址
        mint_keypair = cls._generate_mint_keypair(token_params)

        # 构造指令序列
        instructions = {
            "type": "solana_token_2022_deploy",
            "chain_id": chain_id,
            "payer": execute_address,
            "mint_address": mint_keypair["public_key"],
            "token_params": {
                "name": token_params.get("name", ""),
                "symbol": token_params.get("symbol", ""),
                "decimals": token_params.get("decimals", 9),
                "mint_authority": token_params.get("mint_authority"),
                "freeze_authority": token_params.get("freeze_authority"),
                "close_authority": token_params.get("close_authority"),
            },
            "extensions": token_params.get("extensions", []),
            "instructions": []
        }

        # 1. 创建mint账户指令
        instructions["instructions"].append({
            "type": "create_account",
            "program_id": "11111111111111111111111111111112",  # System Program
            "accounts": {
                "from": execute_address,
                "to": mint_keypair["public_key"],
            },
            "data": {
                "lamports": cls._calculate_mint_rent_exempt_amount(token_params),
                "space": cls._calculate_mint_account_size(token_params),
                "owner": cls.TOKEN_2022_PROGRAM_ID,
            }
        })

        # 2. 初始化扩展指令（必须在InitializeMint之前）
        for extension in token_params.get("extensions", []):
            ext_instruction = cls._build_extension_instruction(extension, mint_keypair["public_key"])
            if ext_instruction:
                instructions["instructions"].append(ext_instruction)

        # 3. 初始化mint指令
        instructions["instructions"].append({
            "type": "initialize_mint",
            "program_id": cls.TOKEN_2022_PROGRAM_ID,
            "accounts": {
                "mint": mint_keypair["public_key"],
            },
            "data": {
                "decimals": token_params.get("decimals", 9),
                "mint_authority": token_params.get("mint_authority"),
                "freeze_authority": token_params.get("freeze_authority"),
            }
        })

        # 4. 设置元数据（如果提供了name和symbol）
        if token_params.get("name") or token_params.get("symbol"):
            instructions["instructions"].append({
                "type": "initialize_metadata",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {
                    "mint": mint_keypair["public_key"],
                    "update_authority": token_params.get("mint_authority"),
                },
                "data": {
                    "name": token_params.get("name", ""),
                    "symbol": token_params.get("symbol", ""),
                    "uri": token_params.get("uri", ""),
                }
            })

        # 返回序列化的指令数据
        return json.dumps(instructions, indent=2)

    @classmethod
    def predict_contract_address(
        cls,
        chain_id: str,
        from_address: str,
        calldata: str,
    ) -> str:
        """
        生成/预测Solana mint地址

        Args:
            chain_id: 链ID
            from_address: 发送地址
            calldata: 指令数据

        Returns:
            mint地址
        """
        try:
            # 解析calldata获取mint地址
            instructions_data = json.loads(calldata)
            mint_address = instructions_data.get("mint_address")

            if mint_address:
                logger.info(f"Using predefined mint address: {mint_address}")
                return mint_address

        except (json.JSONDecodeError, KeyError) as e:
            logger.warning(f"Failed to parse calldata for mint address: {e}")

        # 如果无法从calldata获取，生成一个确定性地址
        return cls._generate_deterministic_mint_address(chain_id, from_address, calldata)

    @classmethod
    def _generate_mint_keypair(cls, token_params: Dict[str, Any]) -> Dict[str, str]:
        """生成mint密钥对"""
        import hashlib
        import base58

        # 使用代币参数生成确定性的密钥对
        seed_data = f"{token_params.get('symbol', '')}_{token_params.get('name', '')}_{token_params.get('decimals', 9)}"
        seed_hash = hashlib.sha256(seed_data.encode()).digest()

        # 模拟生成Solana公钥（32字节）
        public_key = base58.b58encode(seed_hash).decode()

        return {
            "public_key": public_key,
            "private_key": "PRIVATE_KEY_PLACEHOLDER"  # 实际实现中需要真实的密钥生成
        }

    @classmethod
    def _generate_deterministic_mint_address(cls, chain_id: str, from_address: str, calldata: str) -> str:
        """生成确定性的mint地址"""
        import hashlib
        import base58

        # 使用输入参数生成确定性的地址
        data = f"{chain_id}_{from_address}_{calldata}".encode()
        hash_result = hashlib.sha256(data).digest()

        # 模拟Solana地址格式（32字节）
        mock_address = base58.b58encode(hash_result).decode()
        logger.info(f"Generated deterministic Solana mint address: {mock_address}")
        return mock_address

    @classmethod
    def _calculate_mint_rent_exempt_amount(cls, token_params: Dict[str, Any]) -> int:
        """计算mint账户的租金豁免金额"""
        # 基础mint账户大小
        base_size = 82  # 基础Mint账户大小

        # 根据扩展计算额外空间
        extensions = token_params.get("extensions", [])
        extension_size = 0

        for extension in extensions:
            ext_type = extension.get("type", "")
            if ext_type == "TRANSFER_FEE":
                extension_size += 108  # TransferFeeConfig大小
            elif ext_type == "DEFAULT_ACCOUNT_STATE":
                extension_size += 1   # DefaultAccountState大小
            elif ext_type == "IMMUTABLE_OWNER":
                extension_size += 0   # ImmutableOwner无额外空间
            elif ext_type == "INTEREST_BEARING":
                extension_size += 18  # InterestBearingConfig大小
            # 可以添加更多扩展的大小计算

        total_size = base_size + extension_size

        # 模拟租金计算（实际需要查询Solana网络）
        # 假设每字节需要约6960 lamports的租金
        rent_per_byte = 6960
        return total_size * rent_per_byte

    @classmethod
    def _calculate_mint_account_size(cls, token_params: Dict[str, Any]) -> int:
        """计算mint账户大小"""
        base_size = 82
        extensions = token_params.get("extensions", [])
        extension_size = 0

        for extension in extensions:
            ext_type = extension.get("type", "")
            if ext_type == "TRANSFER_FEE":
                extension_size += 108
            elif ext_type == "DEFAULT_ACCOUNT_STATE":
                extension_size += 1
            elif ext_type == "IMMUTABLE_OWNER":
                extension_size += 0
            elif ext_type == "INTEREST_BEARING":
                extension_size += 18

        return base_size + extension_size

    @classmethod
    def _build_extension_instruction(cls, extension: Dict[str, Any], mint_address: str) -> Optional[Dict[str, Any]]:
        """构建扩展初始化指令"""
        from typing import Optional

        ext_type = extension.get("type", "")

        if ext_type == "TRANSFER_FEE":
            return {
                "type": "initialize_transfer_fee_config",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {
                    "mint": mint_address,
                },
                "data": {
                    "transfer_fee_config_authority": extension.get("transfer_fee_config_authority"),
                    "withdraw_withheld_authority": extension.get("withdraw_withheld_authority"),
                    "transfer_fee_basis_points": extension.get("transfer_fee_basis_points", 0),
                    "maximum_fee": extension.get("maximum_fee", 0),
                }
            }

        elif ext_type == "DEFAULT_ACCOUNT_STATE":
            return {
                "type": "initialize_default_account_state",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {
                    "mint": mint_address,
                },
                "data": {
                    "state": extension.get("state", "initialized"),
                }
            }

        elif ext_type == "IMMUTABLE_OWNER":
            return {
                "type": "initialize_immutable_owner",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {
                    "mint": mint_address,
                },
                "data": {}
            }

        elif ext_type == "INTEREST_BEARING":
            return {
                "type": "initialize_interest_bearing_mint",
                "program_id": cls.TOKEN_2022_PROGRAM_ID,
                "accounts": {
                    "mint": mint_address,
                },
                "data": {
                    "rate_authority": extension.get("rate_authority"),
                    "rate": extension.get("rate", 0),
                }
            }

        else:
            logger.warning(f"Unsupported extension type: {ext_type}")
            return None


# 为了兼容性，保留原有的类名
class SolToken2022InstrustionsManager(SolanaToken2022CalldataManager):
    """兼容性别名"""
    pass
