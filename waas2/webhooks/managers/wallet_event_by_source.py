import logging
from typing import List, Tuple

from cobo_libs.utils.logger import timing_logger

from custody.custody.managers.config import OrganizationConfigManager
from waas2.async_signals.enums import ReceiverResult
from waas2.base.dao.wallet_source import WalletSourceDao
from waas2.transaction_query.enums import QueryTransactionStatus
from waas2.transaction_query.enums.transaction_query import QueryTransactionWalletType
from waas2.transactions.dev.enums.transaction import TransactionStatus
from waas2.transactions.mongo.manager.read.waas_transaction import (
    WaaSTransactionViewModelConvert,
)
from waas2.transactions.mongo.manager.transaction_client import TransactionMongoClient
from waas2.transactions.mongo.models.transaction import TransactionDetailData
from waas2.webhooks.daos import WebhookEventDao
from waas2.webhooks.data.enums import WebhookEventNameEnum
from waas2.webhooks.managers.wallet_event import WalletEventManager

logger = logging.getLogger("waas2.webhooks.wallet_event")


class TransactionEventManager(object):
    transaction_status_event_type_mapping = {
        QueryTransactionStatus.Success.value: WebhookEventNameEnum.EN_SUCCEEDED,
        QueryTransactionStatus.Failed.value: WebhookEventNameEnum.EN_FAILED,
        QueryTransactionStatus.Rejected.value: WebhookEventNameEnum.EN_FAILED,
        QueryTransactionStatus.Canceled.value: WebhookEventNameEnum.EN_FAILED,
    }
    _portal_org_cache = set()

    @classmethod
    @timing_logger(
        func_name="TransactionEventManager.create_event",
        log_to_influxdb=True,
        log_queries=True,
    )
    def create_event(
        cls,
        tx_details: TransactionDetailData,
        created: bool = False,
    ):
        """
        :param created: 本次时间是否由create触发
        :param tx_details: 交易详情
        :return:
        """
        logger.info(f"create event, tx_details:{tx_details}, created:{created}.")
        if not tx_details or not tx_details.has_dest_and_source:
            logger.info("tx_details is None, skip creating event for")
            return
        org_id = tx_details.org.biz_id
        if org_id in cls._portal_org_cache:
            is_portal_org = True
        else:
            is_portal_org = OrganizationConfigManager.is_portal_org_only(org_id)
            if is_portal_org:
                cls._portal_org_cache.add(org_id)
        if not is_portal_org:
            logger.info(
                f"ignore create event with {tx_details}, because not portal org"
            )
            return
        cls._create_event_by_transaction_source(
            tx_details=tx_details,
            created=created,
        )

    @classmethod
    def _create_event_by_transaction_source(
        cls,
        tx_details: TransactionDetailData,
        created: bool = False,
    ):
        if not tx_details or not tx_details.has_dest_and_source:
            logger.info(f"tx_details is None, skip creating event for {tx_details}")
            return
        fee_station = (
            tx_details.wallet.query_type == QueryTransactionWalletType.GasStation
        )
        events_to_send = cls.get_events_to_send(created, tx_details)
        if not events_to_send:
            logger.info(
                f"no events to send for transaction {tx_details.transaction_id}"
            )
            return
        transaction_data = WaaSTransactionViewModelConvert.populate_detail(
            tx_details
        ).to_dict()
        if not transaction_data:
            logger.info("transaction_data is None")
            return
        transaction_data["data_type"] = "Transaction"
        # TODO 改成异步
        try:
            with timing_logger(
                "PaymentTransactionController.handle_tx_event", log_to_influxdb=True
            ):
                from waas2.payment.controllers.transaction import (
                    PaymentTransactionController,
                )

                PaymentTransactionController.handle_tx_event(transaction_data)
        except Exception as e:
            logger.warning(
                f"PaymentTransactionController.handle_tx_event: {e}", exc_info=True
            )
        wallet_scopes_info = cls.get_wallet_scope_info(
            transaction_data["wallet_id"], fee_station
        )
        for event_name, event_uuid in events_to_send:
            WalletEventManager.create_transaction_event(
                transaction_data=transaction_data,
                fee_station=fee_station,
                event_name=event_name,
                wallet_scopes_info=wallet_scopes_info,
                event_uuid=event_uuid,
                org_uuid=tx_details.org.id,
            )

    @classmethod
    def get_wallet_scope_info(cls, wallet_id: str, fee_station: bool):
        wallet_source = WalletSourceDao.get_by_wallet_id(wallet_id)
        wallet_scopes_info = {"wallet_id": wallet_id}
        if wallet_source and fee_station is False:
            wallet_scopes_info["wallet_type"] = wallet_source.type
            wallet_scopes_info["wallet_subtype"] = wallet_source.subtype
            if wallet_source.project_id:
                wallet_scopes_info["project_id"] = wallet_source.project_id
            if wallet_source.vault_id:
                wallet_scopes_info["vault_id"] = wallet_source.vault_id
        return wallet_scopes_info

    @classmethod
    def get_events_to_send(
        cls, created: bool, tx_details: TransactionDetailData
    ) -> list[Tuple[WebhookEventNameEnum, str]]:
        events_to_send = []
        transaction_id = tx_details.transaction_id
        if not transaction_id:
            logger.warning(
                f"transaction_id is None for transaction details: {tx_details}"
            )
            return events_to_send
        status = TransactionStatus.from_query_tx_status(
            QueryTransactionStatus.from_value(tx_details.status)
        )
        if not status:
            logger.warning(f"status is None for transaction details: {tx_details}")
            return events_to_send
        confirmed_num = tx_details.tx.confirmed_num if tx_details.tx else None
        if created:
            result = cls._try_create_event(
                WebhookEventNameEnum.EN_CREATED, transaction_id, status, confirmed_num
            )
            if result:
                events_to_send.append(result)
        # always trigger an update event
        result = cls._try_create_event(
            WebhookEventNameEnum.EN_UPDATED, transaction_id, status, confirmed_num
        )
        if result:
            events_to_send.append(result)
        if tx_details.status in cls.transaction_status_event_type_mapping:
            event_name = cls.transaction_status_event_type_mapping[tx_details.status]
            result = cls._try_create_event(
                event_name, transaction_id, status, confirmed_num
            )
            if result:
                events_to_send.append(result)
        return events_to_send

    @classmethod
    def _try_create_event(
        cls, event_name, transaction_id: str, status: str, confirmed_num: int | None
    ) -> Tuple[WebhookEventNameEnum, str] | None:
        event_uuid = WalletEventManager.get_event_id(
            event_name, transaction_id, status, confirmed_num
        )
        if not WebhookEventDao.check_exist_by_uuid(event_uuid):
            return event_name, event_uuid
        else:
            logger.info(
                f"event {event_name} with transaction_id {transaction_id} already exists, skip creating."
            )
            return None

    @classmethod
    def create_event_by_transaction_ids(
        cls, transaction_ids: list[str], created: bool = False
    ) -> List[ReceiverResult] | None:
        if not transaction_ids:
            logger.info("No transaction IDs provided, skipping event creation.")
            return None
        results: List[ReceiverResult] = []
        for transaction_id in transaction_ids:
            tx_details = TransactionMongoClient.get_transaction_by_transaction_id(
                transaction_id
            )
            if tx_details:
                try:
                    cls._create_event_by_transaction_source(tx_details, created)
                    results.append(ReceiverResult.SUCCESS)
                except Exception as e:
                    logger.warning(
                        f"create_event_by_transaction_ids: {e}", exc_info=True
                    )
                    results.append(ReceiverResult.RETRY)
        return results
