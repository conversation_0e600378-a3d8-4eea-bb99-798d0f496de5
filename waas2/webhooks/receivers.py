import logging

from custody.aladdin.bo.suspended_coin import TokenSuspendedEvent
from custody.aladdin.signal.async_signals import token_suspended_signal
from custody.cobo.utils.cache import cache_instance
from custody.custody.utils.feature_switch import (
    SwitchNameEnum,
    SwitchTypeEnum,
    hit_switch,
)
from waas2.async_signals import receiver
from waas2.async_signals.data import SignalContext
from waas2.transaction_query.signals import (
    WaaS2TransactionUpdatedData,
    waas2_transaction_created,
    waas2_transaction_updated,
)
from waas2.webhooks.managers.token_suspended_event import TokenSuspendedEventManager
from waas2.webhooks.managers.wallet_event_by_source import TransactionEventManager

logger = logging.getLogger("wass2.webhooks.token_suspended_receivers")

SUSPENDED_TOKEN_PREFIX = "suspended:token:"


@receiver(
    waas2_transaction_updated,
    consumer_group="webhook_processor",
    main_worker_batch_size=10,
    retry_worker_batch_size=1,
)
def trigger_update_tx_webhook_events(
    data_list: list[WaaS2TransactionUpdatedData], context: SignalContext, **kwargs
):
    if hit_switch(
        switch_type=SwitchTypeEnum.SWITCH_TYPE_BOOL,
        switch_name=SwitchNameEnum.TRIGGER_TX_WEBHOOK_BY_SIGNAL,
        param_value=1,
    ):
        transaction_ids = [data.transaction_id for data in data_list]
        return TransactionEventManager.create_event_by_transaction_ids(transaction_ids)


@receiver(
    waas2_transaction_created,
    consumer_group="webhook_processor",
    main_worker_batch_size=10,
    retry_worker_batch_size=1,
)
def trigger_create_tx_webhook_events(
    data_list: list[WaaS2TransactionUpdatedData], context: SignalContext, **kwargs
):
    if hit_switch(
        switch_type=SwitchTypeEnum.SWITCH_TYPE_BOOL,
        switch_name=SwitchNameEnum.TRIGGER_TX_WEBHOOK_BY_SIGNAL,
        param_value=1,
    ):
        transaction_ids = [data.transaction_id for data in data_list]
        return TransactionEventManager.create_event_by_transaction_ids(
            transaction_ids, True
        )


@receiver(token_suspended_signal, consumer_group="webhook_token_suspended_processor")
def process_token_suspended(
    data_list: list[TokenSuspendedEvent], context: SignalContext, **kwargs
):
    logger.info(f"Processing token suspended event_datas: {data_list}")
    for data in data_list:
        # 幂等判断 操作频率较低 存储12个小时
        if not cache_instance.set(
            SUSPENDED_TOKEN_PREFIX + data.event_id, "1", 60 * 60 * 12, nx=True
        ):
            continue
        TokenSuspendedEventManager.on_token_suspended_event(data)
