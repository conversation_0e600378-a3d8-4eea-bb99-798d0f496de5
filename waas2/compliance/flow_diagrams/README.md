# Compliance Flow Diagrams

This directory contains flow diagrams for compliance-related processes in the MPC transaction system.

## Diagrams

1. **MPC Transaction Compliance Check Flow** - `mpc_transaction_compliance_flow.md`
   - Flow 1: MPC transaction deposit compliance check process
   - Flow 2: Screening APP unfreeze transaction process

## Usage

These diagrams are written in Mermaid format and can be rendered in:
- GitHub (native support)
- Git<PERSON>ab (native support)
- VS Code with Mermaid extension
- Online Mermaid editor: https://mermaid.live/

## Flow Descriptions

### Flow 1: MPC Transaction Deposit Compliance Check
This flow describes the complete process of compliance checking for MPC transaction deposits, including:
- BC transaction capture
- Portal interface for KYT compliance check
- Transaction amount locking/unlocking
- Compliance result synchronization
- Final transaction processing

### Flow 2: Screening APP Unfreeze Transaction
This flow describes the process of unfreezing transactions through the Screening APP, including:
- Screening APP unfreeze request
- Compliance system validation
- BC notification and processing
- Portal synchronization 