# MPC Compliance Check Sequence Diagrams

This document contains sequence diagrams for MPC compliance processes.

## Sequence Diagram 1: MPC Transaction Deposit Compliance Check

```mermaid
sequenceDiagram
    participant BC as Blockchain Client
    participant Portal as Portal System
    participant Compliance as Compliance System
    participant DB as Database

    BC->>Portal: 1. mpc_need_kyt(transaction_data)
    Portal->>Compliance: 2. check_compliance_required(transaction_data)
    Compliance->>DB: 3. query_compliance_rules
    DB-->>Compliance: 4. compliance_rules
    Compliance-->>Portal: 5. compliance_required: true/false
    Portal-->>BC: 6. response with status_code

    alt Compliance Required
        BC->>BC: 7. Lock transaction amount
        BC->>Portal: 8. callback(status_code=350, locked_amount)
        Portal->>Portal: 9. Sync lock transaction amount
        BC->>Compliance: 10. get_compliance_result(transaction_id)
        Compliance->>DB: 11. query_compliance_status
        DB-->>Compliance: 12. compliance_status
        Compliance-->>BC: 13. compliance_result

        alt Compliance Passed
            BC->>BC: 14. Unlock transaction amount
            BC->>Portal: 15. callback(compliance_passed, unlocked_amount)
            Portal->>Portal: 16. Sync unlock amount
        else Compliance Failed
            BC->>BC: 17. Freeze transaction amount
            BC->>Portal: 18. callback(compliance_failed, frozen_amount)
            Portal->>Portal: 19. Sync freeze amount
        end
    else No Compliance Required
        BC->>BC: 20. Process transaction normally
        BC->>Portal: 21. callback(transaction_completed)
    end
```

## Sequence Diagram 2: Screening APP Unfreeze Transaction

```mermaid
sequenceDiagram
    participant SA as Screening APP
    participant Compliance as Compliance System
    participant BC as Blockchain Client
    participant Portal as Portal System
    participant DB as Database

    SA->>Compliance: 1. unfreeze_funds(transaction_id)
    Compliance->>DB: 2. query_transaction_compliance_result
    DB-->>Compliance: 3. compliance_result

    alt Compliance Result Valid
        Compliance->>DB: 4. update_compliance_result
        Compliance->>BC: 5. notify_compliance_update(transaction_id)
        BC->>BC: 6. Process logic based on compliance result
        BC->>Portal: 7. notify_transaction_update(transaction_id, status)
        Portal->>Portal: 8. Update transaction status and unfreeze amount
        Compliance-->>SA: 9. unfreeze_success_response
    else Compliance Result Invalid
        Compliance-->>SA: 10. error_response(invalid_compliance_result)
    end
```

## Detailed State Transitions

### Transaction States

```mermaid
stateDiagram-v2
    [*] --> Pending: Transaction Captured
    Pending --> Locked: Compliance Required
    Pending --> Completed: No Compliance Required
    Locked --> Unlocked: Compliance Passed
    Locked --> Frozen: Compliance Failed
    Unlocked --> Completed: Transaction Processed
    Frozen --> Unfrozen: Manual Unfreeze
    Unfrozen --> Completed: Transaction Processed
    Completed --> [*]
```

### Amount States

```mermaid
stateDiagram-v2
    [*] --> Available: Initial State
    Available --> Locked: Compliance Check Required
    Locked --> Available: Compliance Passed
    Locked --> Frozen: Compliance Failed
    Frozen --> Available: Manual Unfreeze
    Available --> [*]: Transaction Completed
```

## Error Scenarios

### Scenario 1: Network Failure During Compliance Check

```mermaid
sequenceDiagram
    participant BC as Blockchain Client
    participant Portal as Portal System
    participant Compliance as Compliance System

    BC->>Portal: mpc_need_kyt(transaction_data)
    Portal->>Compliance: check_compliance_required(transaction_data)
    Note over Compliance: Network timeout
    Portal-->>BC: timeout_error
    BC->>BC: Retry after delay
    BC->>Portal: mpc_need_kyt(transaction_data) [retry]
```

### Scenario 2: Compliance System Unavailable

```mermaid
sequenceDiagram
    participant BC as Blockchain Client
    participant Portal as Portal System
    participant Compliance as Compliance System

    BC->>Portal: mpc_need_kyt(transaction_data)
    Portal->>Compliance: check_compliance_required(transaction_data)
    Note over Compliance: Service unavailable
    Portal-->>BC: service_unavailable_error
    BC->>BC: Fallback to default behavior
    BC->>Portal: process_with_default_compliance
```

## Monitoring Points

1. **Transaction Capture Rate**: Monitor BC transaction capture performance
2. **Compliance Check Latency**: Track time from capture to compliance decision
3. **Lock/Unlock Success Rate**: Monitor transaction amount state changes
4. **Error Rates**: Track various error scenarios and their frequencies
5. **System Availability**: Monitor all system components health

## Performance Considerations

- **Batch Processing**: Consider batching compliance checks for multiple transactions
- **Caching**: Cache compliance rules and results where appropriate
- **Async Processing**: Use async processing for non-critical compliance operations
- **Retry Logic**: Implement exponential backoff for failed operations
- **Circuit Breaker**: Implement circuit breaker pattern for external service calls 