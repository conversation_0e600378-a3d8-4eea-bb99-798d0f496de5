# MPC Transaction Compliance Check Flow

This document contains flow diagrams for MPC transaction compliance processes.

## Flow 1: MPC Transaction Deposit Compliance Check Process

```mermaid
flowchart TD
    A[BC捕获交易] --> B[请求Portal接口mpc_need_kyt<br/>判断是否需要合规检查]
    B --> C{是否需要合规检查?}
    C -->|否| D[正常入账]
    C -->|是| E[回调status_code=350的message<br/>同时锁定交易金额]
    E --> F[Portal收到status_code=350的通知<br/>同步锁定交易金额]
    F --> G[BC通过get_compliance_result/<br/>list_compliance_result接口获取合规结果]
    G --> H{合规结果?}
    H -->|通过| I[回调合规通过结果<br/>并解锁交易金额]
    H -->|不通过| J[BC将锁定金额改为冻结<br/>并回调结果]
    I --> K[Portal收到通知后<br/>同步解锁金额]
    J --> L[Portal收到通知后<br/>同步将锁定的金额改为冻结]
    K --> M[流程结束]
    L --> M
    D --> M
    
    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style E fill:#fff3e0
    style J fill:#ffebee
```

## Flow 2: Screening APP Unfreeze Transaction Process

```mermaid
flowchart TD
    A[Screening APP 解冻交易] --> B[Screening APP 调用<br/>合规系统的解冻接口]
    B --> C[合规系统检查交易合规结果]
    C --> D{是否符合预期?}
    D -->|否| E[报错]
    D -->|是| F[更新交易合规结果<br/>并通知BC]
    F --> G[BC收到合规结果更新后<br/>进行对应的逻辑处理<br/>并通知Portal]
    G --> H[Portal收到对应通知后<br/>进行金额解冻及交易状态更新]
    H --> I[流程结束]
    E --> I
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style E fill:#ffebee
    style F fill:#fff3e0
```

## System Components

### BC (Blockchain Client)
- 负责捕获区块链交易
- 管理交易状态和金额锁定/解锁
- 与Portal和合规系统进行通信

### Portal
- 提供mpc_need_kyt接口进行合规检查判断
- 同步交易状态和金额变化
- 管理用户界面和通知

### Compliance System
- 执行合规检查逻辑
- 提供合规结果查询接口
- 处理解冻请求和验证

### Screening APP
- 提供交易解冻功能
- 调用合规系统接口
- 管理解冻流程

## Status Codes

- **350**: 需要合规检查，交易金额已锁定
- **200**: 正常处理成功
- **403**: 权限不足或合规检查失败
- **400**: 参数错误或请求无效

## Key Interfaces

1. **mpc_need_kyt**: 判断交易是否需要合规检查
2. **get_compliance_result**: 获取单个交易的合规结果
3. **list_compliance_result**: 批量获取交易合规结果
4. **unfreeze_funds**: 解冻交易资金

## Error Handling

- 所有接口调用失败时应有适当的重试机制
- 状态同步失败时应有补偿机制
- 关键操作应有日志记录和监控 