import json
import logging
from typing import List

from django.conf import settings

from custody.custody.managers.organization import OrganizationManager
from custody.custody.models.transaction import WithdrawRequest
from custody.web3.dao.transaction_request import TransactionRequestDao
from custody.web3.models.transaction_request import TransactionRequest
from waas2.coins.bo_v2 import WaaSChainInfo
from waas2.coins.managers_v2 import Waa<PERSON>hain<PERSON>anager, WaaSTokenManager
from waas2.compliance.black_addresses.managers.black_addresses import (
    BlackAddressesManager,
)
from waas2.compliance.black_addresses.processors.black_addresses import (
    BlackAddressesProcessor,
)
from waas2.compliance.black_addresses.processors.notification import (
    NotificationProcessor,
)
from waas2.compliance.black_addresses.utils.common import TimeUtils
from waas2.transaction_query.models import TransactionSource

logger = logging.getLogger("waas2.compliance.processors")


class WithdrawBlackAddressProcessor:
    @classmethod
    def is_black_address_from_withdraw(
        cls,
        request: WithdrawRequest,
        tx_source: TransactionSource = None,
        is_compliance_req: bool = False,
    ) -> bool:
        transaction_req = TransactionRequestDao.get_by_request_id(
            request.org_id, request.request_id
        )
        if not transaction_req:
            # 说明是全托管交易
            addresses = [request.to_addr]
            token_info = WaaSTokenManager.get_token(request.coin)
            chain_info = WaaSChainManager.get_chain(token_info.chain_id)
            return cls.check_withdraw_black_address(
                request, chain_info, addresses, tx_source, is_compliance_req
            )
        else:
            # mpc或者 web3交易
            addresses = [
                transaction_req.from_address,
            ]
            to_addresses = cls.get_to_addresses(transaction_req)
            if to_addresses:
                addresses.extend(to_addresses)
            chain_info = WaaSChainManager.get_chain(transaction_req.chain_coin)
            return cls.check_withdraw_black_address(
                request, chain_info, addresses, tx_source, is_compliance_req
            )

    @classmethod
    def get_to_addresses(cls, transaction_req: TransactionRequest):
        to_addresses = []
        if transaction_req.to_address:
            to_addresses.append(transaction_req.to_address)
        elif transaction_req.to_address_details:
            for address_detail in json.loads(transaction_req.to_address_details):
                if address_detail.get("to_address"):
                    to_addresses.append(address_detail["to_address"])
        else:
            logger.error(
                f"TransactionRequest {transaction_req.request_id} has invalid to address."
            )
        if not to_addresses:
            logger.warning(
                f"TransactionRequest {transaction_req.request_id} has not to addresses."
            )
        return to_addresses

    @classmethod
    def check_withdraw_black_address(
        cls,
        request: WithdrawRequest,
        chain_info: WaaSChainInfo,
        addresses: List[str],
        tx_source: TransactionSource = None,
        is_compliance_req: bool = False,
    ) -> bool:
        chain_id = chain_info.chain_id
        is_case_sensitive = True if chain_info.is_case_sensitive else False
        # 增加一个逻辑如果chain_id为ETH的话，同时address没有以0x开头，则加上0x前缀
        format_addresses = BlackAddressesProcessor.format_eth_addresses(
            chain_info.chain_identifier, addresses
        )
        if any(f != a for f, a in zip(format_addresses, addresses)):
            logger.warning(
                f"{request.request_id} Addresses {addresses} formatted to {format_addresses}"
            )
        black_addresses = BlackAddressesManager.query_addresses_by_case_sensitive(
            format_addresses, is_case_sensitive
        )
        if not black_addresses:
            logger.info(
                f"{request.request_id} No black addresses found for chain_id: {chain_id}, addresses: {addresses}"
            )
            return False
        else:
            logger.info(
                f"{request.request_id} Found black addresses for chain_id: {chain_id}, addresses: {addresses}"
            )
            actual_black_addresses = []
            for format_address in format_addresses:
                if BlackAddressesProcessor.is_in_black_addresses(
                    chain_id,
                    chain_info.chain_identifier,
                    format_address,
                    black_addresses,
                    log_str=request.request_id,
                ):
                    actual_black_addresses.append(format_address)
            if len(actual_black_addresses) == 0:
                return False
            msg = cls.build_notify_msg(
                request=request,
                chain_info=chain_info,
                actual_black_addresses=actual_black_addresses,
                tx_source=tx_source,
                is_compliance=is_compliance_req,
            )
            if is_compliance_req:
                NotificationProcessor.notice_compliance_withdraw_tx(msg)
                return True
            else:
                NotificationProcessor.notice_non_compliance_withdraw_tx(msg)
                return True

    @classmethod
    def build_notify_msg(
        cls,
        request: WithdrawRequest,
        chain_info: WaaSChainInfo,
        actual_black_addresses: List[str],
        tx_source: TransactionSource = None,
        is_compliance: bool = False,
    ):
        wallet_source = BlackAddressesProcessor.query_wallet_info(
            request.custody_wallet_id
        )
        wallet_type = wallet_source.type if wallet_source else ""
        wallet_subtype = wallet_source.subtype if wallet_source else ""
        header = cls.get_header_msg(is_compliance=is_compliance)
        tx_id = tx_source.uuid if tx_source else request.request_id
        black_address_str = ",".join(actual_black_addresses)
        org_id = request.org_id
        org_detail = OrganizationManager.query_by_org_id_with_cache(org_id)
        org_name = org_detail.name if org_detail else None
        compliance_flag = "Compliance" if is_compliance else "Non-Compliance"
        details = (
            f"*org_id:* {org_id} \n"
            f"*org_name:* {org_name} \n"
            f"*transaction_id:* {tx_id} \n"
            f"*request_id:* {request.request_id} \n"
            f"*wallet_id:* {request.custody_wallet_id} \n"
            f"*wallet_type:* {compliance_flag}|{wallet_type}|{wallet_subtype} \n"
            f"*chain_id:* {chain_info.chain_id} \n"
            f"*black_address:* {black_address_str}\n"
            f"*time:* {TimeUtils.get_now_dt()} \n"
        )
        msg = f"{header}{details}"
        return msg

    @classmethod
    def get_header_msg(cls, is_compliance):
        if is_compliance:
            header = f"*[Withdrawal Alert][{settings.ENV}] Transaction Rejected - Cobo Blacklisted Address* \n :warning: Action Required - Review Transaction \n A withdrawal transaction was rejected due to a match with a Cobo blacklisted address.\n"
        else:
            header = f"*[Withdrawal Alert][{settings.ENV}] Outgoing Transaction to Blacklisted Address* \n :warning: Action Required - Customer Notification\n A customer submitted a withdrawal to a Cobo blacklisted address, please notify the customer immediately.\n"
        return header
