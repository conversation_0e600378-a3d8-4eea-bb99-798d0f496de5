from custody.custody.utils.feature_switch import SwitchNameEnum, list_feature_switch
from waas2.coins.managers_v2 import WaaSChainManager


class EOAAddressManager:
    @classmethod
    def convert_eoa_address(
        cls, org_id: int, chain_id: str, address: str, check_switch=True
    ) -> str:
        if check_switch:
            if not cls._should_convert_address(org_id=org_id):
                return address
        chain_info = WaaSChainManager.get_chain(chain_id)
        if chain_info.is_case_sensitive:
            return address
        return address.lower()

    @classmethod
    @list_feature_switch(SwitchNameEnum.EOA_AUTO_CONVERT_ADDRESS, extra_key="org_id")
    def _should_convert_address(cls):
        return True
