from typing import List

from pydantic import BaseModel

from waas2.transactions.dev.enums.transaction_risk import TransactionRiskType


class TransactionRiskRequest(BaseModel):
    request_ids: str


class TransactionRiskContext(BaseModel):
    pass


class TransactionRiskAction(BaseModel):
    pass


class TransactionRiskPolicy(BaseModel):
    policy_uuid: str
    policy_name: str
    version: int
    type: TransactionRiskType = TransactionRiskType.USER_POLICY
    matched: bool = True
    context: str | None = None
    action: str | None = None


class TransactionRiskPolicyDecision(BaseModel):
    policy_uuid: str


class TransactionRiskDetailResponse(BaseModel):
    request_id: str
    risk_message: str | None = None
    flow: List[TransactionRiskPolicy]
    decision: List[TransactionRiskPolicyDecision]


class TransactionRiskExcludeResponse(BaseModel):
    result: bool
