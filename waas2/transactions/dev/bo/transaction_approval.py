from typing import List

from pydantic import BaseModel


class TransactionApprovalDetailRequest(BaseModel):
    transaction_ids: str | None = None
    request_ids: str | None = None
    cobo_ids: str | None = None


class TransactionApprovalShowInfo(BaseModel):
    org_name: str
    wallet_name: str
    environment: str
    from_address_label: str = ""
    to_address_label: str = ""


class TransactionApprovalUserDetail(BaseModel):
    user_email: str
    pubkey: str
    signature: str
    statement_uuid: str
    result: int
    created_time: int
    template_version: str
    header_title: str
    show_info: TransactionApprovalShowInfo
    is_for_sign: bool = False


class TransactionApprovalRoleDetail(BaseModel):
    result: int
    threshold: int
    user_details: List[TransactionApprovalUserDetail]


class TransactionApprovalDetailResponse(BaseModel):
    broker_user: TransactionApprovalRoleDetail | None = None
    spender: TransactionApprovalRoleDetail | None = None
    approver: TransactionApprovalRoleDetail | None = None
    transaction_id: str | None = None
    request_id: str | None = None
    cobo_id: str | None = None


class TransactionApprovalTemplateRequest(BaseModel):
    template_key: str
    template_version: str | None = None


class TransactionApprovalTemplateItem(BaseModel):
    business_key: str
    template_text: str
    version: str
