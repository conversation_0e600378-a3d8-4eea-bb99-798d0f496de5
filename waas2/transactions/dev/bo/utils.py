from typing import List

from pydantic import BaseModel


class FilterByWalletScopeRequest(BaseModel):
    transaction_ids: List | None = None
    request_ids: List | None = None
    cobo_ids: List | None = None

    @classmethod
    def populate(
        cls,
        transaction_ids: List | None = None,
        request_ids: List | None = None,
        cobo_ids: List | None = None,
    ) -> "FilterByWalletScopeRequest":
        return cls(
            transaction_ids=transaction_ids, request_ids=request_ids, cobo_ids=cobo_ids
        )


class FilterByWalletScopeResponse(BaseModel):
    transaction_id: str
    request_id: str | None = None
    cobo_id: str | None = None
