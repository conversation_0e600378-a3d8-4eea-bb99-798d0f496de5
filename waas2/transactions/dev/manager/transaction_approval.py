import logging
import re
from typing import List

from cobo_libs.api.exceptions import BaseApiException
from cobo_libs.cobo_auth.manangers.statement_template import (
    AuthStatementTemplateManager,
)
from pydantic import ValidationError

from custody.custody.services.risk_control import CustodyRiskControlService
from waas2.authentications.universal_access_control.bo.request_context import (
    WaaS2RequestContext,
)
from waas2.transactions.dev.bo.transaction_approval import (
    TransactionApprovalDetailResponse,
    TransactionApprovalTemplateItem,
    TransactionApprovalTemplateRequest,
)
from waas2.transactions.dev.bo.utils import FilterByWalletScopeResponse

logger = logging.getLogger("waas2.transactions.approval")


class TransactionApprovalManager:
    @classmethod
    def list_approval_details(
        cls,
        request_data: List[FilterByWalletScopeResponse],
        request_context: WaaS2RequestContext,
    ) -> List[TransactionApprovalDetailResponse]:
        _ids_map = {
            item.request_id: (item.transaction_id, item.cobo_id)
            for item in request_data
            if item.request_id
        }
        request_ids = list(_ids_map.keys())
        if not request_ids:
            return []

        approval_details = CustodyRiskControlService.list_approval_details(
            org_id=str(request_context.biz_org_id), request_ids=request_ids
        )

        logger.info(f"Fetched approval details: {approval_details}")

        responses = []
        for request_id, approval_detail in approval_details.items():
            try:
                response_item = TransactionApprovalDetailResponse.model_validate(
                    approval_detail
                )
                _ids = _ids_map[request_id]
                response_item.request_id = request_id
                response_item.transaction_id = _ids[0]
                response_item.cobo_id = _ids[1]
                responses.append(response_item)
            except (KeyError, ValueError, ValidationError):
                # Skip invalid data
                continue

        return responses

    @classmethod
    def get_transaction_approval_template(
        cls, request_data: TransactionApprovalTemplateRequest
    ) -> List[TransactionApprovalTemplateItem]:
        template_key = request_data.template_key
        template_version = request_data.template_version

        logger.info(
            f"Fetching templates for template key: {template_key}, version: {template_version}"
        )

        # validate version format (e.g., "1.0.0")
        if template_version:
            version_pattern = r"^\d+\.\d+\.\d+$"
            if not re.match(version_pattern, template_version):
                raise BaseApiException(
                    BaseApiException.ERROR_INVALID_PARAM,
                    f"Invalid version format: {template_version}. Expected format: X.Y.Z (e.g., 1.0.0)",
                )

        # Convert to lowercase for business_key
        template_key_lower = template_key.lower()
        biz_key = f"transaction_{template_key_lower}"

        logger.info(f"Using business key: {biz_key} for fetching templates")
        templates = AuthStatementTemplateManager.get_static_templates_by_business_key(
            business_key=biz_key, version=template_version
        )

        result = []
        for template in templates:
            result.append(TransactionApprovalTemplateItem.model_validate(template))

        return result
