from typing import List

from waas2.authentications.businesses import get_wallet_scopes_from_request
from waas2.authentications.universal_access_control.bo.request_context import (
    WaaS2RequestContext,
)
from waas2.base.dao.wallet_source import WalletSourceDao
from waas2.developers.data import WalletScope
from waas2.transaction_query.bo.transaction_query import (
    QueryTransactionConditions,
    QueryTransactionFilters,
)
from waas2.transactions.dev.bo.utils import (
    FilterByWalletScopeRequest,
    FilterByWalletScopeResponse,
)
from waas2.transactions.mongo.manager.transaction_client import TransactionMongoClient


class TransactionsUtils:
    @staticmethod
    def filter_request_by_wallet_scope(
        request,
        request_info: FilterByWalletScopeRequest,
        request_context: WaaS2RequestContext,
    ) -> List[FilterByWalletScopeResponse]:
        """
        按照钱包范围过滤请求交易信息，只返回在允许的钱包范围内的请求交易信息。
        """
        result = TransactionMongoClient.query_transactions(
            QueryTransactionConditions(
                org_id=str(request_context.biz_org_id),
                filters=QueryTransactionFilters(
                    uuids=request_info.transaction_ids,
                    cobo_ids=request_info.cobo_ids,
                    request_ids=request_info.request_ids,
                ),
            )
        )
        if not result:
            return []

        allowed_wallet_scopes = get_wallet_scopes_from_request(request)
        filter_request_info = []
        wallet_id_maps = {}
        for record in result.records:
            wallet_id = record.wallet.id
            if wallet_id not in wallet_id_maps:
                wallet_id_maps[wallet_id] = []
            wallet_id_maps[wallet_id].append(
                FilterByWalletScopeResponse(
                    request_id=record.request_id,
                    transaction_id=record.transaction_id,
                    cobo_id=record.cobo_id,
                )
            )

        for wallet_id, _ in wallet_id_maps.items():
            wallet_instance = WalletSourceDao.get_by_wallet_id(wallet_id)
            if not WalletScope.is_in_scopes(allowed_wallet_scopes, wallet_instance):
                wallet_id_maps.pop(wallet_id, None)

        return filter_request_info
