import json
from typing import List

from custody.custody.dao.withdraw_request import WithdrawRequestDao
from custody.custody.services.risk_control import CustodyRiskControlService
from custody.custody.utils.feature_switch import (
    hit_high_risk_api_switch,
    hit_low_risk_api_switch,
)
from waas2.authentications.universal_access_control.bo.request_context import (
    WaaS2RequestContext,
)
from waas2.transactions.dev.bo.transaction_risk import (
    TransactionRiskDetailResponse,
    TransactionRiskExcludeResponse,
    TransactionRiskPolicy,
    TransactionRiskPolicyDecision,
)
from waas2.transactions.dev.bo.utils import FilterByWalletScopeResponse


class TransactionRiskDetailManager:
    @classmethod
    def list_risk_details(
        cls,
        request: List[FilterByWalletScopeResponse],
        request_context: WaaS2RequestContext,
    ) -> List[TransactionRiskDetailResponse]:
        """
        List transaction risk details based on provided filters.
        """
        org_id = request_context.biz_org_id
        if not hit_low_risk_api_switch(org_id=org_id, api_name="list_risk_details"):
            return []

        request_ids = [item.request_id for item in request if item.request_id]
        if not request_ids:
            return []

        values = WithdrawRequestDao.list_values_by_request_ids(
            org_id=org_id,
            request_ids=request_ids,
            value_keys=["approval_process", "request_id", "risk_message"],
        )

        responses = []
        for item in values:
            approval_process = json.loads(item["approval_process"])
            responses.append(
                TransactionRiskDetailResponse(
                    request_id=item["request_id"],
                    risk_message=item["risk_message"],
                    flow=[
                        TransactionRiskPolicy(
                            policy_uuid=x.get("policy_id", ""),
                            policy_name=x.get("policy_name", ""),
                            version=int(x.get("version", 0)),
                        )
                        for x in approval_process.get("policy_result", [])[0]
                    ],
                    decision=[
                        TransactionRiskPolicyDecision(policy_uuid=x.get("policy_uuid"))
                        for x in approval_process["policy_result"][1]
                    ],
                )
            )

        return responses

    @classmethod
    def exclude_requests(
        cls,
        request: List[FilterByWalletScopeResponse],
        request_context: WaaS2RequestContext,
    ) -> TransactionRiskExcludeResponse:
        """
        Exclude specific transaction risk details based on provided IDs.
        """
        org_id = request_context.biz_org_id
        if not hit_high_risk_api_switch(org_id=org_id, api_name="exclude_requests"):
            return TransactionRiskExcludeResponse(result=False)

        request_ids = [item.request_id for item in request if item.request_id]
        if not request_ids:
            return TransactionRiskExcludeResponse(result=False)

        request_data = []
        for request_id in request_ids:
            request_data.append(
                {
                    "org_id": org_id,
                    "withdrawal_id": request_id,
                }
            )

        CustodyRiskControlService.report_by_new_rc(request_data)

        return TransactionRiskExcludeResponse(result=True)
