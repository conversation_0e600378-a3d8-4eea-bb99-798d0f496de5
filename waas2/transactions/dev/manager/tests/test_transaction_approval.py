import unittest
from unittest.mock import MagicMock, Mock, patch

from waas2.transactions.dev.manager.transaction_approval import (
    TransactionApprovalManager,
)


class TestTransactionApprovalManager(unittest.TestCase):
    def setUp(self):
        self.request_context = Mock()
        self.request_context.biz_org_id = 12345

    def test_list_approval_details_normal(self):
        from waas2.transactions.dev.bo.utils import FilterByWalletScopeResponse

        filter_response = FilterByWalletScopeResponse(
            request_id="req-1", transaction_id="tx-1", cobo_id="cobo-1"
        )
        with patch(
            "waas2.transactions.dev.manager.transaction_approval.CustodyRiskControlService.list_approval_details"
        ) as mock_list_details:
            mock_list_details.return_value = {"req-1": {"field": "value"}}
            with patch(
                "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalDetailResponse.model_validate"
            ) as mock_validate:
                mock_item = MagicMock()
                mock_validate.return_value = mock_item
                result = TransactionApprovalManager.list_approval_details(
                    [filter_response], self.request_context
                )
                self.assertEqual(result, [mock_item])
                mock_list_details.assert_called_once()
                mock_validate.assert_called_once()

    def test_list_approval_details_empty(self):
        result = TransactionApprovalManager.list_approval_details(
            [], self.request_context
        )
        self.assertEqual(result, [])

    def test_list_approval_details_invalid_data(self):
        from waas2.transactions.dev.bo.utils import FilterByWalletScopeResponse

        filter_response = FilterByWalletScopeResponse(
            request_id="req-1", transaction_id="tx-1", cobo_id="cobo-1"
        )
        with patch(
            "waas2.transactions.dev.manager.transaction_approval.CustodyRiskControlService.list_approval_details"
        ) as mock_list_details:
            mock_list_details.return_value = {"req-1": {"field": "value"}}
            with patch(
                "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalDetailResponse.model_validate"
            ) as mock_validate:
                mock_validate.side_effect = ValueError("invalid")
                result = TransactionApprovalManager.list_approval_details(
                    [filter_response], self.request_context
                )
                self.assertEqual(result, [])

    def test_get_transaction_approval_template_normal(self):
        from waas2.transactions.dev.bo.transaction_approval import (
            TransactionApprovalTemplateRequest,
        )

        request_data = TransactionApprovalTemplateRequest(
            template_key="Withdrawal", template_version="1.0.0"
        )
        mock_templates = [
            {
                "business_key": "transaction_withdrawal",
                "template_text": "Template text 1",
                "version": "1.0.0",
            },
            {
                "business_key": "transaction_withdrawal",
                "template_text": "Template text 2",
                "version": "1.0.0",
            },
        ]
        with patch(
            "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
        ) as mock_get_templates:
            mock_get_templates.return_value = mock_templates
            result = TransactionApprovalManager.get_transaction_approval_template(
                request_data
            )
            self.assertIsInstance(result, list)
            self.assertEqual(len(result), 2)
            self.assertEqual(result[0].business_key, "transaction_withdrawal")
            self.assertEqual(result[0].template_text, "Template text 1")
            self.assertEqual(result[0].version, "1.0.0")
            mock_get_templates.assert_called_once_with(
                business_key="transaction_withdrawal", version="1.0.0"
            )

    def test_get_transaction_approval_template_empty(self):
        from waas2.transactions.dev.bo.transaction_approval import (
            TransactionApprovalTemplateRequest,
        )

        request_data = TransactionApprovalTemplateRequest(
            template_key="Withdrawal", template_version="1.0.0"
        )
        with patch(
            "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
        ) as mock_get_templates:
            mock_get_templates.return_value = []
            result = TransactionApprovalManager.get_transaction_approval_template(
                request_data
            )
            self.assertIsInstance(result, list)
            self.assertEqual(len(result), 0)

    def test_get_transaction_approval_template_without_version(self):
        from waas2.transactions.dev.bo.transaction_approval import (
            TransactionApprovalTemplateRequest,
        )

        request_data = TransactionApprovalTemplateRequest(
            template_key="Withdrawal", template_version=None
        )
        mock_templates = [
            {
                "business_key": "transaction_withdrawal",
                "template_text": "Template text",
                "version": "1.0.0",
            }
        ]
        with patch(
            "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
        ) as mock_get_templates:
            mock_get_templates.return_value = mock_templates
            result = TransactionApprovalManager.get_transaction_approval_template(
                request_data
            )
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0].version, "1.0.0")
            mock_get_templates.assert_called_once_with(
                business_key="transaction_withdrawal", version=None
            )

    def test_get_transaction_approval_template_uppercase_key(self):
        from waas2.transactions.dev.bo.transaction_approval import (
            TransactionApprovalTemplateRequest,
        )

        request_data = TransactionApprovalTemplateRequest(
            template_key="Withdrawal", template_version="1.0.0"
        )
        with patch(
            "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
        ) as mock_get_templates:
            mock_get_templates.return_value = []
            TransactionApprovalManager.get_transaction_approval_template(request_data)
            mock_get_templates.assert_called_once_with(
                business_key="transaction_withdrawal", version="1.0.0"
            )

    def test_get_transaction_approval_template_invalid_version_format(self):
        from cobo_libs.api.exceptions import BaseApiException

        from waas2.transactions.dev.bo.transaction_approval import (
            TransactionApprovalTemplateRequest,
        )

        request_data = TransactionApprovalTemplateRequest(
            template_key="Withdrawal", template_version="invalid_version"
        )
        with self.assertRaises(BaseApiException) as context:
            TransactionApprovalManager.get_transaction_approval_template(request_data)
        self.assertEqual(
            context.exception.error_code, BaseApiException.ERROR_INVALID_PARAM
        )
        self.assertIn(
            "Invalid version format: invalid_version. Expected format: X.Y.Z (e.g., 1.0.0)",
            str(context.exception),
        )

    def test_get_transaction_approval_template_valid_version_formats(self):
        from waas2.transactions.dev.bo.transaction_approval import (
            TransactionApprovalTemplateRequest,
        )

        valid_versions = ["1.0.0", "2.1.3", "10.20.30"]
        for version in valid_versions:
            request_data = TransactionApprovalTemplateRequest(
                template_key="Withdrawal", template_version=version
            )
            with patch(
                "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
            ) as mock_get_templates:
                mock_get_templates.return_value = []
                TransactionApprovalManager.get_transaction_approval_template(
                    request_data
                )
                mock_get_templates.assert_called_once_with(
                    business_key="transaction_withdrawal", version=version
                )

    def test_get_transaction_approval_template_special_characters_in_key(self):
        from waas2.transactions.dev.bo.transaction_approval import (
            TransactionApprovalTemplateRequest,
        )

        request_data = TransactionApprovalTemplateRequest(
            template_key="API-Key-Generation", template_version="1.0.0"
        )
        with patch(
            "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
        ) as mock_get_templates:
            mock_get_templates.return_value = []
            TransactionApprovalManager.get_transaction_approval_template(request_data)
            mock_get_templates.assert_called_once_with(
                business_key="transaction_api-key-generation", version="1.0.0"
            )


if __name__ == "__main__":
    unittest.main()
