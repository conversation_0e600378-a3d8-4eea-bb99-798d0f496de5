from textwrap import dedent
from typing import List

from cobo_libs.api.decorator.drf import cobo_extend_schema

from waas2.authentications.universal_access_control.decorators.check_permission_scope import (
    check_permission_scope,
)
from waas2.authentications.universal_access_control.permissions.operations import (
    PermissionOperation,
)
from waas2.authentications.universal_access_control.permissions.resources import (
    PermissionResource,
)
from waas2.base.schema.tags import SwaggerTags
from waas2.devapi.uac_base_view2 import UacBaseAPIKeyAPIViewV2
from waas2.transactions.dev.bo.transaction_risk import (
    TransactionRiskDetailResponse,
    TransactionRiskExcludeResponse,
    TransactionRiskRequest,
)
from waas2.transactions.dev.bo.utils import FilterByWalletScopeRequest
from waas2.transactions.dev.manager.transaction_risk import TransactionRiskDetailManager
from waas2.transactions.dev.manager.utils import TransactionsUtils


class TransactionRiskDetailsApi(UacBaseAPIKeyAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.TRANSACTION,
        permission_operation=PermissionOperation.READ,
    )
    @cobo_extend_schema(
        summary="query transaction risk details",
        description=dedent(
            """
        Query the risk details of transactions by their request IDs.
        """
        ),
        tags=SwaggerTags.DEVAPI_TRANSACTIONS,
        parameter=TransactionRiskRequest,
        responses={200: List[TransactionRiskDetailResponse]},
    )
    def get(self, request, *args, **kwargs):
        request_data: TransactionRiskRequest = request.validated_data
        request_context = request.request_context

        request_ids = (
            request_data.request_ids.split(",") if request_data.request_ids else None
        )
        filter_responses = TransactionsUtils.filter_request_by_wallet_scope(
            request=request,
            request_info=FilterByWalletScopeRequest.populate(request_ids=request_ids),
            request_context=request_context,
        )

        return TransactionRiskDetailManager.list_risk_details(
            request=filter_responses,
            request_context=request_context,
        )


class TransactionRiskExcludeApi(UacBaseAPIKeyAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.TRANSACTION,
        permission_operation=PermissionOperation.MANAGE,
    )
    @cobo_extend_schema(
        summary="exclude transaction in calc total_amount",
        description=dedent(
            """
        Exclude specific transaction in calc total_amount based on provided request IDs.
        """
        ),
        tags=SwaggerTags.TransactionApproval,
        parameter=TransactionRiskRequest,
        responses={201: TransactionRiskExcludeResponse},
    )
    def put(self, request, *args, **kwargs):
        request_data: TransactionRiskRequest = request.validated_data
        request_context = request.request_context

        request_ids = (
            request_data.request_ids.split(",") if request_data.request_ids else None
        )
        filter_responses = TransactionsUtils.filter_request_by_wallet_scope(
            request=request,
            request_info=FilterByWalletScopeRequest.populate(request_ids=request_ids),
            request_context=request_context,
        )

        return TransactionRiskDetailManager.exclude_requests(
            request=filter_responses,
            request_context=request_context,
        )
