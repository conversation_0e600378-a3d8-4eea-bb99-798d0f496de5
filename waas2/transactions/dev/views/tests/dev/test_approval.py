from unittest.mock import MagicMock, patch

from waas2.base.views.tests.dev.test_base import TestBase
from waas2.transactions.dev.bo.transaction_approval import (
    TransactionApprovalDetailResponse,
    TransactionApprovalRoleDetail,
    TransactionApprovalShowInfo,
    TransactionApprovalUserDetail,
)


class TestTransactionApprovalDetailsApi(TestBase):
    """测试交易审批详情API"""

    @patch(
        "waas2.transactions.dev.manager.utils.TransactionsUtils.filter_request_by_wallet_scope"
    )
    @patch(
        "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalManager.list_approval_details"
    )
    def test_get_approval_details_with_transaction_ids(
        self, mock_list_details, mock_filter_scope
    ):
        mock_user_detail = TransactionApprovalUserDetail(
            user_email="<EMAIL>",
            pubkey="pubkey1",
            signature="sig1",
            statement_uuid="uuid1",
            result=1,
            created_time=1234567890,
            template_version="1.0.0",
            header_title="Test Header",
            show_info=TransactionApprovalShowInfo(
                org_name="test_org",
                wallet_name="Test Wallet",
                environment="SANDBOX",
                from_address_label="",
                to_address_label="",
            ),
            is_for_sign=False,
        )
        mock_response = [
            TransactionApprovalDetailResponse(
                broker_user=None,
                spender=None,
                approver=TransactionApprovalRoleDetail(
                    result=1,
                    threshold=2,
                    user_details=[mock_user_detail],
                ),
                request_id="req-1",
                transaction_id="tx-1",
                cobo_id="cobo-1",
            )
        ]
        mock_filter_scope.return_value = [MagicMock()]
        mock_list_details.return_value = mock_response
        path = "/v2/transactions/approval/details"
        params = {"transaction_ids": "tx-1, tx-2"}
        headers = self.get_auth_headers(path, params=params)
        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )
        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["request_id"], "req-1")
        self.assertEqual(data[0]["transaction_id"], "tx-1")
        self.assertEqual(data[0]["cobo_id"], "cobo-1")
        mock_filter_scope.assert_called_once()
        mock_list_details.assert_called_once()

    @patch(
        "waas2.transactions.dev.manager.utils.TransactionsUtils.filter_request_by_wallet_scope"
    )
    @patch(
        "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalManager.list_approval_details"
    )
    def test_get_approval_details_with_request_ids(
        self, mock_list_details, mock_filter_scope
    ):
        mock_user_detail = TransactionApprovalUserDetail(
            user_email="<EMAIL>",
            pubkey="pubkey1",
            signature="sig1",
            statement_uuid="uuid1",
            result=1,
            created_time=1234567890,
            template_version="1.0.0",
            header_title="Test Header",
            show_info=TransactionApprovalShowInfo(
                org_name="test_org",
                wallet_name="Test Wallet",
                environment="SANDBOX",
                from_address_label="",
                to_address_label="",
            ),
            is_for_sign=False,
        )
        mock_response = [
            TransactionApprovalDetailResponse(
                broker_user=None,
                spender=None,
                approver=TransactionApprovalRoleDetail(
                    result=1,
                    threshold=2,
                    user_details=[mock_user_detail],
                ),
                request_id="req-1",
                transaction_id="tx-1",
                cobo_id="cobo-1",
            )
        ]
        mock_filter_scope.return_value = [MagicMock()]
        mock_list_details.return_value = mock_response
        path = "/v2/transactions/approval/details"
        params = {"request_ids": "req-1, req-2"}
        headers = self.get_auth_headers(path, params=params)
        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )
        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["request_id"], "req-1")
        self.assertEqual(data[0]["transaction_id"], "tx-1")
        self.assertEqual(data[0]["cobo_id"], "cobo-1")
        mock_filter_scope.assert_called_once()
        mock_list_details.assert_called_once()

    @patch(
        "waas2.transactions.dev.manager.utils.TransactionsUtils.filter_request_by_wallet_scope"
    )
    @patch(
        "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalManager.list_approval_details"
    )
    def test_get_approval_details_with_cobo_ids(
        self, mock_list_details, mock_filter_scope
    ):
        mock_user_detail = TransactionApprovalUserDetail(
            user_email="<EMAIL>",
            pubkey="pubkey1",
            signature="sig1",
            statement_uuid="uuid1",
            result=1,
            created_time=1234567890,
            template_version="1.0.0",
            header_title="Test Header",
            show_info=TransactionApprovalShowInfo(
                org_name="test_org",
                wallet_name="Test Wallet",
                environment="SANDBOX",
                from_address_label="",
                to_address_label="",
            ),
            is_for_sign=False,
        )
        mock_response = [
            TransactionApprovalDetailResponse(
                broker_user=None,
                spender=None,
                approver=TransactionApprovalRoleDetail(
                    result=1,
                    threshold=2,
                    user_details=[mock_user_detail],
                ),
                request_id="req-1",
                transaction_id="tx-1",
                cobo_id="cobo-1",
            )
        ]
        mock_filter_scope.return_value = [MagicMock()]
        mock_list_details.return_value = mock_response
        path = "/v2/transactions/approval/details"
        params = {"cobo_ids": "cobo-1, cobo-2"}
        headers = self.get_auth_headers(path, params=params)
        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )
        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["request_id"], "req-1")
        self.assertEqual(data[0]["transaction_id"], "tx-1")
        self.assertEqual(data[0]["cobo_id"], "cobo-1")
        mock_filter_scope.assert_called_once()
        mock_list_details.assert_called_once()

    @patch(
        "waas2.transactions.dev.manager.utils.TransactionsUtils.filter_request_by_wallet_scope"
    )
    @patch(
        "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalManager.list_approval_details"
    )
    def test_get_approval_details_no_params(self, mock_list_details, mock_filter_scope):
        """测试没有提供任何参数时返回错误"""
        from cobo_libs.api.exceptions import BaseApiException

        mock_list_details.side_effect = BaseApiException(
            BaseApiException.ERROR_INVALID_PARAM,
            "At least one of transaction_ids or request_ids or cobo_ids must be provided.",
        )
        mock_filter_scope.return_value = [MagicMock()]

        path = "/v2/transactions/approval/details"
        params = {}
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(400, ret.status_code)
        self.assertIn(
            "At least one of transaction_ids or request_ids or cobo_ids must be provided",
            ret.json()["error_message"],
        )

    @patch(
        "waas2.transactions.dev.manager.utils.TransactionsUtils.filter_request_by_wallet_scope"
    )
    @patch(
        "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalManager.list_approval_details"
    )
    def test_get_approval_details_empty_result(
        self, mock_list_details, mock_filter_scope
    ):
        mock_filter_scope.return_value = [MagicMock()]
        mock_list_details.return_value = []
        path = "/v2/transactions/approval/details"
        params = {"transaction_ids": "tx-1"}
        headers = self.get_auth_headers(path, params=params)
        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )
        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 0)
        mock_filter_scope.assert_called_once()
        mock_list_details.assert_called_once()

    @patch(
        "waas2.transactions.dev.manager.utils.TransactionsUtils.filter_request_by_wallet_scope"
    )
    @patch(
        "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalManager.list_approval_details"
    )
    def test_get_approval_details_with_complete_data(
        self, mock_list_details, mock_filter_scope
    ):
        """测试完整的审批详情数据"""
        mock_filter_scope.return_value = [MagicMock()]
        # 准备完整的测试数据
        mock_user_detail = TransactionApprovalUserDetail(
            user_email="<EMAIL>",
            pubkey="dce5743d58cd0fbd5dcbca1faa2ee184c9c0b0642f97160a9aa063bbba5ba726634abd1571d1c704256d083fbe4e800bce90f069ccf42a4123b67d5f2b164d09",
            signature="54bf4cabe5b01ca65aec3f4b8550045e6bd41d86b61f93082ffc5eaee72506d3a6cc8bb953155650019008fa094b4bf6796a4061aa86aa692ffb5b8f87316492",
            statement_uuid="f70ee86d-a127-47f3-a347-85a92081824f",
            result=2,
            created_time=1750744087,
            template_version="1.0.0",
            header_title="xxxx",
            show_info=TransactionApprovalShowInfo(
                org_name="portal_xj_099",
                wallet_name="Default wallet 08996",
                environment="SANDBOX",
                from_address_label="",
                to_address_label="",
            ),
            is_for_sign=False,
        )

        mock_response = [
            TransactionApprovalDetailResponse(
                broker_user=None,
                spender=None,
                approver=TransactionApprovalRoleDetail(
                    result=1,
                    threshold=2,
                    user_details=[mock_user_detail],
                ),
                request_id="req-1",
                transaction_id="tx-1",
                cobo_id="cobo-1",
            )
        ]
        mock_list_details.return_value = mock_response

        path = "/v2/transactions/approval/details"
        params = {
            "transaction_ids": "tx-1",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["request_id"], "req-1")
        self.assertEqual(data[0]["transaction_id"], "tx-1")
        self.assertEqual(data[0]["cobo_id"], "cobo-1")
        self.assertIsNotNone(data[0]["approver"])
        self.assertEqual(data[0]["approver"]["result"], 1)
        self.assertEqual(data[0]["approver"]["threshold"], 2)
        self.assertEqual(len(data[0]["approver"]["user_details"]), 1)
        self.assertEqual(
            data[0]["approver"]["user_details"][0]["user_email"],
            "<EMAIL>",
        )
        mock_list_details.assert_called_once()

    @patch(
        "waas2.transactions.dev.manager.utils.TransactionsUtils.filter_request_by_wallet_scope"
    )
    @patch(
        "waas2.transactions.dev.manager.transaction_approval.TransactionApprovalManager.list_approval_details"
    )
    def test_get_approval_details_multiple_results(
        self, mock_list_details, mock_filter_scope
    ):
        """测试返回多个审批详情"""
        mock_filter_scope.return_value = [MagicMock()]
        # 准备测试数据
        mock_user_detail_1 = TransactionApprovalUserDetail(
            user_email="<EMAIL>",
            pubkey="pubkey1",
            signature="sig1",
            statement_uuid="uuid1",
            result=1,
            created_time=1234567890,
            template_version="1.0.0",
            header_title="Test Header 1",
            show_info=TransactionApprovalShowInfo(
                org_name="test_org",
                wallet_name="Test Wallet 1",
                environment="SANDBOX",
                from_address_label="",
                to_address_label="",
            ),
            is_for_sign=False,
        )

        mock_user_detail_2 = TransactionApprovalUserDetail(
            user_email="<EMAIL>",
            pubkey="pubkey2",
            signature="sig2",
            statement_uuid="uuid2",
            result=0,
            created_time=1234567891,
            template_version="1.0.0",
            header_title="Test Header 2",
            show_info=TransactionApprovalShowInfo(
                org_name="test_org",
                wallet_name="Test Wallet 2",
                environment="SANDBOX",
                from_address_label="",
                to_address_label="",
            ),
            is_for_sign=False,
        )

        mock_response = [
            TransactionApprovalDetailResponse(
                broker_user=None,
                spender=None,
                approver=TransactionApprovalRoleDetail(
                    result=1,
                    threshold=2,
                    user_details=[mock_user_detail_1],
                ),
                request_id="req-1",
                transaction_id="tx-1",
                cobo_id="cobo-1",
            ),
            TransactionApprovalDetailResponse(
                broker_user=None,
                spender=None,
                approver=TransactionApprovalRoleDetail(
                    result=0,
                    threshold=1,
                    user_details=[mock_user_detail_2],
                ),
                request_id="req-2",
                transaction_id="tx-2",
                cobo_id="cobo-2",
            ),
        ]
        mock_list_details.return_value = mock_response

        path = "/v2/transactions/approval/details"
        params = {
            "transaction_ids": "tx-1,tx-2",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 2)

        # 验证第一个结果
        self.assertEqual(data[0]["request_id"], "req-1")
        self.assertEqual(data[0]["transaction_id"], "tx-1")
        self.assertEqual(data[0]["cobo_id"], "cobo-1")
        self.assertIsNotNone(data[0]["approver"])
        self.assertEqual(data[0]["approver"]["result"], 1)
        self.assertEqual(data[0]["approver"]["threshold"], 2)

        # 验证第二个结果
        self.assertEqual(data[1]["request_id"], "req-2")
        self.assertEqual(data[1]["transaction_id"], "tx-2")
        self.assertEqual(data[1]["cobo_id"], "cobo-2")
        self.assertIsNotNone(data[1]["approver"])
        self.assertEqual(data[1]["approver"]["result"], 0)
        self.assertEqual(data[1]["approver"]["threshold"], 1)

        mock_list_details.assert_called_once()


class TestTransactionApprovalTemplateApi(TestBase):
    """测试交易审批模板API"""

    @patch(
        "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
    )
    def test_get_approval_template_with_version(self, mock_get_templates):
        """测试获取指定版本的审批模板"""
        # 准备测试数据
        mock_templates = [
            {
                "business_key": "transaction_withdrawal",
                "template_text": "Template text 1",
                "version": "1.0.0",
            },
            {
                "business_key": "transaction_withdrawal",
                "template_text": "Template text 2",
                "version": "1.0.0",
            },
        ]
        mock_get_templates.return_value = mock_templates

        path = "/v2/transactions/templates"
        params = {
            "template_key": "Withdrawal",
            "template_version": "1.0.0",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0]["business_key"], "transaction_withdrawal")
        self.assertEqual(data[0]["template_text"], "Template text 1")
        self.assertEqual(data[0]["version"], "1.0.0")
        mock_get_templates.assert_called_once_with(
            business_key="transaction_withdrawal", version="1.0.0"
        )

    @patch(
        "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
    )
    def test_get_approval_template_without_version(self, mock_get_templates):
        """测试不指定版本获取审批模板"""
        # 准备测试数据
        mock_templates = [
            {
                "business_key": "transaction_withdrawal",
                "template_text": "Template text",
                "version": "1.0.0",
            }
        ]
        mock_get_templates.return_value = mock_templates

        path = "/v2/transactions/templates"
        params = {
            "template_key": "Withdrawal",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["version"], "1.0.0")
        mock_get_templates.assert_called_once_with(
            business_key="transaction_withdrawal", version=None
        )

    @patch(
        "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
    )
    def test_get_approval_template_uppercase_key(self, mock_get_templates):
        """测试模板键为大写时的处理"""
        mock_get_templates.return_value = []

        path = "/v2/transactions/templates"
        params = {
            "template_key": "Withdrawal",
            "template_version": "1.0.0",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(200, ret.status_code)
        mock_get_templates.assert_called_once_with(
            business_key="transaction_withdrawal", version="1.0.0"
        )

    @patch(
        "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
    )
    def test_get_approval_template_empty_result(self, mock_get_templates):
        """测试获取审批模板返回空结果"""
        mock_get_templates.return_value = []

        path = "/v2/transactions/templates"
        params = {
            "template_key": "Withdrawal",
            "template_version": "1.0.0",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertEqual(len(data), 0)

    @patch(
        "waas2.transactions.dev.manager.transaction_approval.AuthStatementTemplateManager.get_static_templates_by_business_key"
    )
    def test_get_approval_template_different_keys(self, mock_get_templates):
        """测试不同模板键的审批模板"""
        mock_templates = [
            {
                "business_key": "transaction_deposit",
                "template_text": "Deposit template",
                "version": "1.0.0",
            }
        ]
        mock_get_templates.return_value = mock_templates

        path = "/v2/transactions/templates"
        params = {
            "template_key": "Deposit",
            "template_version": "1.0.0",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(200, ret.status_code)
        data = ret.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["business_key"], "transaction_deposit")
        self.assertEqual(data[0]["template_text"], "Deposit template")
        mock_get_templates.assert_called_once_with(
            business_key="transaction_deposit", version="1.0.0"
        )

    def test_get_approval_template_invalid_version_format(self):
        """测试无效版本格式时返回错误"""
        path = "/v2/transactions/templates"
        params = {
            "template_key": "Withdrawal",
            "template_version": "invalid_version",
        }
        headers = self.get_auth_headers(path, params=params)

        ret = self.client.get(
            path, data=params, content_type="application/json", **headers
        )

        self.assertEqual(400, ret.status_code)
        self.assertIn(
            "Invalid version format: invalid_version. Expected format: X.Y.Z (e.g., 1.0.0)",
            ret.json()["error_message"],
        )
