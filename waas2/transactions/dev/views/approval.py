import logging
from textwrap import dedent
from typing import List

from cobo_libs.api.decorator.drf import cobo_extend_schema
from cobo_libs.api.exceptions import BaseApiException

from waas2.authentications.universal_access_control.decorators.check_permission_scope import (
    check_permission_scope,
)
from waas2.authentications.universal_access_control.permissions.operations import (
    PermissionOperation,
)
from waas2.authentications.universal_access_control.permissions.resources import (
    PermissionResource,
)
from waas2.base.schema.tags import SwaggerTags
from waas2.devapi.uac_base_view2 import UacBaseAPIKeyAPIViewV2
from waas2.transactions.dev.bo.transaction_approval import (
    TransactionApprovalDetailRequest,
    TransactionApprovalDetailResponse,
    TransactionApprovalTemplateItem,
    TransactionApprovalTemplateRequest,
)
from waas2.transactions.dev.bo.utils import FilterByWalletScopeRequest
from waas2.transactions.dev.manager.transaction_approval import (
    TransactionApprovalManager,
)
from waas2.transactions.dev.manager.utils import TransactionsUtils

logger = logging.getLogger("waas2.transactions.approval")


class TransactionApprovalDetailsApi(UacBaseAPIKeyAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.TRANSACTION,
        permission_operation=PermissionOperation.READ,
    )
    @cobo_extend_schema(
        summary="query transaction approval details",
        description=dedent(
            """
        Query the details of a transaction approval by its transaction ID.
        """
        ),
        tags=SwaggerTags.TransactionApproval,
        parameter=TransactionApprovalDetailRequest,
        responses={200: List[TransactionApprovalDetailResponse]},
    )
    def get(self, request, *args, **kwargs):
        request_data: TransactionApprovalDetailRequest = request.validated_data
        request_context = request.request_context

        if (
            not request_data.transaction_ids
            and not request_data.request_ids
            and not request_data.cobo_ids
        ):
            raise BaseApiException(
                BaseApiException.ERROR_INVALID_PARAM,
                "At least one of transaction_ids or request_ids or cobo_ids must be provided.",
            )

        logger.info(f"Listing approval details with request data: {request_data}")

        # Check that only one parameter is provided
        provided_params = [
            bool(request_data.transaction_ids),
            bool(request_data.request_ids),
            bool(request_data.cobo_ids),
        ]

        if sum(provided_params) > 1:
            raise BaseApiException(
                BaseApiException.ERROR_INVALID_PARAM,
                "Only one of transaction_ids, request_ids, or cobo_ids should be provided.",
            )

        request_ids = (
            request_data.request_ids.split(",") if request_data.request_ids else None
        )
        transaction_ids = (
            request_data.transaction_ids.split(",")
            if request_data.transaction_ids
            else None
        )
        cobo_ids = request_data.cobo_ids.split(",") if request_data.cobo_ids else None
        filter_responses = TransactionsUtils.filter_request_by_wallet_scope(
            request=request,
            request_info=FilterByWalletScopeRequest.populate(
                request_ids=request_ids,
                transaction_ids=transaction_ids,
                cobo_ids=cobo_ids,
            ),
            request_context=request_context,
        )

        return TransactionApprovalManager.list_approval_details(
            request_data=filter_responses,
            request_context=request_context,
        )


class TransactionApprovalTemplateApi(UacBaseAPIKeyAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.TRANSACTION,
        permission_operation=PermissionOperation.READ,
    )
    @cobo_extend_schema(
        summary="query transaction approval template details",
        description=dedent(
            """
        Query the details of a transaction approval template by its transaction type，
        template version is optional.
        """
        ),
        tags=SwaggerTags.TransactionApproval,
        parameter=TransactionApprovalTemplateRequest,
        responses={200: List[TransactionApprovalTemplateItem]},
    )
    def get(self, request, *args, **kwargs):
        request_data: TransactionApprovalTemplateRequest = request.validated_data

        return TransactionApprovalManager.get_transaction_approval_template(
            request_data=request_data
        )
