from typing import Any, Union

from bc_waas2 import (
    EOABatchTxFeeReqRequest,
    EOABatchTxFeeResp,
    EOACustodyMaxTransferValueReqRequest,
    EOACustodyMaxTransferValueReqRequestDestination,
    EOACustodyMaxTransferValueReqRequestFee,
    EOACustodyMaxTransferValueReqRequestSource,
    EOAMaxTransferValueReqRequest,
    EOAMPCMaxTransferValueReqRequest,
    EOAPerTxFeeReqRequest,
    EOAPerTxFeeReqRequestDestination,
    EOAPerTxFeeReqRequestFee,
    EOAPerTxFeeReqRequestSource,
    EOATxFeeRespV2,
    SubTypeEnum,
)
from bc_waas2.models import (
    EOARBFPerTxFeeReqRequest,
    EOARBFPerTxFeeReqRequestFee,
    EOARBFSourceUTXORequest,
    EOARBFTxFeeReqRequest,
    EOATxFeeReqRequest,
    FeeTypeEnum,
    RbfTypeEnum,
    SourceTypeEnum,
    WaaS2EVMEIP1559EstimatedFee,
)

from custody.custody.dao.custody_wallet import CustodyWalletDao
from custody.custody.data.enums import FeeModelType
from custody.custody.data.objects import (
    EOAFee,
    EVMEIP1559EstimatedFee,
    EVMLegacyEstimatedFee,
    FILEstimatedFee,
    FixedEstimatedFee,
    MPCEstimateFeeResponse,
    SOLEstimatedFee,
    UTXOEstimatedFee,
)
from custody.custody.managers.blockchain import BlockchainMPCClientManager
from custody.custody.managers.blockchain_v2 import (
    BlockchainClientManagerV2Base,
    BlockchainClientManagerV2EOACustody,
    BlockchainClientManagerV2EOAMpc,
)
from custody.custody.models.custody_wallet import CustodyWallet
from custody.web3.dao.transaction_request import RBFRelationDao, TransactionRequestDao
from waas2.base.bo.transactions import FeeFILParamBO, FeeSOLParamBO, TransactionCreateBO
from waas2.base.managers.blockchain.entity import Fee, FeeDetail, MaxTransferableAmount
from waas2.base.managers.blockchain.interface import (
    EstimateRBFTxFeeDetailParams,
    EstimateRBFTxFeeParams,
    EstimateTxFeeDetailParams,
    EstimateTxFeeParams,
    EstimateTxFeeResult,
    GetMaxTransferableAmountParams,
    IBlockChainFeeEstimate,
)


class BlockchainManager(IBlockChainFeeEstimate):
    client: BlockchainClientManagerV2Base

    @classmethod
    def estimate_tx_fee(cls, params: EstimateTxFeeParams) -> EstimateTxFeeResult:
        fee_result = EstimateTxFeeResult(fee_model_types=params.fee_types)
        fee_result.slow = Fee()
        fee_result.fast = Fee()
        fee_result.standard = Fee()
        for fee_type in params.get_fee_types():
            resp = cls.client.estimate_tx_fee(
                req=EOATxFeeReqRequest(
                    fee_type=fee_type,
                    asset_coin=params.asset_coin,
                    chain_coin=params.chain_coin,
                    source=EOAPerTxFeeReqRequestSource(
                        params.account.source if params.account else params.utxo.source
                    ),
                    destination=EOAPerTxFeeReqRequestDestination(
                        params.account.destination
                        if params.account
                        else params.utxo.destination
                    ),
                    enable_validation=False,
                )
            )

            if fee_type == FeeTypeEnum.FEE_MODEL_UTXO:
                fee_result.slow.fee_utxo = resp.slow.actual_instance
                fee_result.standard.fee_utxo = resp.standard.actual_instance
                fee_result.fast.fee_utxo = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_EVM_LEGACY:
                fee_result.slow.fee_evm_legacy = resp.slow.actual_instance
                fee_result.standard.fee_evm_legacy = resp.standard.actual_instance
                fee_result.fast.fee_evm_legacy = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_EVM_EIP_1559:
                fee_result.slow.fee_evm_eip1559 = resp.slow.actual_instance
                fee_result.standard.fee_evm_eip1559 = resp.standard.actual_instance
                fee_result.fast.fee_evm_eip1559 = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_FIXED:
                fee_result.slow.fee_fixed = resp.slow.actual_instance
                fee_result.standard.fee_fixed = resp.standard.actual_instance
                fee_result.fast.fee_fixed = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_FIL:
                fee_result.slow.fee_fil = resp.slow.actual_instance
                fee_result.standard.fee_fil = resp.standard.actual_instance
                fee_result.fast.fee_fil = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_SOLANA:
                fee_result.slow.fee_sol = resp.slow.actual_instance
                fee_result.standard.fee_sol = resp.standard.actual_instance
                fee_result.fast.fee_sol = resp.fast.actual_instance

        return fee_result

    @classmethod
    def estimate_rbf_tx_fee(cls, params: EstimateRBFTxFeeParams) -> EstimateTxFeeResult:
        rbf_type = (
            RbfTypeEnum.RBF_SPEEDUP
            if params.rbf_type == "speedup"
            else RbfTypeEnum.RBF_DROP
        )
        origin_cobo_id = cls.get_rbf_origin_cobo_id(params.origin_cobo_id)
        fee_result = EstimateTxFeeResult(fee_model_types=params.fee_types)
        fee_result.slow = Fee()
        fee_result.fast = Fee()
        fee_result.standard = Fee()
        for fee_type in params.get_fee_types():
            append_source = None
            if fee_type == FeeTypeEnum.FEE_MODEL_UTXO:
                tr = TransactionRequestDao.get_by_cobo_id(origin_cobo_id)
                custody_wallet = CustodyWalletDao.get_custody_wallet_by_id_or_none(
                    tr.custody_wallet_id
                )
                append_source = EOARBFSourceUTXORequest(
                    source_type=SourceTypeEnum.SRC_MODEL_UTXO,
                    wallet_id=str(custody_wallet.remote_wallet_id),
                    address=tr.from_address,
                )
            resp = cls.client.estimate_rbf_tx_fee(
                req=EOARBFTxFeeReqRequest(
                    rbf_type=rbf_type,
                    request_id=origin_cobo_id,
                    append_source=append_source,
                )
            )

            if fee_type == FeeTypeEnum.FEE_MODEL_UTXO:
                fee_result.slow.fee_utxo = resp.slow.actual_instance
                fee_result.standard.fee_utxo = resp.standard.actual_instance
                fee_result.fast.fee_utxo = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_EVM_LEGACY:
                fee_result.slow.fee_evm_legacy = resp.slow.actual_instance
                fee_result.standard.fee_evm_legacy = resp.standard.actual_instance
                fee_result.fast.fee_evm_legacy = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_EVM_EIP_1559:
                fee_result.slow.fee_evm_eip1559 = resp.slow.actual_instance
                fee_result.standard.fee_evm_eip1559 = resp.standard.actual_instance
                fee_result.fast.fee_evm_eip1559 = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_FIXED:
                fee_result.slow.fee_fixed = resp.slow.actual_instance
                fee_result.standard.fee_fixed = resp.standard.actual_instance
                fee_result.fast.fee_fixed = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_FIL:
                fee_result.slow.fee_fil = resp.slow.actual_instance
                fee_result.standard.fee_fil = resp.standard.actual_instance
                fee_result.fast.fee_fil = resp.fast.actual_instance
            elif fee_type == FeeTypeEnum.FEE_MODEL_SOLANA:
                fee_result.slow.fee_sol = resp.slow.actual_instance
                fee_result.standard.fee_sol = resp.standard.actual_instance
                fee_result.fast.fee_sol = resp.fast.actual_instance

        return fee_result

    @classmethod
    def estimate_mpc_rbf_tx_fee(
        cls, wallet: CustodyWallet, params: TransactionCreateBO, fee_type: FeeModelType
    ) -> EstimateTxFeeResult:
        origin_cobo_id = cls.get_rbf_origin_cobo_id(params.origin_cobo_id)
        fee_result = EstimateTxFeeResult(fee_model_types=[fee_type])
        fee_result.slow = Fee()
        fee_result.fast = Fee()
        fee_result.standard = Fee()
        token_info = params.token_info
        fee_rate = None
        gas_limit = None
        if params.legacy:
            fee_rate = params.legacy.gas_price
            gas_limit = params.legacy.gas_limit
        elif params.utxo:
            fee_rate = params.utxo.fee_rate

        resp = BlockchainMPCClientManager.mpc_estimate_fee_by_transaction(
            chain_coin=token_info.chain_id,
            asset_coin=token_info.token_id,
            replaced_request_id=origin_cobo_id,
            from_address=params.from_address,
            from_wallet_id=wallet.remote_wallet_id,
            to_address=params.to_address,
            amount=params.amount,
            fee_rate=fee_rate,
            gas_limit=gas_limit,
            fee_type=FeeModelType.TYPE_EVM_EIP_1559.value
            if fee_type == FeeModelType.TYPE_EVM_EIP_1559
            else None,
        )

        if fee_type == FeeModelType.TYPE_UTXO:
            fee_result.slow.fee_utxo = MPCEstimateFeeResponse.to_utxo(
                resp.low, token_info
            )
            fee_result.standard.fee_utxo = MPCEstimateFeeResponse.to_utxo(
                resp.recommended, token_info
            )
            fee_result.fast.fee_utxo = MPCEstimateFeeResponse.to_utxo(
                resp.high, token_info
            )
        elif fee_type == FeeModelType.TYPE_EVM_LEGACY:
            fee_result.slow.fee_evm_legacy = MPCEstimateFeeResponse.to_legacy(
                resp.low, token_info
            )
            fee_result.standard.fee_evm_legacy = MPCEstimateFeeResponse.to_legacy(
                resp.recommended, token_info
            )
            fee_result.fast.fee_evm_legacy = MPCEstimateFeeResponse.to_legacy(
                resp.high, token_info
            )
        elif fee_type == FeeModelType.TYPE_EVM_EIP_1559:
            fee_result.slow.fee_evm_eip1559 = WaaS2EVMEIP1559EstimatedFee.from_dict(
                resp.fee_model_low
            )
            fee_result.standard.fee_evm_eip1559 = WaaS2EVMEIP1559EstimatedFee.from_dict(
                resp.fee_model_standard
            )
            fee_result.fast.fee_evm_eip1559 = WaaS2EVMEIP1559EstimatedFee.from_dict(
                resp.fee_model_fast
            )
        elif fee_type == FeeModelType.TYPE_FIXED:
            fee_result.slow.fee_fixed = MPCEstimateFeeResponse.to_fixed(
                resp.low, token_info
            )
            fee_result.standard.fee_fixed = MPCEstimateFeeResponse.to_fixed(
                resp.recommended, token_info
            )
            fee_result.fast.fee_fixed = MPCEstimateFeeResponse.to_fixed(
                resp.high, token_info
            )

        return fee_result

    @classmethod
    def estimate_mpc_per_rbf_tx_fee(
        cls, wallet: CustodyWallet, params: TransactionCreateBO, fee_type: FeeModelType
    ) -> FeeDetail:
        origin_cobo_id = cls.get_rbf_origin_cobo_id(params.origin_cobo_id)
        fee_result = EstimateTxFeeResult(fee_model_types=[fee_type])
        fee_result.slow = Fee()
        fee_result.fast = Fee()
        fee_result.standard = Fee()
        token_info = params.token_info
        fee_rate = None
        gas_limit = None
        max_fee = None
        max_priority_fee = None
        if params.legacy:
            fee_rate = params.legacy.gas_price
            gas_limit = params.legacy.gas_limit
        elif params.utxo:
            fee_rate = params.utxo.fee_rate
        elif params.eip1559:
            max_fee = params.eip1559.max_fee
            max_priority_fee = params.eip1559.max_priority_fee

        resp = BlockchainMPCClientManager.mpc_estimate_fee_by_transaction(
            chain_coin=token_info.chain_id,
            asset_coin=token_info.token_id,
            replaced_request_id=origin_cobo_id,
            from_address=params.from_address,
            from_wallet_id=wallet.remote_wallet_id,
            to_address=params.to_address,
            amount=params.amount,
            fee_rate=fee_rate,
            gas_limit=gas_limit,
            fee_type=FeeModelType.TYPE_EVM_EIP_1559.value
            if fee_type == FeeModelType.TYPE_EVM_EIP_1559
            else None,
            max_fee_per_gas=max_fee,
            max_priority_fee_per_gas=max_priority_fee,
        )

        fee_detail = FeeDetail(
            fee_type=fee_type,
            fee_token_id=token_info.fee_token_id,
        )

        if fee_type == FeeModelType.TYPE_EVM_EIP_1559:
            customise_fee = WaaS2EVMEIP1559EstimatedFee.from_dict(
                resp.fee_model_customise
            )
            fee_detail.max_priority_fee_per_gas = customise_fee.max_priority_fee_per_gas
            fee_detail.gas_limit = customise_fee.gas_limit
            fee_detail.max_fee_per_gas = customise_fee.max_fee_per_gas
        elif fee_type == FeeModelType.TYPE_EVM_LEGACY:
            fee_detail.gas_price = str(fee_rate)
            fee_detail.gas_limit = str(int(resp.customise_fee_limit / fee_rate))
        elif fee_type == FeeModelType.TYPE_UTXO:
            fee_detail.fee_rate = fee_rate
            fee_detail.max_fee_amount = str(resp.customise_fee_limit)
        elif fee_type == FeeModelType.TYPE_FIXED:
            fee_detail.max_fee_amount = str(resp.customise_fee_limit)

        return fee_detail

    @classmethod
    def get_rbf_origin_cobo_id(cls, cobo_id: str) -> str:
        origin_cobo_id = cobo_id
        rbf_relation = RBFRelationDao.get_not_failed_relation_by_cobo_id_or_null(
            transaction_cobo_id=origin_cobo_id
        )
        if rbf_relation:
            origin_cobo_id = rbf_relation.origin_cobo_id
        return origin_cobo_id

    @classmethod
    def estimate_per_tx_fee(cls, params: EstimateTxFeeDetailParams) -> FeeDetail:
        per_tx_req = EOAPerTxFeeReqRequest(
            fee_type=params.custom_fee.get_fee_type(),
            asset_coin=params.asset_coin,
            chain_coin=params.chain_coin,
            source=EOAPerTxFeeReqRequestSource(
                params.account.source if params.account else params.utxo.source
            ),
            destination=EOAPerTxFeeReqRequestDestination(
                params.account.destination
                if params.account
                else params.utxo.destination
            ),
            fee=EOAPerTxFeeReqRequestFee(params.custom_fee.to_bc_eoa_request_fee()),
            enable_validation=False,
            prevent_fee_change=True,
        )
        per_tx_response = cls.client.estimate_per_tx_fee(per_tx_req)

        fee_detail = FeeDetail(
            fee_type=params.custom_fee.fee_type,
            fee_token_id=params.custom_fee.fee_token_id,
        )

        actual = per_tx_response.tx_fee.actual_instance
        if params.custom_fee.fee_type == FeeModelType.TYPE_EVM_EIP_1559:
            fee_detail.max_priority_fee_per_gas = actual.max_priority_fee_per_gas
            fee_detail.gas_limit = actual.gas_limit
            fee_detail.max_fee_per_gas = actual.max_fee_per_gas
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_EVM_LEGACY:
            fee_detail.gas_price = actual.gas_price
            fee_detail.gas_limit = actual.gas_limit
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_UTXO:
            fee_detail.fee_rate = actual.fee_rate
            fee_detail.max_fee_amount = actual.max_fee_amount
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_FIXED:
            fee_detail.max_fee_amount = actual.max_fee_amount
        elif params.custom_fee.fee_type == FeeModelType.TYPE_FIL:
            fee_detail.fee_fil = FeeFILParamBO(
                gas_premium=actual.gas_premium,
                gas_fee_cap=actual.gas_fee_cap,
                gas_limit=int(actual.gas_limit),
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_SOL:
            fee_detail.fee_sol = FeeSOLParamBO(
                compute_unit_price=str(actual.compute_unit_price),
                compute_unit_limit=actual.compute_unit_limit,
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)

        return fee_detail

    @classmethod
    def estimate_per_rbf_tx_fee(cls, params: EstimateRBFTxFeeDetailParams) -> FeeDetail:
        origin_cobo_id = cls.get_rbf_origin_cobo_id(params.origin_cobo_id)
        rbf_type = (
            RbfTypeEnum.RBF_SPEEDUP
            if params.rbf_type == "speedup"
            else RbfTypeEnum.RBF_DROP
        )
        per_tx_req = EOARBFPerTxFeeReqRequest(
            rbf_type=rbf_type,
            request_id=origin_cobo_id,
            fee=EOARBFPerTxFeeReqRequestFee(params.custom_fee.to_bc_eoa_request_fee()),
            prevent_fee_change=True,
        )
        per_tx_response = cls.client.estimate_per_rbf_tx_fee(per_tx_req)

        fee_detail = FeeDetail(
            fee_type=params.custom_fee.fee_type,
            fee_token_id=params.custom_fee.fee_token_id,
        )

        actual = per_tx_response.tx_fee.actual_instance
        if params.custom_fee.fee_type == FeeModelType.TYPE_EVM_EIP_1559:
            fee_detail.max_priority_fee_per_gas = actual.max_priority_fee_per_gas
            fee_detail.gas_limit = actual.gas_limit
            fee_detail.max_fee_per_gas = actual.max_fee_per_gas
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_EVM_LEGACY:
            fee_detail.gas_price = actual.gas_price
            fee_detail.gas_limit = actual.gas_limit
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_UTXO:
            fee_detail.fee_rate = actual.fee_rate
            fee_detail.max_fee_amount = actual.max_fee_amount
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_FIXED:
            fee_detail.max_fee_amount = actual.max_fee_amount
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_FIL:
            fee_detail.fee_fil = FeeFILParamBO(
                gas_premium=actual.gas_premium,
                gas_fee_cap=actual.gas_fee_cap,
                gas_limit=int(actual.gas_limit),
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_SOL:
            fee_detail.fee_sol = FeeSOLParamBO(
                compute_unit_price=str(actual.compute_unit_price),
                compute_unit_limit=actual.compute_unit_limit,
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)

        return fee_detail

    @classmethod
    def get_max_transfer_value(
        cls, params: GetMaxTransferableAmountParams
    ) -> MaxTransferableAmount:
        request = EOACustodyMaxTransferValueReqRequest(
            sub_type=params.sub_type,
            chain_coin=params.chain_coin,
            asset_coin=params.asset_coin,
            source=EOACustodyMaxTransferValueReqRequestSource(
                params.account.source if params.account else params.utxo.source
            ),
            destination=EOACustodyMaxTransferValueReqRequestDestination(
                params.account.destination
                if params.account
                else params.utxo.destination
            ),
            fee=EOACustodyMaxTransferValueReqRequestFee(
                params.custom_fee.to_bc_eoa_request_transfer_fee()
            ),
            enable_validation=False,
        )

        if params.sub_type == SubTypeEnum.EOA_SUB_TYPE_MPC:
            request = EOAMPCMaxTransferValueReqRequest(
                sub_type=params.sub_type,
                chain_coin=params.chain_coin,
                asset_coin=params.asset_coin,
                source=EOACustodyMaxTransferValueReqRequestSource(
                    params.account.source if params.account else params.utxo.source
                ),
                destination=EOACustodyMaxTransferValueReqRequestDestination(
                    params.account.destination
                    if params.account
                    else params.utxo.destination
                ),
                fee=EOACustodyMaxTransferValueReqRequestFee(
                    params.custom_fee.to_bc_eoa_request_transfer_fee()
                ),
            )

        res = cls.client.wallets_max_transferable_value_v2(
            wallet_id=str(params.wallet_id),
            req=EOAMaxTransferValueReqRequest(request),
        )

        fee_detail = FeeDetail(
            fee_type=params.custom_fee.fee_type,
            fee_token_id=params.custom_fee.fee_token_id,
        )

        actual = res.fee.actual_instance
        if params.custom_fee.fee_type == FeeModelType.TYPE_EVM_EIP_1559:
            fee_detail.max_priority_fee_per_gas = actual.max_priority_fee_per_gas
            fee_detail.gas_limit = actual.gas_limit
            fee_detail.max_fee_per_gas = actual.max_fee_per_gas
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_EVM_LEGACY:
            fee_detail.gas_price = actual.gas_price
            fee_detail.gas_limit = actual.gas_limit
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_UTXO:
            fee_detail.fee_rate = actual.fee_rate
            fee_detail.max_fee_amount = (
                actual.max_fee_amount if actual.max_fee_amount else "0"
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_FIXED:
            fee_detail.max_fee_amount = (
                actual.max_fee_amount if actual.max_fee_amount else "0"
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_SOL:
            fee_detail.fee_sol = FeeSOLParamBO(
                compute_unit_price=str(actual.compute_unit_price),
                compute_unit_limit=actual.compute_unit_limit,
                rent_amount=actual.rent_amount,
                base_fee=actual.base_fee,
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)
        elif params.custom_fee.fee_type == FeeModelType.TYPE_FIL:
            fee_detail.fee_fil = FeeFILParamBO(
                gas_premium=actual.gas_premium,
                gas_fee_cap=actual.gas_fee_cap,
                gas_limit=actual.gas_limit,
            )
            fee_detail.estimated_fee_amount = int(actual.estimated_fee_used)

        return MaxTransferableAmount(fee=fee_detail, send_max_value=res.send_max_value)

    @classmethod
    def estimate_tx_fee_v2(
        cls, req: EOATxFeeReqRequest
    ) -> Union[EOAFee, FixedEstimatedFee]:
        result: EOATxFeeRespV2 = cls.client.estimate_tx_fee_v2(req)
        fee = None
        if not result:
            return fee
        # 此处为方便后续非dev api相关逻辑复用 多做一层对象转换处理
        match result.fee_type:
            case FeeTypeEnum.FEE_MODEL_UTXO:
                fee = EOAFee(
                    fee_type=FeeModelType.TYPE_UTXO,
                    fee_token_id=result.fee_token_id,
                    slow=UTXOEstimatedFee.populate_from_bc_utxo(
                        result.slow.actual_instance
                    ),
                    standard=UTXOEstimatedFee.populate_from_bc_utxo(
                        result.standard.actual_instance
                    ),
                    fast=UTXOEstimatedFee.populate_from_bc_utxo(
                        result.fast.actual_instance
                    ),
                )
            case FeeTypeEnum.FEE_MODEL_EVM_LEGACY:
                fee = EOAFee(
                    fee_type=FeeModelType.TYPE_EVM_LEGACY,
                    fee_token_id=result.fee_token_id,
                    slow=EVMLegacyEstimatedFee.populate_from_bc_evm_legacy(
                        result.slow.actual_instance
                    ),
                    standard=EVMLegacyEstimatedFee.populate_from_bc_evm_legacy(
                        result.standard.actual_instance
                    ),
                    fast=EVMLegacyEstimatedFee.populate_from_bc_evm_legacy(
                        result.fast.actual_instance
                    ),
                )

            case FeeTypeEnum.FEE_MODEL_EVM_EIP_1559:
                fee = EOAFee(
                    fee_type=FeeModelType.TYPE_EVM_EIP_1559,
                    fee_token_id=result.fee_token_id,
                    slow=EVMEIP1559EstimatedFee.populate_from_bc_evm_eip1559(
                        result.slow.actual_instance
                    ),
                    standard=EVMEIP1559EstimatedFee.populate_from_bc_evm_eip1559(
                        result.standard.actual_instance
                    ),
                    fast=EVMEIP1559EstimatedFee.populate_from_bc_evm_eip1559(
                        result.fast.actual_instance
                    ),
                )
            case FeeTypeEnum.FEE_MODEL_FIXED:
                fee = FixedEstimatedFee.populate_from_bc_fixed(
                    result.standard.actual_instance
                )
            case FeeTypeEnum.FEE_MODEL_SOLANA:
                fee = EOAFee(
                    fee_type=FeeModelType.TYPE_SOL,
                    fee_token_id=result.fee_token_id,
                    slow=SOLEstimatedFee.populate_from_bc(result.slow.actual_instance),
                    standard=SOLEstimatedFee.populate_from_bc(
                        result.standard.actual_instance
                    ),
                    fast=SOLEstimatedFee.populate_from_bc(result.fast.actual_instance),
                )
            case FeeTypeEnum.FEE_MODEL_FIL:
                fee = EOAFee(
                    fee_type=FeeModelType.TYPE_FIL,
                    fee_token_id=result.fee_token_id,
                    slow=FILEstimatedFee.populate_from_bc(result.slow.actual_instance),
                    standard=FILEstimatedFee.populate_from_bc(
                        result.standard.actual_instance
                    ),
                    fast=FILEstimatedFee.populate_from_bc(result.fast.actual_instance),
                )
        return fee

    @classmethod
    def batch_estimate_tx_fee_v2(cls, req: EOABatchTxFeeReqRequest) -> list[Any]:
        response: EOABatchTxFeeResp = cls.client.batch_estimate_tx_fee_v2(req)

        result = []
        for bc_fee in response.fees:
            fee = None
            data = bc_fee.actual_instance

            match data.fee_type:
                case FeeTypeEnum.FEE_MODEL_UTXO:
                    raise
                case FeeTypeEnum.FEE_MODEL_EVM_LEGACY:
                    fee = EOAFee(
                        fee_type=FeeModelType.TYPE_EVM_LEGACY,
                        fee_token_id=data.fee_token_id,
                        slow=EVMLegacyEstimatedFee.populate_from_bc_evm_legacy(
                            data.slow.actual_instance
                        ),
                        standard=EVMLegacyEstimatedFee.populate_from_bc_evm_legacy(
                            data.standard.actual_instance
                        ),
                        fast=EVMLegacyEstimatedFee.populate_from_bc_evm_legacy(
                            data.fast.actual_instance
                        ),
                    )

                case FeeTypeEnum.FEE_MODEL_EVM_EIP_1559:
                    fee = EOAFee(
                        fee_type=FeeModelType.TYPE_EVM_EIP_1559,
                        fee_token_id=data.fee_token_id,
                        slow=EVMEIP1559EstimatedFee.populate_from_bc_evm_eip1559(
                            data.slow.actual_instance
                        ),
                        standard=EVMEIP1559EstimatedFee.populate_from_bc_evm_eip1559(
                            data.standard.actual_instance
                        ),
                        fast=EVMEIP1559EstimatedFee.populate_from_bc_evm_eip1559(
                            data.fast.actual_instance
                        ),
                    )
                case FeeTypeEnum.FEE_MODEL_FIXED:
                    fee = FixedEstimatedFee.populate_from_bc_fixed(
                        data.standard.actual_instance
                    )

            result.append(fee)

        return result


class BlockchainFeeManagerEOACustody(BlockchainManager):
    client = BlockchainClientManagerV2EOACustody

    @classmethod
    def get_max_transfer_value(
        cls, params: GetMaxTransferableAmountParams
    ) -> MaxTransferableAmount:
        params.sub_type = SubTypeEnum.EOA_SUB_TYPE_CUSTODY
        return super().get_max_transfer_value(params)


class BlockchainFeeManagerEOAMPC(BlockchainManager):
    client = BlockchainClientManagerV2EOAMpc

    @classmethod
    def get_max_transfer_value(
        cls, params: GetMaxTransferableAmountParams
    ) -> MaxTransferableAmount:
        params.sub_type = SubTypeEnum.EOA_SUB_TYPE_MPC
        return super().get_max_transfer_value(params)
