import json
import logging
import uuid
from collections import defaultdict
from typing import Dict, List, Optional, Tuple, Union

from _decimal import Decimal
from cobo_libs.cobo_auth.dao import AuthStatementDao
from cobo_libs.cobo_auth.data.enums import AuthStatementSignResult, AuthStatementType
from cobo_libs.cobo_auth.data.object import AuthStatementResult
from cobo_libs.cobo_auth.manangers.statement import AuthStatementManager
from cobo_libs.cobo_auth.manangers.template import StatementBuilder
from cobo_libs.cobo_auth.models import AuthStatement
from cobo_libs.crypto import CurveType
from cobo_libs.utils.lock import CoboLock, LockTimeOutError
from cobo_libs.utils.time import now_ts
from django.conf import settings
from django.db import transaction

from custody.api.exception import CustodyException
from custody.cobo.settings.constants.coin_codes import COIN_SETH
from custody.cobo.utils.env import Env
from custody.cobo.utils.precondition import check
from custody.coin.managers import CustodyCoinManager
from custody.custody.dao.custody_user import CustodyUserDao
from custody.custody.dao.custody_wallet import CustodyWalletMPCVaultRelationDao
from custody.custody.dao.mpc_vault import (
    MPCProjectDao,
    MPCVaultDao,
    UserDefaultMPCVaultDao,
)
from custody.custody.dao.organization import OrganizationDao
from custody.custody.dao.tss_node_v2 import (
    TSSGroupV2Dao,
    TSSNodeV2Dao,
    TSSRequestV2ConfirmLogDao,
    TSSRequestV2Dao,
    TSSRootPubkeyV2Dao,
)
from custody.custody.data.enums import (
    TSSGroupStatusEnum,
    TSSGroupTypeEnum,
    TssNodeStatus,
    TSSNodeStatusEnum,
    TSSNodeTypeEnum,
    TSSRequestConfirmResult,
    TSSRequestStatusEnum,
    TSSRequestTypeEnum,
    UserDefaultMPCVaultStatus,
)
from custody.custody.data.objects import (
    BatchTSSKeygenParam,
    BatchTSSReshareParam,
    TSSGroupConfig,
)
from custody.custody.exceptions import CustodyApiException
from custody.custody.managers.authchain_user import AuthchainUserSwitchManager
from custody.custody.managers.blockchain import BlockchainMPCClientManager
from custody.custody.managers.cobo_auth import CoboAuthManager
from custody.custody.managers.config import OrganizationConfigManager
from custody.custody.managers.tss_node import TssNodeManager
from custody.custody.managers.tss_node_v2 import TSSRequestV2Manager
from custody.custody.models.custody_user import CustodyUser
from custody.custody.models.organization import Organization
from custody.custody.models.tss_node_v2 import (
    MPCVault,
    TSSGroupV2,
    TSSNodeV2,
    TSSRequestV2,
    TSSRequestV2ConfirmLog,
)
from custody.custody.utils.feature_switch import SwitchNameEnum, bool_feature_switch
from custody.custody.utils.switch import send_portal_mpc_key_share_notification
from custody.marketdata.controllers import CoinCurrencyRateManager
from custody.wallet.models.wallet import TokenAsset
from custody.web3.data.enums import EOAMPCFailedType
from custody.web3.data.objects import EOAMPCCallbackFailedReason
from custody.web3.managers.balance.manager import BalanceManager
from waas2.authentications.universal_access_control.bo.permission_scope import Scope
from waas2.base.bo.wallet import WaaS2WalletCreateAddressBO
from waas2.base.bo.wallets.mpc import WaaS2EOAWalletCreateBO
from waas2.base.enums.common import AddressEncoding
from waas2.base.enums.wallet import WalletSubtype, WalletType
from waas2.base.managers.wallet_source import WalletSourceManager
from waas2.coins.managers_v2 import WaaSTokenManager
from waas2.mpc.bo.key_share_verification import CreateKeyShareVerificationBo
from waas2.mpc.bo.vault import (
    CreateTSSGroupKeyBo,
    CreateTSSRequestBo,
    KeyGeneratingFailedDetail,
    KeyHolderConfirmation,
    KeyHolderResponse,
    MPCVaultBo,
    RootPubkeyBo,
    TSSGroupIdBo,
    TSSGroupKeyBo,
    TSSKeyHolderBo,
    TSSRequestBo,
)
from waas2.mpc.enums.mpc_vault import (
    KeyGroupStatus,
    KeyGroupType,
    KeyHolderStatus,
    KeyHolderType,
    MPCVaultType,
    TSSRequestType,
    TSSType,
)
from waas2.mpc.managers.dev.webhook_wallet_mpc import WalletMpcWebhookEventManager
from waas2.mpc.processors.utils import MPCWalletUtils
from waas2.notifications.enums.notification import TemplateType
from waas2.notifications.notifiers import PortalNotifier

logger = logging.getLogger("app.custody")


class MPCVaultProcessor:
    @classmethod
    def create_vault(
        cls,
        org_id: int,
        name: str,
        vault_type: MPCVaultType,
        project_id: str = None,
        default: bool = False,
        custody_user: CustodyUser = None,
    ) -> MPCVaultBo:
        project = None
        if default is None:
            default = False
        if vault_type == MPCVaultType.UserControlled:
            if not project_id:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"{MPCVaultType.UserControlled} vault need project id",
                )
            project = MPCProjectDao.get_project(project_id)
            if not project:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"project id {project_id} invalid",
                )
        exist_vault = MPCVaultDao.query_by_org_and_name(org_id, name)
        if exist_vault:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"The MPC vault with the name '{name}' exists.",
            )
        cls._check_vault_name(name)
        v = MPCVaultDao.create(
            uuid=str(uuid.uuid4()),
            org_id=org_id,
            name=name,
            vault_type=vault_type,
            project_id=project.id if project else None,
        )
        if default and custody_user and vault_type == MPCVaultType.OrgControlled:
            default_vault = UserDefaultMPCVaultDao.get_by_user(custody_user.id)
            if default_vault:
                UserDefaultMPCVaultDao.update_by_id(
                    default_vault.id, status=UserDefaultMPCVaultStatus.STATUS_INACTIVE
                )
            UserDefaultMPCVaultDao.create(custody_user=custody_user, mpc_vault=v)
        return cls._pack_vaults([v], custody_user)[0]

    @classmethod
    @transaction.atomic
    def set_node_backed_up(cls, org_id: int, node_id: str):
        tss_nodes = TSSNodeV2Dao.get_node_by_node_id(org_id, node_id)
        for tss_node in tss_nodes:
            groups = TSSGroupV2Dao.list_group_by_node(org_id, tss_node.pk)
            for group in groups:
                if group.status != TSSGroupStatusEnum.STATUS_ACTIVE:
                    continue
                TSSGroupV2Dao.add_backed_up_node(group.pk, tss_node.pk)

    @classmethod
    def update_node_name(cls, org_id: int, key_group_id: str, node_id: str, name: str):
        tss_group = TSSGroupV2Dao.get_by_uuid(key_group_id)
        if not tss_group or tss_group.org_id != org_id:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"key group :{key_group_id} not exist",
            )
        tss_nodes = [
            n
            for n in TSSNodeV2Dao.list_node_by_node_pks(
                TSSGroupV2Dao.get_node_pks(tss_group)
            )
            if n.node_id == node_id
        ]
        if not tss_nodes:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"tss node :{node_id} not exist",
            )
        tss_node = tss_nodes[0]
        group_config = TSSGroupConfig.from_json(tss_group.config)
        node_names = group_config.node_names or {}
        node_names[tss_node.pk] = name
        group_config.node_names = node_names
        TSSGroupV2Dao.update_by_id(tss_group.pk, config=group_config.to_json())

    @classmethod
    def update_vault(
        cls,
        custody_user: CustodyUser,
        vault_uuid: str,
        name: str,
        default: bool = False,
    ):
        mpc_vault = MPCVaultDao.get_by_uuid(vault_uuid)
        if not mpc_vault:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"mpc vault of:{vault_uuid} not exist",
            )
        if name:
            cls._check_vault_name(name)
            exist_vault = MPCVaultDao.query_by_org_and_name(mpc_vault.org.id, name)
            if exist_vault:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"The MPC vault with the name '{name}' exists.",
                )
            MPCVaultDao.update_by_id_directly(mpc_vault.id, name=name)
        if default and mpc_vault.vault_type == MPCVaultType.OrgControlled.value:
            default_vault = UserDefaultMPCVaultDao.get_by_user(custody_user.id)
            if default_vault and default_vault.id != mpc_vault.id:
                UserDefaultMPCVaultDao.update_by_id(
                    default_vault.id, status=UserDefaultMPCVaultStatus.STATUS_INACTIVE
                )
            if not default_vault or default_vault.id != mpc_vault.id:
                UserDefaultMPCVaultDao.create(
                    custody_user=custody_user, mpc_vault=mpc_vault
                )

    @classmethod
    def list_vault(
        cls,
        org_id: int,
        project_id: str = None,
        custody_user: CustodyUser = None,
        scope: Scope = None,
    ) -> List[MPCVaultBo]:
        if project_id:
            if scope is not None:
                if (
                    WalletType.MPC not in scope.wallet_types
                    and WalletSubtype.UserControlled not in scope.wallet_subtypes
                    and (scope.project_ids and project_id not in scope.project_ids)
                ):
                    raise CustodyApiException(
                        CustodyException.ERROR_PERMISSION,
                        f"mpc project of:{project_id} is not accessible",
                    )

            project = MPCProjectDao.get_project(project_id)
            if not project:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"mpc project of:{project_id} not exist",
                )
            if scope is not None and scope.vault_ids:
                vaults = MPCVaultDao.get_by_projects_and_uuids(
                    project.id, scope.vault_ids
                )
            else:
                vaults = MPCVaultDao.list_by_projects(project.id)
        else:
            org = OrganizationDao.get_by_id_or_null(org_id)
            # demo org首次进入，自动创建test vault
            if OrganizationConfigManager.is_portal_demo_org_by_org(org):
                vaults = MPCVaultDao.list_by_org(
                    org_id, vault_type=MPCVaultType.OrgControlled
                )
                if not vaults:
                    cls.create_vault(
                        org_id, name="test vault", vault_type=MPCVaultType.OrgControlled
                    )
            if scope is not None:
                if (
                    WalletType.MPC in scope.wallet_types
                    or WalletSubtype.OrgControlled in scope.wallet_subtypes
                ):
                    vaults = MPCVaultDao.list_by_org(
                        org_id, vault_type=MPCVaultType.OrgControlled
                    )
                elif scope.vault_ids:
                    vaults = MPCVaultDao.get_by_uuids(
                        scope.vault_ids, vault_type=MPCVaultType.OrgControlled
                    )
                else:
                    return []
            else:
                vaults = MPCVaultDao.list_by_org(
                    org_id, vault_type=MPCVaultType.OrgControlled
                )
        return cls._pack_vaults(vaults, custody_user)

    @classmethod
    def get_vault_by_id(cls, uuid: str, custody_user: CustodyUser = None) -> MPCVaultBo:
        mpc_vault = MPCVaultDao.get_by_uuid(uuid)
        if not mpc_vault:
            return None
        return cls._pack_vaults([mpc_vault], custody_user)[0]

    @classmethod
    @transaction.atomic
    def create_key_group(
        cls,
        org_id: int,
        vault_uuid: str,
        create_bo: CreateTSSGroupKeyBo,
        auto_backup: bool = False,
    ):
        mpc_vault = cls.get_and_check_vault(vault_uuid)
        if create_bo.type == KeyGroupType.MainKeyGroup.value:
            exist_key_groups = TSSGroupV2Dao.list_by_vault(
                vault_id=mpc_vault.id,
                group_type=TSSGroupTypeEnum.TYPE_MPC_OWNER,
                exclude_status=[TSSGroupStatusEnum.STATUS_INACTIVE],
            )
            if len(exist_key_groups) > 1:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    "Your request failed because a Main Key Group is generated.",
                )

        if create_bo.type in [
            KeyGroupType.MainKeyGroup.value,
            KeyGroupType.SigningKeyGroup.value,
        ]:
            create_bo.key_holders = [
                TSSKeyHolderBo(
                    name="Cobo",
                    type=KeyHolderType.COBO.name,
                    tss_node_id=settings.COBO_TSS_NODE,
                )
            ] + create_bo.key_holders
        if len(create_bo.key_holders) != create_bo.node_number:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                "key holders count invalid",
            )

        if create_bo.threshold < 2 or create_bo.threshold > create_bo.node_number:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                "threshold invalid",
            )
        # create tss node
        node_pks = []
        node_names = {}
        exist_nodes = set()
        cobo_node = None
        for holder in create_bo.key_holders:
            check(
                TssNodeManager.verify_node_id(holder.tss_node_id),
                CustodyApiException,
                CustodyApiException.ERROR_INVALID_PARAM,
                "TSS node ID invalid",
            )
            if holder.tss_node_id in exist_nodes:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"TSS node ID: {holder.tss_node_id} duplicate",
                )
            exist_nodes.add(holder.tss_node_id)
            if holder.type == KeyHolderType.COBO.name:
                if holder.tss_node_id != settings.COBO_TSS_NODE:
                    raise CustodyApiException(
                        CustodyException.ERROR_INVALID_PARAM,
                        f"cobo tss node: {holder.tss_node_id} invalid",
                    )
                cobo_node = TSSNodeV2Dao.get_cobo_node(org_id)
                if not cobo_node:
                    cobo_node = TSSNodeV2Dao.create(
                        org_id, holder.tss_node_id, TSSNodeTypeEnum.TYPE_COBO, "Cobo"
                    )
                    BlockchainMPCClientManager.add_auth_tss_node(holder.tss_node_id)
                node_pks.append(cobo_node.pk)
            elif holder.type == KeyHolderType.API.name:
                nodes = TSSNodeV2Dao.get_node_by_node_id(org_id, holder.tss_node_id)
                node = nodes[0] if nodes else None
                if not node:
                    node = TSSNodeV2Dao.create(
                        org_id,
                        holder.tss_node_id,
                        TSSNodeTypeEnum.TYPE_SERVER,
                        holder.name,
                    )
                    BlockchainMPCClientManager.add_auth_tss_node(holder.tss_node_id)
                node_pks.append(node.pk)
                node_names[node.pk] = holder.name
            else:
                user = CustodyUserDao.get_by_uuid(holder.account_id)
                if not user:
                    raise CustodyApiException(
                        CustodyException.ERROR_INVALID_PARAM,
                        f"holder of account: {holder.account_id} not exist",
                    )
                node = TSSNodeV2Dao.get_mobile_node_by_node_id(
                    org_id, holder.tss_node_id, user.id
                )
                if not node:
                    raise CustodyApiException(
                        CustodyException.ERROR_INVALID_PARAM,
                        f"holder of tss node: {holder.tss_node_id} not exist",
                    )
                node = node[0]
                node_pks.append(node.pk)

        # mapping group type
        group_type = cls._get_tss_group_type_by_key_group_type(create_bo.type)
        tss_group = TSSGroupV2Dao.create(
            org_id=org_id,
            group_type=group_type,
            node_number=create_bo.node_number,
            threshold=create_bo.threshold,
            node_pks=node_pks,
            status=TSSGroupStatusEnum.STATUS_NEW,
            vault_id=mpc_vault.id,
            node_names=node_names,
        )
        if cobo_node:
            TSSGroupV2Dao.add_backed_up_node(tss_group.id, cobo_node.pk)

        if auto_backup:
            other_node_pks = [pk for pk in node_pks if pk != cobo_node.pk]
            other_nodes = TSSNodeV2Dao.list_node_by_node_pks(other_node_pks)
            for node in other_nodes:
                TSSGroupV2Dao.add_backed_up_node(tss_group.id, node.pk)

        return cls._pack_key_group(tss_group)

    @classmethod
    def get_and_check_vault(cls, vault_uuid: str) -> MPCVault:
        mpc_vault = MPCVaultDao.get_by_uuid(vault_uuid)
        if not mpc_vault:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"mpc vault of:{vault_uuid} not exist",
            )
        return mpc_vault

    @classmethod
    def get_key_group(cls, vault_uuid: str, key_group_id: str):
        tss_group = TSSGroupV2Dao.get_by_uuid(key_group_id)
        if not tss_group:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"key group of:{key_group_id} not exist",
            )
        if vault_uuid != tss_group.mpc_vault.uuid:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"key group of:{key_group_id} not belong to vault:{vault_uuid}",
            )
        account_map = {}
        online_map = {}
        guard_enable_map = {}
        user_images = {}
        node_pks = TSSGroupV2Dao.get_node_pks(tss_group)
        if node_pks:
            tss_nodes = TSSNodeV2Dao.list_node_by_node_pks_include_inactive(
                list(node_pks)
            )
            user_ids = [t.user_id for t in tss_nodes if t.user_id]
            users = CustodyUserDao.list_by_ids(user_ids)
            account_map = {u.id: u.uuid for u in users}
            online_map = BlockchainMPCClientManager.check_nodes(
                [t.node_id for t in tss_nodes]
            )
            from custody.custody.services.custody import CustodyService

            guard_enable_map = CustodyService.cobo_auth_enabled_by_users(
                user_ids, str(tss_group.org.id)
            )
            from waas2.organizations.processors.organization import (
                OrganizationProcessor,
            )

            user_images = OrganizationProcessor.get_members_logo(user_ids)
        return cls._pack_key_group(
            tss_group, online_map, account_map, guard_enable_map, user_images
        )

    @classmethod
    def list_key_group(cls, vault_uuid: str, group_key_type: KeyGroupType = None):
        mpc_vault = MPCVaultDao.get_by_uuid(vault_uuid)
        if not mpc_vault:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"mpc vault of:{vault_uuid} not exist",
            )
        tss_group_type = None
        if group_key_type:
            tss_group_type = cls._get_tss_group_type_by_key_group_type(
                group_key_type, default_signing=False
            )
        tss_groups = TSSGroupV2Dao.list_by_vault(
            mpc_vault.id,
            tss_group_type,
            exclude_status=[TSSGroupStatusEnum.STATUS_INACTIVE],
        )

        account_map = {}
        online_map = {}
        guard_enable_map = {}
        user_images = {}
        node_pks = set()
        for group in tss_groups:
            group_node_pks = TSSGroupV2Dao.get_node_pks(group)
            node_pks.update(group_node_pks)
        if node_pks:
            tss_nodes = TSSNodeV2Dao.list_node_by_node_pks_include_inactive(
                list(node_pks)
            )
            user_ids = [t.user_id for t in tss_nodes if t.user_id]
            users = CustodyUserDao.list_by_ids(user_ids)
            account_map = {u.id: u.uuid for u in users}
            online_map = BlockchainMPCClientManager.check_nodes(
                [t.node_id for t in tss_nodes]
            )
            from custody.custody.services.custody import CustodyService

            guard_enable_map = CustodyService.cobo_auth_enabled_by_users(
                user_ids, str(mpc_vault.org.id)
            )
            from waas2.organizations.processors.organization import (
                OrganizationProcessor,
            )

            user_images = OrganizationProcessor.get_members_logo(user_ids)

        return [
            cls._pack_key_group(
                group, online_map, account_map, guard_enable_map, user_images
            )
            for group in tss_groups
        ]

    @classmethod
    def delete_key_group(cls, vault_uuid: str, key_group_id: str):
        group = TSSGroupV2Dao.get_by_uuid(key_group_id)
        if not group:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"not exist key group of id:{key_group_id}",
            )
        if vault_uuid != group.mpc_vault.uuid:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"key group of:{group.uuid} not belong to vault:{vault_uuid}",
            )
        if (
            group.group_type == TSSGroupTypeEnum.TYPE_MPC_OWNER
            and group.status == TSSGroupStatusEnum.STATUS_ACTIVE
        ):
            raise CustodyApiException(
                CustodyException.ERROR_PERMISSION_DENY,
                "This key group has already generated private key shares and cannot be deleted.",
            )
        if group.status != TSSGroupStatusEnum.STATUS_INACTIVE:
            TSSGroupV2Dao.update_by_id_directly(
                group.id, status=TSSGroupStatusEnum.STATUS_INACTIVE
            )
            if group.group_type == TSSGroupTypeEnum.TYPE_MPC_OWNER:
                tss_requests = TSSRequestV2Dao.list_by_group_id(group.id)
            else:
                tss_requests = TSSRequestV2Dao.list_by_new_group_id(group.id)
            pending_tss_requests = [
                r
                for r in tss_requests
                if r.status not in TSSRequestStatusEnum.final_status()
            ]
            if pending_tss_requests:
                for r in pending_tss_requests:
                    cls.cancel_tss_request(vault_uuid, r.id, "system")

    @classmethod
    def update_key_group(cls, vault_uuid: str, key_group_id: str, action: str):
        group, exist_groups = cls.check_update_key_group_param(
            vault_uuid, key_group_id, action
        )
        if action == "upgradeToMasterKeyGroup":
            TSSGroupV2Dao.update_by_ids(
                [g.id for g in exist_groups], status=TSSGroupStatusEnum.STATUS_INACTIVE
            )
            TSSGroupV2Dao.update_by_id_directly(
                group.id, group_type=TSSGroupTypeEnum.TYPE_MPC_OWNER
            )

    @classmethod
    def check_update_key_group_param(
        cls, vault_uuid: str, key_group_id: str, action: str
    ) -> Tuple[TSSGroupV2, List[TSSGroupV2]]:
        group = TSSGroupV2Dao.get_by_uuid(key_group_id)
        if not group:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"not exist key group of id:{key_group_id}",
            )
        if vault_uuid != group.mpc_vault.uuid:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"key group of:{group.uuid} not belong to vault:{vault_uuid}",
            )
        exist_groups = []
        if action == "upgradeToMasterKeyGroup":
            if (
                group.group_type != TSSGroupTypeEnum.TYPE_MPC_SIGNER
                or group.status != TSSGroupStatusEnum.STATUS_ACTIVE
            ):
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"key group of id:{key_group_id} is not valid signer group",
                )
            exist_groups = TSSGroupV2Dao.list_by_vault(
                group.mpc_vault.id,
                TSSGroupTypeEnum.TYPE_MPC_OWNER,
                exclude_status=[TSSGroupStatusEnum.STATUS_INACTIVE],
            )
            if len(exist_groups) > 1:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    "Your request failed because a new Master Key Group is currently being generated.",
                )
        return group, exist_groups

    @classmethod
    def create_tss_request(cls, create_bo: CreateTSSRequestBo):
        vault_uuid = create_bo.vault_id
        target_key_group_id = create_bo.target_key_group_id
        used_key_group_id = create_bo.used_key_group_id
        request_type = create_bo.type
        detail_param = create_bo.detail_param
        mpc_vault = MPCVaultDao.get_by_uuid(vault_uuid)
        if not mpc_vault:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"mpc vault of:{vault_uuid} not exist",
            )
        target_group = TSSGroupV2Dao.get_by_uuid(target_key_group_id)
        if not target_group:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"not exist key group of id:{target_key_group_id}",
            )
        used_group = None
        if request_type in [TSSRequestType.KeyGenFromKeyGroup, TSSRequestType.RECOVERY]:
            used_group = TSSGroupV2Dao.get_by_uuid(used_key_group_id)
            if not used_group:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"not exist key group of id:{used_key_group_id}",
                )
        # create tss request
        param = None
        group_origin = None
        group_target = None
        if request_type == TSSRequestType.KEYGEN:
            pubkeys = TSSRootPubkeyV2Dao.list_by_vault(mpc_vault.id)
            if pubkeys is not None and len(pubkeys) > 0:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"vault:{mpc_vault.uuid} already has root pubkeys, can not create KeyGen tss request",
                )

            tss_nodes = TSSNodeV2Dao.list_node_by_node_pks(
                TSSGroupV2Dao.get_node_pks(target_group)
            )
            param = BatchTSSKeygenParam(
                node_ids=[t.node_id for t in tss_nodes],
                threshold=target_group.threshold,
                curves=settings.MPC_SUPPORT_CURVES,
            ).to_json()
            group_origin = target_group
        elif request_type in [
            TSSRequestType.KeyGenFromKeyGroup,
            TSSRequestType.RECOVERY,
        ]:
            if used_group.status != TSSGroupStatusEnum.STATUS_ACTIVE:
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    "used group status invalid",
                )
            group_ids = [g for g in TSSGroupV2Dao.get_group_ids(used_group).values()]
            pubkeys = TSSRootPubkeyV2Dao.list_by_vault(mpc_vault.id)
            used_tss_nodes = TSSNodeV2Dao.list_node_by_node_pks(
                TSSGroupV2Dao.get_node_pks(used_group)
            )
            group_config = TSSGroupConfig.from_json(used_group.config)
            if group_config.signer_node_pks:
                used_node_ids = [
                    t.node_id
                    for t in used_tss_nodes
                    if t.id in group_config.signer_node_pks
                ]
            else:
                used_node_ids = [
                    t.node_id
                    for t in used_tss_nodes
                    if t.node_type
                    not in [
                        TSSNodeTypeEnum.TYPE_THIRD,
                        TSSNodeTypeEnum.TYPE_MOBILE_BACKUP,
                    ]
                ]
            if len(used_node_ids) != used_group.threshold:
                logger.error(
                    f"vault {create_bo.vault_id} tss request {request_type} error, used node ids {len(used_node_ids)} not equal used group threshold {used_group.threshold}"
                )
                raise CustodyApiException(
                    CustodyException.ERROR_INVALID_PARAM,
                    f"used node ids {len(used_node_ids)} not equal used group threshold {used_group.threshold}",
                )
            if detail_param and detail_param.used_node_ids:
                if (
                    set(detail_param.used_node_ids).issubset(set(used_node_ids))
                    is False
                ):
                    raise CustodyApiException(
                        CustodyException.ERROR_INVALID_PARAM,
                        "used node ids invalid",
                    )
                used_node_ids = detail_param.used_node_ids
            new_tss_nodes = TSSNodeV2Dao.list_node_by_node_pks(
                TSSGroupV2Dao.get_node_pks(target_group)
            )
            new_node_ids = [t.node_id for t in new_tss_nodes]
            param = BatchTSSReshareParam(
                group_ids=group_ids,
                root_pubkeys=[p.pubkey for p in pubkeys],
                used_node_ids=used_node_ids,
                new_threshold=target_group.threshold,
                new_node_ids=new_node_ids,
            ).to_json()
            group_origin = used_group
            group_target = target_group
        tss_request = TSSRequestV2Dao.create_with_param(
            request_type=cls._convert_to_tss_request_enum(request_type),
            biz_type=request_type.value,
            group_origin=group_origin,
            group_target=group_target,
            param=param,
            vault_id=mpc_vault.id,
            user_id=create_bo.user.id if create_bo.user else None,
            status=TSSRequestStatusEnum.STATUS_WAIT_RISKCONTROL,
        )
        WalletMpcWebhookEventManager.create_tss_request_event(
            org_id=mpc_vault.org.uuid, tss_request=tss_request, create=True
        )
        return cls._pack_tss_request(tss_request, [])

    @classmethod
    def get_tss_request(cls, vault_uuid: str, request_id: int) -> TSSRequestBo:
        request = TSSRequestV2Dao.get_by_id_or_null(request_id)
        if not request:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"not exist tss request of id:{request_id}",
            )
        if request.mpc_vault.uuid != vault_uuid:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"tss request of:{request_id} not belong to vault:{vault_uuid}",
            )
        confirm_logs = TSSRequestV2ConfirmLogDao.list_by_requests([request_id])
        from waas2.organizations.processors.organization import OrganizationProcessor

        user_images = OrganizationProcessor.get_members_logo(
            [
                confirm_log.tss_node.user_id
                for confirm_log in confirm_logs
                if confirm_log.tss_node.user_id
            ]
        )
        return cls._pack_tss_request(request, confirm_logs, user_images)

    @classmethod
    def list_tss_request(cls, vault_uuid: str, key_group_id: str) -> List[TSSRequestBo]:
        key_group = TSSGroupV2Dao.get_by_uuid(key_group_id)
        if not key_group:
            return []
        if key_group.mpc_vault.uuid != vault_uuid:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"vault id: {vault_uuid} invalid",
            )
        if key_group.group_type == TSSGroupTypeEnum.TYPE_MPC_OWNER:
            tss_requests = TSSRequestV2Dao.list_by_group_id(key_group.id)
            # 兼容signer key group upgrade to main key group的场景
            if not tss_requests:
                tss_requests = TSSRequestV2Dao.list_by_new_group_id(key_group.id)
        else:
            tss_requests = TSSRequestV2Dao.list_by_new_group_id(key_group.id)
        confirm_logs = TSSRequestV2ConfirmLogDao.list_by_requests(
            [r.id for r in tss_requests]
        )
        confirm_log_map = {}
        user_ids = []
        for confirm_log in confirm_logs:
            user_ids.extend(
                [
                    confirm_log.tss_node.user_id
                    for confirm_log in confirm_logs
                    if confirm_log.tss_node.user_id
                ]
            )
            confirm_log_map.setdefault(confirm_log.tss_request.id, [])
            confirm_log_map[confirm_log.tss_request.id].append(confirm_log)
        from waas2.organizations.processors.organization import OrganizationProcessor

        user_images = OrganizationProcessor.get_members_logo(user_ids)
        return [
            cls._pack_tss_request(r, confirm_log_map.get(r.id, []), user_images)
            for r in tss_requests
        ]

    @classmethod
    def cancel_tss_request(cls, vault_uuid: str, request_id: int, operator: str = ""):
        request = TSSRequestV2Dao.get_by_id_or_null(request_id)
        if not request:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                "invalid request id",
            )
        if request.mpc_vault.uuid != vault_uuid:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"tss request of:{request_id} not belong to vault:{vault_uuid}",
            )
        if request.status in TSSRequestStatusEnum.final_status():
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                "This key group has already generated private key shares and cannot be deleted.",
            )
        if request.status != TSSRequestStatusEnum.STATUS_SENT:
            TSSRequestV2Manager.on_tss_request_failed(
                request,
                EOAMPCCallbackFailedReason(
                    failed_type=EOAMPCFailedType.TYPE_CUSTODY_FAILED,
                    detail="Canceled by admin",
                ).to_json(),
                False,
            )
        res = BlockchainMPCClientManager.batch_cancel_tss_request(
            request_id=request.request_id, reason="Canceled by admin", operator=operator
        )
        # cancel是同步接口，调用成功后，可以直接fail调request
        if res["cancel_result"]:
            TSSRequestV2Manager.on_tss_request_failed(
                request,
                EOAMPCCallbackFailedReason(
                    failed_type=EOAMPCFailedType.TYPE_CUSTODY_FAILED,
                    detail="Canceled by admin",
                ).to_json(),
                False,
            )
        unfinished_confirm_logs = [
            confirm_log
            for confirm_log in TSSRequestV2ConfirmLogDao.list_by_requests([request_id])
            if confirm_log.result == TSSRequestConfirmResult.STATUS_NEW
            and confirm_log.statement_id
        ]
        statements = AuthStatementDao.list_by_ids(
            [confirm_log.statement_id for confirm_log in unfinished_confirm_logs]
        )
        for statement in statements:
            CoboAuthManager.remove_statement(statement.msghash)

    @classmethod
    def list_api_signer_key_groups(cls, vault_id: int, order_by_online: bool = False):
        groups = TSSGroupV2Dao.list_by_vault(
            vault_id,
            group_type=[
                TSSGroupTypeEnum.TYPE_MPC_OWNER,
                TSSGroupTypeEnum.TYPE_MPC_SIGNER,
            ],
            include_status=[TSSGroupStatusEnum.STATUS_ACTIVE],
        )
        group_node_pk_map = {}
        for g in groups:
            group_node_pk_map[g.id] = TSSGroupV2Dao.get_node_pks(g)

        node_map = {
            n.pk: n
            for n in TSSNodeV2Dao.list_node_by_node_pks(
                sum(group_node_pk_map.values(), [])
            )
        }

        api_signer_groups = []
        for g in groups:
            group_nodes: List[TSSNodeV2] = [
                node_map.get(node_pk) for node_pk in group_node_pk_map.get(g.id)
            ]
            mobile_nodes = [
                n for n in group_nodes if n.node_type == TSSNodeTypeEnum.TYPE_MOBILE
            ]
            if mobile_nodes:
                continue
            api_signer_groups.append(g)
        if order_by_online and api_signer_groups:
            node_online_status = BlockchainMPCClientManager.check_nodes(
                [n.node_id for n in node_map.values()]
            )
            order_groups = []
            for g in api_signer_groups:
                group_nodes: List[TSSNodeV2] = [
                    node_map.get(node_pk) for node_pk in group_node_pk_map.get(g.id)
                ]
                offline_nodes = [
                    n
                    for n in group_nodes
                    if node_online_status.get(n.node_id)
                    != TssNodeStatus.StatusRegisterOnline
                ]
                order_groups.append((g, not bool(offline_nodes)))
            order_groups.sort(key=lambda x: (x[1], x[0].created_time), reverse=True)
            api_signer_groups = [t[0] for t in order_groups]
        return api_signer_groups

    @classmethod
    def list_mobile_signer_key_groups(cls, vault_id: int):
        groups = TSSGroupV2Dao.list_by_vault(
            vault_id,
            group_type=[
                TSSGroupTypeEnum.TYPE_MPC_OWNER,
                TSSGroupTypeEnum.TYPE_MPC_SIGNER,
            ],
            include_status=[TSSGroupStatusEnum.STATUS_ACTIVE],
        )
        group_node_pk_map = {}
        for g in groups:
            group_node_pk_map[g.id] = TSSGroupV2Dao.get_node_pks(g)

        node_map = {
            n.pk: n
            for n in TSSNodeV2Dao.list_node_by_node_pks(
                sum(group_node_pk_map.values(), [])
            )
        }

        mobile_signer_groups = []
        for g in groups:
            group_nodes: List[TSSNodeV2] = [
                node_map.get(node_pk) for node_pk in group_node_pk_map.get(g.id)
            ]
            mobile_nodes = [
                n for n in group_nodes if n.node_type == TSSNodeTypeEnum.TYPE_MOBILE
            ]
            if not mobile_nodes:
                continue
            mobile_signer_groups.append(g)
        return mobile_signer_groups

    @classmethod
    def list_group_by_node(cls, node_pk: int, vault_id: int) -> List[TSSGroupV2]:
        groups = TSSGroupV2Dao.list_by_vault(
            vault_id,
            group_type=[
                TSSGroupTypeEnum.TYPE_MPC_OWNER,
                TSSGroupTypeEnum.TYPE_MPC_SIGNER,
            ],
            include_status=[TSSGroupStatusEnum.STATUS_ACTIVE],
        )
        res = []
        for group in groups:
            config = TSSGroupConfig.from_json(group.config)
            if node_pk in config.node_pks:
                res.append(group)
        return res

    @classmethod
    def list_org_group_by_node(cls, node_pk: int, org_id: int) -> List[TSSGroupV2]:
        groups = TSSGroupV2Dao.list_by_org(
            org_id,
            include_status=[TSSGroupStatusEnum.STATUS_ACTIVE],
        )
        res = []
        for group in groups:
            config = TSSGroupConfig.from_json(group.config)
            if config.node_pks and node_pk in config.node_pks:
                res.append(group)
        return res

    @classmethod
    @transaction.atomic
    def request_holder_confirm(cls, tss_request: TSSRequestV2, tss_node: TSSNodeV2):
        if not tss_node.user_id:
            return
        handler_auth_chain_user = (
            AuthchainUserSwitchManager.get_auth_pubkey_with_compare(
                org_id=tss_node.org_id,
                user_id=tss_node.user_id,
            )
        )
        if not handler_auth_chain_user or not handler_auth_chain_user.pubkey:
            raise CustodyApiException(
                CustodyApiException.ERROR_PERMISSION,
                "User has no owner permission",
            )

        vault = tss_request.mpc_vault
        initiator = ""
        if tss_request.user_id:
            request_creator = CustodyUserDao.get_by_id_or_null(tss_request.user_id)
            initiator = request_creator.email if request_creator else ""
        created_time = int(tss_request.created_time.timestamp()) * 1000
        expiration_time = created_time + 60 * 60 * 1000
        tss_group = tss_request.tss_group
        if tss_request.new_tss_group:
            tss_group = tss_request.new_tss_group

        tss_nodes = TSSNodeV2Dao.list_node_by_node_pks(
            TSSGroupV2Dao.get_node_pks(tss_group)
        )
        key_holders = []
        for node in tss_nodes:
            key_holders.append(
                {
                    "name": node.name,
                    "tss_node_id": node.node_id,
                    "type": "cobo"
                    if node.node_id == settings.COBO_TSS_NODE
                    else "client",
                    "client_type": "mobile"
                    if node.node_type == TSSNodeTypeEnum.TYPE_MOBILE
                    else "api",
                    "is_me": node.pk == tss_node.pk,
                }
            )
        ctx = json.dumps(
            {
                "title": "Become Key Holder",
                "vault": {
                    "id": vault.uuid,
                    "name": vault.name,
                    "type": vault.vault_type,
                    "org_name": vault.org.name,
                },
                "initiator": initiator,
                "created_time": created_time,
                "expiration_time": expiration_time,
                "event": "Become Key Holder",
                "key_group_type": cls.get_key_group_type_by_tss_group_type(
                    tss_group.group_type
                ).value,
                "key_group_id": tss_group.id,
                "quorum": f"{tss_group.threshold}/{tss_group.node_number}",
                "key_holders": key_holders,
                "environment": settings.ENV,
                "target_node_id": tss_node.id,
            }
        )

        (
            message,
            extra_message,
        ) = StatementBuilder.build_vault_become_key_holder_statement(ctx)
        statement = AuthStatementManager.submit_message(
            message=message,
            extra_message=extra_message,
            target_pubkey=handler_auth_chain_user.pubkey,
            statement_type=AuthStatementType.VAULT_BECOME_KEY_HOLDER,
            context=ctx,
            callback_url=f"{settings.DOMAIN_URL}/v1/custody/auth/callback/statement/",
        )
        TSSRequestV2ConfirmLogDao.create(
            tss_request=tss_request,
            tss_node=tss_node,
            statement_id=statement.id,
            result=TSSRequestConfirmResult.STATUS_NEW,
        )

    @classmethod
    @transaction.atomic
    def on_receive_auth_statement_signal(
        cls, sender, auth_statement_result: AuthStatementResult, **kwargs
    ):
        try:
            statement: AuthStatement = AuthStatementDao.get_by_id_or_null(
                auth_statement_result.statement_id
            )
            if statement.statement_type == AuthStatementType.VAULT_BECOME_KEY_HOLDER:
                cls._handle_tss_request_confirm(statement, auth_statement_result)
        except Exception as e:
            logger.error(
                f"receive_auth_statement_signal failed, "
                f"result:{auth_statement_result}, error:{e}",
            )
            raise e

    @classmethod
    @transaction.atomic
    def on_tss_request_signal(cls, sender, tss_request: TSSRequestV2, **kwargs):
        logger.info(f"on keygen signal: {tss_request}")
        try:
            if tss_request.status == TSSRequestStatusEnum.STATUS_SUCCESS:
                cls.on_tss_request_success(tss_request)
            elif tss_request.status == TSSRequestStatusEnum.STATUS_RELAY_RECEIVE:
                cls.on_tss_request_relay_received(tss_request)
            elif tss_request.status == TSSRequestStatusEnum.STATUS_FAILED:
                cls.on_tss_request_failed(tss_request)
        except Exception as e:
            logger.error(f"on_keygen_signal failed, error:{e}")

    @classmethod
    def on_tss_request_failed(cls, tss_request: TSSRequestV2):
        logger.info(f"on keygen failed: {tss_request}")
        cls._send_keygen_result_notifications(tss_request, TemplateType.KeyGenFailed)

    @classmethod
    @transaction.atomic
    def on_tss_request_success(cls, tss_request: TSSRequestV2):
        logger.info(f"on keygen success: {tss_request}")
        # vault keygen成功后，默认添加一个钱包，以及生产地址
        org = tss_request.org
        from waas2.mpc.processors.mpc import MPCWalletProcessor

        if (
            tss_request.mpc_vault.vault_type == MPCVaultType.OrgControlled
            and tss_request.request_type == TSSRequestTypeEnum.TYPE_PORTAL_KEY_GEN
            and tss_request.status == TSSRequestStatusEnum.STATUS_SUCCESS
        ):
            wallet_info = MPCWalletProcessor.create_wallet(
                WaaS2EOAWalletCreateBO(
                    org_id=tss_request.org.uuid,
                    biz_org_id=tss_request.org.id,
                    type=WalletType.MPC,
                    sub_type=WalletSubtype.OrgControlled,
                    name=f"Default wallet {str(int(now_ts()))[-5:]}",
                    vault_id=tss_request.mpc_vault.uuid,
                )
            )
            WalletSourceManager.create_ref(
                org_id=tss_request.org.uuid,
                wallet_type=WalletType.MPC,
                wallet_id=wallet_info.wallet_id,
                wallet_subtype=WalletSubtype.OrgControlled,
            )
            chain_ids = settings.PORTAL_MPC_DEFAULT_ADD_CHAINS
            if org.business_type == "payment":
                chain_ids = settings.PORTAL_MPC_PAYMENT_DEFAULT_ADD_CHAINS
            token_info_map = {
                t.token_id: t for t in WaaSTokenManager.list_tokens(token_ids=chain_ids)
            }
            custody_user = None
            if tss_request.user_id:
                custody_user = CustodyUserDao.get_by_id_or_null(tss_request.user_id)
            for chain_id in chain_ids:
                encoding = None
                token_info = token_info_map.get(chain_id)
                if token_info.is_utxo:
                    encoding = AddressEncoding.ENCODING_P2TR
                address = MPCWalletProcessor.generate_new_addresses(
                    WaaS2WalletCreateAddressBO(
                        ref_org_id=tss_request.org.uuid,
                        biz_org_id=tss_request.org.id,
                        wallet_id=wallet_info.wallet_id,
                        chain_id=chain_id,
                        chain=chain_id,
                        encodings=[encoding] if encoding else None,
                        custody_user=custody_user,
                        count=1,
                    )
                )
                # dev or sandbox 环境，自动发放测试币
                if (
                    (Env.ENV_SANDBOX() or Env.ENV_DEVELOP())
                    and address
                    and chain_id == COIN_SETH
                    and org.business_type != "payment"
                ):
                    from waas2.eoa.managers.dev_util import EOADevUtil

                    try:
                        EOADevUtil.send_test_coin(COIN_SETH, address[0].address, "0.01")
                    except Exception as e:
                        logger.warning(
                            f"wallet: {wallet_info.wallet_id}, failed to transfer coin, error {e}"
                        )

        cls._send_key_share_verification(tss_request)
        cls._send_keygen_result_notifications(tss_request, TemplateType.KeyGenSucceed)

    @classmethod
    def on_tss_request_relay_received(cls, tss_request: TSSRequestV2):
        if tss_request.status != TSSRequestStatusEnum.STATUS_RELAY_RECEIVE:
            return
        node_pks = []
        if tss_request.tss_group:
            node_pks += TSSGroupV2Dao.get_node_pks(tss_request.tss_group)
        if tss_request.new_tss_group:
            node_pks += TSSGroupV2Dao.get_node_pks(tss_request.new_tss_group)
        nodes = [
            n
            for n in TSSNodeV2Dao.list_node_by_node_pks(node_pks)
            if n.node_type == TSSNodeTypeEnum.TYPE_MOBILE
        ]
        title_map = {"en": "Key Share Pending Generation", "zh": "您有一份私钥分片需生成"}
        content_map = {
            "en": "Before generating the key share, please review the key share group information carefully. [View Details]",
            "zh": "生成前，请仔细检查私钥分片组的信息 【查看详情】",
        }
        user_map = {
            u.id: u for u in CustodyUserDao.list_by_ids([n.user_id for n in nodes])
        }
        user_uuids = []
        for node in nodes:
            user = user_map.get(node.user_id)
            user_uuids.append(user.uuid)
            handler_auth_chain_user = (
                AuthchainUserSwitchManager.get_auth_pubkey_with_compare(
                    org_id=node.org_id,
                    user_id=node.user_id,
                )
            )
            if not handler_auth_chain_user or not handler_auth_chain_user.pubkey:
                continue

            key = f"{tss_request.request_id}+{handler_auth_chain_user.pubkey}"
            try:
                # 消息有幂等处理，仅在消息并发回调时会给客户多次发送相同消息，所以超时时间设置为 60 可以防止并发场景
                with CoboLock(key, expire=60, timeout=1):
                    logger.info(
                        f"on_tss_request_relay_received try lock key:{key} for tss_node:{node.node_id} success"
                    )
                    CoboAuthManager.push_message(
                        handler_auth_chain_user.pubkey,
                        title=title_map[user.language],
                        content=content_map[user.language],
                    )
                    cls._send_keygen_notifications(tss_request, user_uuids, node_pks)
            except LockTimeOutError as e:
                logger.info(
                    f"on_tss_request_relay_received try lock key:{key} for tss_node:{node.node_id} Exception: {e}"
                )

    @classmethod
    def on_user_guard_changed(cls, user: CustodyUser, org: Organization):
        logger.info(f"on user guard changed: {user.email} in org {org.name}")
        cls._notify_on_group_not_operational(user, org)

    @classmethod
    def _notify_on_group_not_operational(cls, user: CustodyUser, org: Organization):
        if not send_portal_mpc_key_share_notification():
            return

        org_groups = TSSGroupV2Dao.list_by_org(
            org_id=org.id, exclude_status=[TSSGroupStatusEnum.STATUS_INACTIVE]
        )
        for group in org_groups:
            if user.uuid in MPCWalletUtils.get_key_holder_user_uuids_from_group(group):
                cls._send_group_not_operational_notification(user.uuid, group)

    @classmethod
    def _send_group_not_operational_notification(
        cls, user_uuid: str, group: TSSGroupV2
    ):
        logger.info(f"send group not operational notification for group {group}")
        try:
            format_params = {
                "org_name": group.org.name,
                "vault_name": group.mpc_vault.name,
                "group_type": MPCWalletUtils.get_key_group_type_by_tss_group_type(
                    group.group_type
                ).value,
                "group_id": group.uuid,
            }
            # notify key holder
            PortalNotifier.notify(
                org_uuid=group.org.uuid,
                template_type=TemplateType.GroupNotOperational,
                user_uuids=[user_uuid],
                **format_params,
            )
            # notify the specified roles in templates
            PortalNotifier.notify(
                org_uuid=group.org.uuid,
                template_type=TemplateType.GroupNotOperational,
                **format_params,
            )
        except Exception as e:
            logger.error(f"failed to send group not operational notification: {e}")

    @classmethod
    def _extract_notification_format_params(cls, tss_request: TSSRequestV2):
        return {
            "org_name": tss_request.org.name,
            "vault_name": tss_request.mpc_vault.name,
            "group_type": MPCWalletUtils.get_key_group_type_by_tss_group_type(
                tss_request.tss_group.group_type
            ).value,
            "group_id": tss_request.tss_group.uuid,
        }

    @classmethod
    def _send_keygen_notifications(
        cls, tss_request: TSSRequestV2, user_uuids: List[str], node_pks: List[int]
    ):
        if not send_portal_mpc_key_share_notification():
            return

        logger.info(f"send keygen notification for request {tss_request}")
        try:
            format_params = cls._extract_notification_format_params(tss_request)
            # notify Cobo Guard users via web message & email
            PortalNotifier.notify(
                org_uuid=tss_request.org.uuid,
                template_type=TemplateType.KeyGenFromGuardNode,
                user_uuids=user_uuids,
                **format_params,
            )
            # notify admin for API nodes via web message & email
            for n in TSSNodeV2Dao.list_node_by_node_pks(node_pks):
                if n.node_type == TSSNodeTypeEnum.TYPE_SERVER:
                    format_params["tss_node_id"] = n.node_id
                    PortalNotifier.notify(
                        org_uuid=tss_request.org.uuid,
                        template_type=TemplateType.KeyGenFromAPINode,
                        **format_params,
                    )
        except Exception as e:
            logger.error(f"failed to send keygen notification: {e}")

    @classmethod
    def _send_keygen_result_notifications(
        cls, tss_request: TSSRequestV2, template_type: TemplateType
    ):
        if not send_portal_mpc_key_share_notification():
            return

        logger.info(
            f"send keygen result notification with {template_type} for request {tss_request}"
        )
        try:
            format_params = cls._extract_notification_format_params(tss_request)
            # notify key holders
            PortalNotifier.notify(
                org_uuid=tss_request.org.uuid,
                template_type=template_type,
                user_uuids=cls._get_key_holder_user_uuids_from_request(tss_request),
                **format_params,
            )
            # notify the specified roles in templates
            PortalNotifier.notify(
                org_uuid=tss_request.org.uuid,
                template_type=template_type,
                **format_params,
            )
        except Exception as e:
            logger.error(f"failed to send keygen result notification: {e}")

    @classmethod
    def _get_key_holder_user_uuids_from_request(
        cls, tss_request: TSSRequestV2
    ) -> List[str]:
        node_pks = []
        if tss_request.tss_group:
            node_pks += TSSGroupV2Dao.get_node_pks(tss_request.tss_group)
        if tss_request.new_tss_group:
            node_pks += TSSGroupV2Dao.get_node_pks(tss_request.new_tss_group)
        return MPCWalletUtils.get_key_holder_user_uuids_from_node_pks(node_pks)

    @classmethod
    def _pack_group_info(
        cls, group: TSSGroupV2, used_key_group=True, request: TSSRequestV2 = None
    ):
        if not group:
            return None

        group_ids = None
        group_config = TSSGroupConfig.from_json(group.config)
        if group_config.group_ids:
            group_ids = list(group_config.group_ids.values())
        root_pub_keys = [
            p.pubkey for p in TSSRootPubkeyV2Dao.list_by_vault(group.mpc_vault.id)
        ]

        nodes = TSSNodeV2Dao.list_node_by_node_pks(TSSGroupV2Dao.get_node_pks(group))
        if used_key_group and request:
            param = BatchTSSReshareParam.from_json(request.param)
            nodes = [n for n in nodes if n.node_id in param.used_node_ids]
        user_map: Dict[int, CustodyUser] = {
            u.id: u for u in CustodyUserDao.list_by_ids([n.user_id for n in nodes])
        }
        return {
            "key_group_id": str(group.id),
            "key_group_type": cls.get_key_group_type_by_tss_group_type(
                group.group_type
            ),
            "nodes": [
                {
                    "node_id": n.node_id,
                    "node_type": n.node_type,
                    "account_id": user_map.get(n.user_id).oauth_user.account_user_id
                    if user_map.get(n.user_id)
                    else "",
                    "key_holder_name": n.name,
                    "description": "",
                }
                for n in nodes
            ],
            "group_ids": group_ids,
            "root_pub_keys": root_pub_keys,
        }

    @classmethod
    def _portal_meta(cls, request: TSSRequestV2):
        used_key_group = None
        if request.request_type == TSSRequestTypeEnum.TYPE_PORTAL_KEY_GEN:
            target_key_group = request.tss_group
        else:
            target_key_group = request.new_tss_group
            used_key_group = request.tss_group
        return {
            "org_id": request.org.uuid,
            "org_name": request.org.name,
            "vault_id": request.mpc_vault.uuid,
            "vault_name": request.mpc_vault.name,
            "vault_type": request.mpc_vault.vault_type,
            "env": settings.ENV,
            "tss_type": TSSType.TSSRequest.value,
            "tss_request_detail": {
                "tss_request_type": request.biz_type,
                "description": "Generate Prviate Key Share",
                "threshold": target_key_group.threshold,
                "used_key_group": cls._pack_group_info(
                    used_key_group, used_key_group=True, request=request
                ),
                "target_key_group": cls._pack_group_info(target_key_group, False),
            },
        }

    @classmethod
    def batch_generation_root_pubkey(cls, request: TSSRequestV2):
        check(request.request_type, TSSRequestTypeEnum.TYPE_PORTAL_KEY_GEN)
        keygen_param = BatchTSSKeygenParam.from_dict(json.loads(request.param))
        meta = keygen_param.meta
        if not meta:
            meta = {
                "cobo": {},
                "customer": {
                    "cobo_id": str(request.request_id),
                    "api_request_id": request.request_id,
                },
                "portal": json.dumps(cls._portal_meta(request)),
                "waas2": cls.get_waas2_meta_data(request),
            }
        try:
            BlockchainMPCClientManager.batch_generate_tss_root_pubkey(
                request_id=str(request.request_id),
                org_id=request.org.pk,
                threshold=request.tss_group.threshold,
                node_ids=keygen_param.node_ids,
                meta=meta,
                curves=keygen_param.curves,
                vault_id=request.mpc_vault.id if request.mpc_vault else 0,
            )
        except CustodyApiException as e:
            if e.error_code == CustodyException.ERROR_DUP_REQUEST_ID:
                logger.warning(
                    f"retry send generation_root_pubkey reqeust, request_id: {request.id}, org_id: {request.org_id}"
                )
            else:
                raise e

    @classmethod
    def batch_reshare(cls, request: TSSRequestV2):
        reshare_param = BatchTSSReshareParam.from_dict(json.loads(request.param))
        meta = reshare_param.meta
        if not meta:
            meta = {
                "cobo": {},
                "customer": {
                    "cobo_id": str(request.request_id),
                    "api_request_id": request.request_id,
                },
                "portal": json.dumps(cls._portal_meta(request)),
                "waas2": cls.get_waas2_meta_data(request),
            }
        try:
            BlockchainMPCClientManager.batch_tss_reshare(
                request_id=str(request.request_id),
                group_ids=reshare_param.group_ids,
                root_pubkeys=reshare_param.root_pubkeys,
                used_node_ids=reshare_param.used_node_ids,
                new_node_ids=reshare_param.new_node_ids,
                new_threshold=reshare_param.new_threshold,
                meta=meta,
            )
        except CustodyApiException as e:
            if e.error_code == CustodyException.ERROR_DUP_REQUEST_ID:
                logger.warning(
                    f"retry send reshare reqeust, request_id: {request.id}, org_id: {request.org_id}"
                )
            else:
                raise e

    @classmethod
    def is_key_group_backed_up(
        cls, tss_group: TSSGroupV2, nodes: Dict[int, TSSNodeV2] = None
    ):
        backed_up_node_pks = TSSGroupV2Dao.get_backed_up_node_pks(tss_group)
        node_pks = TSSGroupV2Dao.get_node_pks(tss_group)
        if nodes:
            tss_nodes = [nodes.get(pk) for pk in node_pks]
        else:
            tss_nodes = TSSNodeV2Dao.list_node_by_node_pks_include_inactive(node_pks)
        cobo_node = [
            n for n in tss_nodes if n.node_type == TSSNodeTypeEnum.TYPE_COBO.value
        ]
        if cobo_node:
            backed_up_node_pks = set(backed_up_node_pks)
            backed_up_node_pks.add(cobo_node[0].pk)
        return len(backed_up_node_pks) == tss_group.node_number

    @classmethod
    @bool_feature_switch(switch_name=SwitchNameEnum.MPC_KEY_SHARE_VERIFICATION)
    def _send_key_share_verification(cls, tss_request: TSSRequestV2):
        if tss_request.status != TSSRequestStatusEnum.STATUS_SUCCESS:
            return
        tss_group = None
        if tss_request.request_type == TSSRequestTypeEnum.TYPE_PORTAL_KEY_GEN:
            tss_group = tss_request.tss_group
        elif tss_request.request_type == TSSRequestTypeEnum.TYPE_PORTAL_RESHARE:
            tss_group = tss_request.new_tss_group
        if not tss_group:
            return
        nodes = TSSNodeV2Dao.list_node_by_node_pks_include_inactive(
            TSSGroupV2Dao.get_node_pks(tss_group)
        )
        from waas2.mpc.processors.key_share_verification import (
            MPCKeyShareVerificationProcessor,
        )

        for node in nodes:
            MPCKeyShareVerificationProcessor.create_verification(
                CreateKeyShareVerificationBo(
                    vault_id=tss_group.mpc_vault.uuid,
                    key_group_id=tss_group.uuid,
                    node_id=node.node_id,
                ),
            )

    @classmethod
    def _handle_tss_request_confirm(
        cls, statement: AuthStatement, auth_statement_result: AuthStatementResult
    ):
        confirm_log = TSSRequestV2ConfirmLogDao.get_by_statement_id(statement.id)
        if not confirm_log:
            return
        if confirm_log.result != TSSRequestConfirmResult.STATUS_NEW:
            return
        tss_request = confirm_log.tss_request
        if tss_request.status != TSSRequestStatusEnum.STATUS_CONFIRM:
            return
        confirm_result = None
        if auth_statement_result.result == AuthStatementSignResult.RESULT_APPROVED:
            confirm_result = TSSRequestConfirmResult.STATUS_SUCCESS
        elif auth_statement_result.result == AuthStatementSignResult.RESULT_TIMEOUT:
            confirm_result = TSSRequestConfirmResult.STATUS_TIMEOUT
        elif auth_statement_result.result == AuthStatementSignResult.RESULT_DECLINED:
            confirm_result = TSSRequestConfirmResult.STATUS_REJECT
        if confirm_result:
            TSSRequestV2ConfirmLogDao.update_by_id_directly(
                confirm_log.id, result=confirm_result
            )
            if confirm_result in [
                TSSRequestConfirmResult.STATUS_REJECT,
                TSSRequestConfirmResult.STATUS_TIMEOUT,
            ]:
                TSSRequestV2Dao.update_by_id_directly(
                    tss_request.id, status=TSSRequestStatusEnum.STATUS_CONFIRM_FAILED
                )
                CoboAuthManager.remove_statement(statement.msghash)

    @classmethod
    def get_holder_type_by_node_type(cls, node_type: Union[TSSNodeTypeEnum, int]):
        if isinstance(node_type, int):
            node_type = TSSNodeTypeEnum(node_type)
        holder_type = None
        if node_type == TSSNodeTypeEnum.TYPE_COBO:
            holder_type = KeyHolderType.COBO
        elif node_type in [
            TSSNodeTypeEnum.TYPE_MOBILE,
            TSSNodeTypeEnum.TYPE_MOBILE_BACKUP,
        ]:
            holder_type = KeyHolderType.MOBILE
        elif node_type in [TSSNodeTypeEnum.TYPE_SERVER, TSSNodeTypeEnum.TYPE_THIRD]:
            holder_type = KeyHolderType.API
        return holder_type

    @classmethod
    def _get_node_type_by_holder_type(cls, holder_type: Union[KeyHolderType, str]):
        if isinstance(holder_type, int):
            holder_type = KeyHolderType.get_type_by_name(holder_type)
        node_type = None
        if holder_type == KeyHolderType.COBO:
            node_type = TSSNodeTypeEnum.TYPE_COBO
        elif holder_type == KeyHolderType.MOBILE:
            node_type = TSSNodeTypeEnum.TYPE_MOBILE
        elif holder_type == KeyHolderType.API:
            node_type = TSSNodeTypeEnum.TYPE_SERVER
        return node_type

    @classmethod
    def _get_tss_group_type_by_key_group_type(
        cls, key_group_type: Union[KeyGroupType, str], default_signing=True
    ):
        if isinstance(key_group_type, str):
            key_group_type = KeyGroupType.get_type_by_name(key_group_type)
        tss_group_type = None
        if key_group_type == KeyGroupType.MainKeyGroup:
            tss_group_type = TSSGroupTypeEnum.TYPE_MPC_OWNER
        elif key_group_type == KeyGroupType.SigningKeyGroup:
            if default_signing:
                tss_group_type = TSSGroupTypeEnum.TYPE_MPC_SIGNER
            else:
                tss_group_type = [
                    TSSGroupTypeEnum.TYPE_MPC_SIGNER,
                    TSSGroupTypeEnum.TYPE_MPC_SERVER,
                ]
        elif key_group_type == KeyGroupType.RecoveryKeyGroup:
            tss_group_type = TSSGroupTypeEnum.TYPE_MPC_BACKUP
        return tss_group_type

    @classmethod
    def get_key_group_type_by_tss_group_type(
        cls, tss_group_type: Union[TSSGroupTypeEnum, int]
    ):
        if isinstance(tss_group_type, int):
            tss_group_type = TSSGroupTypeEnum(tss_group_type)
        key_group_type = None
        if tss_group_type == TSSGroupTypeEnum.TYPE_MPC_OWNER:
            key_group_type = KeyGroupType.MainKeyGroup
        elif tss_group_type in [
            TSSGroupTypeEnum.TYPE_MPC_SIGNER,
            TSSGroupTypeEnum.TYPE_MPC_SERVER,
        ]:
            key_group_type = KeyGroupType.SigningKeyGroup
        elif tss_group_type == TSSGroupTypeEnum.TYPE_MPC_BACKUP:
            key_group_type = KeyGroupType.RecoveryKeyGroup
        return key_group_type

    @classmethod
    def _get_user_default_vault(cls, custody_user: CustodyUser) -> MPCVault:
        default_vault = UserDefaultMPCVaultDao.get_by_user(custody_user.id)
        if default_vault:
            return default_vault.mpc_vault
        vaults = MPCVaultDao.list_by_org(
            org_id=custody_user.org.pk, vault_type=MPCVaultType.OrgControlled
        )
        if vaults:
            return vaults[0]

    @classmethod
    def _pack_vaults(
        cls, vaults: List[MPCVault], custody_user: CustodyUser = None
    ) -> List[MPCVaultBo]:
        res = []
        if len(vaults) == 0:
            return res

        pubkeys = TSSRootPubkeyV2Dao.list_by_vaults([v.id for v in vaults])
        vault_pubkey_map = defaultdict(list)
        for p in pubkeys:
            vault_pubkey_map[p.mpc_vault.id].append(p)

        default_vault = None
        if custody_user:
            default_vault = cls._get_user_default_vault(custody_user)

        key_groups = TSSGroupV2Dao.list_by_vault(
            [v.id for v in vaults],
            group_type=TSSGroupTypeEnum.TYPE_MPC_OWNER,
            exclude_status=[TSSGroupStatusEnum.STATUS_INACTIVE],
        )
        key_group_map = defaultdict(list)
        for key_group in key_groups:
            key_group_map[key_group.mpc_vault.id].append(key_group)

        nodes_pks = []
        for g in key_groups:
            nodes_pks += TSSGroupV2Dao.get_node_pks(g)
        node_map = {
            n.pk: n
            for n in TSSNodeV2Dao.list_node_by_node_pks_include_inactive(nodes_pks)
        }

        relations = CustodyWalletMPCVaultRelationDao.list_vault_wallets(
            [v.id for v in vaults]
        )
        vault_wallet_map = defaultdict(list)
        for r in relations:
            vault_wallet_map[r.mpc_vault.id].append(r.custody_wallet)

        wallet_balances = BalanceManager.get_wallet_balances(
            org_uuid=vaults[0].org.uuid,
            org_id=vaults[0].org.pk,
            wallet_ids=[r.custody_wallet.id for r in relations],
            use_available=False,
        )
        tokens = []
        for wallet_id, wallet_balance in wallet_balances.items():
            tokens.extend(wallet_balance.filter_token_assets())

        wallet_token_map = defaultdict(list)
        for t in tokens:
            wallet_token_map[t.custody_wallet_id].append(t)
        coin_rates = CoinCurrencyRateManager.get_rates(
            [t.asset_code for t in tokens], currency=settings.CURRENCY_USD
        )
        for v in vaults:
            vault_pubkeys = vault_pubkey_map.get(v.id, [])
            wallets = vault_wallet_map.get(v.id, [])
            vault_tokens: List[TokenAsset] = []

            # screening_balance = ScreeningBalanceBO()
            for w in wallets:
                vault_tokens.extend(wallet_token_map.get(w.id, []))
                wallet_balance = wallet_balances.get(w.id)
                wallet_balance.add_coin_rates(coin_rates=coin_rates)
                # screening_balance += wallet_balance.get_screening_balance()

            last_activity_timestamp = 0
            currency_value = Decimal(0)
            deduplication_asset_code = set()
            no_zero_token_assets = []
            for t in vault_tokens:
                coin_info = CustodyCoinManager.get_asset_coin(
                    t.asset_code, nullable=True
                )
                if not coin_info:
                    continue
                currency_value += MPCWalletUtils.calc_coin_value(
                    coin_rates.get(t.asset_code), coin_info, int(t.balance)
                )
                last_activity_timestamp = max(
                    last_activity_timestamp, int(t.modified_time.timestamp()) * 1000
                )
                if t.balance > 0:
                    no_zero_token_assets.append(t)
                deduplication_asset_code.add(t.asset_code)

            backed_up = False
            vault_main_key_groups = key_group_map.get(v.id, [])
            check_backed_up = False
            if len(vault_main_key_groups) == 1:
                if cls.is_key_group_backed_up(vault_main_key_groups[0], node_map):
                    backed_up = True
                group_config = TSSGroupConfig.from_json(vault_main_key_groups[0].config)
                check_backed_up = group_config.check_backed_up
            if OrganizationConfigManager.is_portal_demo_org_by_org(v.org):
                check_backed_up = False

            vault_bo = MPCVaultBo(
                id=v.uuid,
                name=v.name,
                type=v.vault_type,
                root_pubkeys=[
                    RootPubkeyBo(
                        pubkey=p.pubkey,
                        curve=CurveType.from_int(int(p.curve_type)).name,
                    )
                    for p in vault_pubkeys
                ],
                has_main_key_group=bool(key_group_map.get(v.id, [])),
                wallets=len(wallets),
                currency_value=currency_value,
                create_timestamp=int(v.created_time.timestamp()) * 1000,
                last_activity_timestamp=last_activity_timestamp,
                default=default_vault and default_vault.id == v.id,
                backed_up=backed_up,
                check_backed_up=check_backed_up,
                tokens=len(deduplication_asset_code),
                no_zero_balance_tokens=[],
                # screening_balance=screening_balance,
            )
            res.append(vault_bo)

        res = sorted(res, key=lambda v: (not v.default, v.id))
        return res

    @classmethod
    def _pack_key_group(
        cls,
        tss_group: TSSGroupV2,
        node_online_map: Dict[str, bool] = None,
        account_map: Dict[int, str] = None,
        guard_enable_map: dict[str, bool] = None,
        user_image_map: dict[str, str] = None,
    ) -> TSSGroupKeyBo:
        if not node_online_map:
            node_online_map = {}
        if not account_map:
            account_map = {}
        status = KeyGroupStatus.New
        if tss_group.status == TSSGroupStatusEnum.STATUS_ACTIVE:
            status = KeyGroupStatus.Valid
        elif tss_group.status == TSSGroupStatusEnum.STATUS_WAIT_BY_RESHARE:
            status = KeyGroupStatus.Unavailable
        group_key = TSSGroupKeyBo(
            id=tss_group.uuid,
            type=cls.get_key_group_type_by_tss_group_type(tss_group.group_type).value,
            node_number=tss_group.node_number,
            threshold=tss_group.threshold,
            status=status,
            created_timestamp=int(tss_group.created_time.timestamp()) * 1000,
        )
        group_config = TSSGroupConfig.from_json(tss_group.config)
        if group_config.group_ids:
            group_ids = []
            for curve, group_id in group_config.group_ids.items():
                group_ids.append(
                    TSSGroupIdBo(
                        curve=CurveType.from_int(int(curve)).name, group_id=group_id
                    )
                )
            group_key.tss_group_ids = group_ids
        backed_up_node_pks = []
        if group_config.backed_up_node_pks:
            backed_up_node_pks = set(group_config.backed_up_node_pks)
        if group_config.node_pks:
            tss_nodes = TSSNodeV2Dao.list_node_by_node_pks_include_inactive(
                group_config.node_pks
            )
            tss_node_map = {t.pk: t for t in tss_nodes}
            holders = []
            for node_pk in group_config.node_pks:
                node = tss_node_map.get(node_pk)
                status = KeyHolderStatus.Valid
                if node.status == TSSNodeStatusEnum.STATUS_INACTIVE:
                    status = KeyHolderStatus.Unavailable
                # guard还未配置激活，无法接收消息
                elif (
                    node.user_id
                    and guard_enable_map
                    and not guard_enable_map.get(str(node.user_id))
                ):
                    status = KeyHolderStatus.NotConfigured
                holders.append(
                    TSSKeyHolderBo(
                        name=node.get_name(node_name=group_config.node_names),
                        tss_node_id=node.node_id,
                        account_id=account_map.get(node.user_id, None),
                        online=node_online_map.get(node.node_id, None)
                        == TssNodeStatus.StatusRegisterOnline,
                        type=cls.get_holder_type_by_node_type(node.node_type).value,
                        status=status,
                        user_logo=user_image_map.get(str(node.user_id), None)
                        if user_image_map
                        else None,
                        backed_up=node.pk in backed_up_node_pks
                        or node.node_type == TSSNodeTypeEnum.TYPE_COBO.value,
                        signing_node=node.pk in group_config.signer_node_pks
                        if group_config.signer_node_pks
                        else False,
                    )
                )
            group_key.key_holders = holders
        return group_key

    @classmethod
    def _convert_to_tss_request_enum(
        cls, request_type: TSSRequestType
    ) -> TSSRequestTypeEnum:
        if request_type == TSSRequestType.KEYGEN:
            return TSSRequestTypeEnum.TYPE_PORTAL_KEY_GEN
        elif request_type in [
            TSSRequestType.KeyGenFromKeyGroup,
            TSSRequestType.RECOVERY,
        ]:
            return TSSRequestTypeEnum.TYPE_PORTAL_RESHARE

    @classmethod
    def _parse_tss_request_fail_detail(
        cls, tss_request: TSSRequestV2
    ) -> Optional[KeyGeneratingFailedDetail]:
        if tss_request.status != TSSRequestStatusEnum.STATUS_FAILED:
            return
        try:
            reason = EOAMPCCallbackFailedReason.from_json(tss_request.failed_reason)
            if reason.failed_type == EOAMPCFailedType.TYPE_CUSTODY_FAILED:
                return KeyGeneratingFailedDetail(fail_reason=reason.detail)
            elif reason.failed_type in [
                EOAMPCFailedType.TYPE_TSS_DECLINED,
                EOAMPCFailedType.TYPE_TSS_CANCELED,
                EOAMPCFailedType.TYPE_TSS_FAILED,
            ]:
                message = ""
                not_response_node_ids = set()
                for node_id, err_str in reason.detail.items():
                    not_response_node_ids.add(node_id)
                    if reason.failed_type == EOAMPCFailedType.TYPE_TSS_FAILED:
                        message = "System error"
                    else:
                        try:
                            err_info = json.loads(err_str)
                            message = err_info.get("cause")
                            if not message:
                                message = err_info.get("text")
                        except ValueError:
                            message = err_str
                node_pks = TSSGroupV2Dao.get_node_pks(tss_request.tss_group)
                if tss_request.new_tss_group:
                    node_pks.extend(
                        TSSGroupV2Dao.get_node_pks(tss_request.new_tss_group)
                    )
                tss_nodes = TSSNodeV2Dao.list_node_by_node_pks_include_inactive(
                    node_pks
                )
                holder_detail = []
                for tss_node in tss_nodes:
                    holder_detail.append(
                        KeyHolderResponse(
                            holder_name=tss_node.name,
                            tss_node_id=tss_node.node_id,
                            response=tss_node.node_id not in not_response_node_ids,
                        )
                    )
                return KeyGeneratingFailedDetail(
                    fail_reason=message, holder_detail=holder_detail
                )
            else:
                return KeyGeneratingFailedDetail(fail_reason="System error")
        except Exception as e:
            logger.warning(
                f"parse tss request: {tss_request} failed reason failed: {e}"
            )

    @classmethod
    def _pack_tss_request(
        cls,
        tss_request: TSSRequestV2,
        key_holder_confirm_logs: List[TSSRequestV2ConfirmLog],
        user_image_map: Dict[str, str] = None,
    ):
        fail_detail = None
        status = ""
        if tss_request.status == TSSRequestStatusEnum.STATUS_CONFIRM:
            status = "PendingKeyHolderConfirmation"
        elif tss_request.status == TSSRequestStatusEnum.STATUS_CONFIRM_FAILED:
            status = "KeyHolderConfirmationFailed"
        elif tss_request.status in [
            TSSRequestStatusEnum.STATUS_NEW,
            TSSRequestStatusEnum.STATUS_WAIT_RISKCONTROL,
            TSSRequestStatusEnum.STATUS_WAIT_SEND_TO_BC,
            TSSRequestStatusEnum.STATUS_SENT,
            TSSRequestStatusEnum.STATUS_RELAY_RECEIVE,
        ]:
            status = "KeyGenerating"
        elif tss_request.status == TSSRequestStatusEnum.STATUS_FAILED:
            status = "KeyGeneratingFailed"
            fail_detail = cls._parse_tss_request_fail_detail(tss_request)
        elif tss_request.status == TSSRequestStatusEnum.STATUS_SUCCESS:
            status = "SUCCESS"

        return TSSRequestBo(
            id=str(tss_request.id),
            type=tss_request.biz_type,
            status=status,
            key_holder_confirmation_detail=[
                KeyHolderConfirmation(
                    holder_name=confirm_log.tss_node.name,
                    tss_node_id=confirm_log.tss_node.node_id,
                    confirmed=confirm_log.result
                    == TSSRequestConfirmResult.STATUS_SUCCESS,
                    user_logo=user_image_map.get(confirm_log.tss_node.user_id, None)
                    if user_image_map
                    else None,
                )
                for confirm_log in key_holder_confirm_logs
            ],
            key_generating_failed_detail=fail_detail,
        )

    @classmethod
    def _check_vault_name(cls, name: str):
        check(
            name,
            CustodyApiException,
            CustodyException.ERROR_INVALID_PARAM,
            "vault name can not be empty",
        )
        check(
            len(name) <= 30,
            CustodyApiException,
            CustodyException.ERROR_INVALID_PARAM,
            "vault name too long",
        )
        invalid_token = ["+", "-", "=", "@"]
        for token in invalid_token:
            check(
                token not in name,
                CustodyApiException,
                CustodyException.ERROR_INVALID_PARAM,
                "vault name can not contain +、-、=、@",
            )

    @classmethod
    def get_waas2_meta_data(cls, tss_request: TSSRequestV2) -> dict:
        org = tss_request.org

        from waas2.mpc.processors.dev.mpc_project import MPCProjectProcessor
        from waas2.mpc.processors.dev.mpc_vault import MPCVaultProcessor

        vault = MPCVaultProcessor.get_vault_by_id(org.id, tss_request.mpc_vault.uuid)
        project = None
        if vault.project_id:
            project = MPCProjectProcessor.get_project(org.id, vault.project_id)

        tss_request = MPCVaultProcessor.get_tss_request(
            org.id, vault.vault_id, tss_request.request_id
        )

        source_key_share_holder_group = None
        if (
            tss_request.source_key_share_holder_group
            and tss_request.source_key_share_holder_group.key_share_holder_group_id
        ):
            source_key_share_holder_group = MPCVaultProcessor.get_key_group(
                org.id,
                vault.vault_id,
                tss_request.source_key_share_holder_group.key_share_holder_group_id,
            )
        target_key_share_holder_group = MPCVaultProcessor.get_key_group(
            org.id, vault.vault_id, tss_request.target_key_share_holder_group_id
        )
        return {
            "org": {
                "org_id": org.uuid,
                "name": org.name,
                "created_timestamp": int(org.created_time.timestamp()) * 1000,
            },
            "project": project.to_dict() if project else None,
            "vault": vault.to_dict(),
            "tss_request": tss_request.to_dict(),
            "source_key_share_holder_group": source_key_share_holder_group.to_dict()
            if source_key_share_holder_group
            else None,
            "target_key_share_holder_group": target_key_share_holder_group.to_dict(),
        }

    @classmethod
    @transaction.atomic
    def setup_default_vault(cls, org_id: int):
        if not Env.ENV_DEVELOP() and not Env.ENV_SANDBOX():
            return
        exist_vault = MPCVaultDao.list_by_org(
            org_id, vault_type=MPCVaultType.OrgControlled
        )
        if exist_vault:
            return
        vault_bo = cls.create_vault(
            org_id=org_id,
            name="Cobo Demo Vault (Testing Only)",
            vault_type=MPCVaultType.OrgControlled,
            default=True,
        )
        key_group = cls.create_key_group(
            org_id=org_id,
            vault_uuid=vault_bo.id,
            create_bo=CreateTSSGroupKeyBo(
                type=KeyGroupType.MainKeyGroup.value,
                node_number=2,
                threshold=2,
                key_holders=[
                    TSSKeyHolderBo(
                        name="demo node",
                        type=KeyHolderType.API.name,
                        tss_node_id=settings.DEMO_ORG_TSS_NODE,
                    ),
                ],
            ),
            auto_backup=True,
        )

        cls.create_tss_request(
            create_bo=CreateTSSRequestBo(
                vault_id=vault_bo.id,
                type=TSSRequestType.KEYGEN,
                target_key_group_id=key_group.id,
            )
        )
