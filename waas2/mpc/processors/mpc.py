import json
import logging
from typing import Dict, List, Optional, Tuple, Union

from _decimal import Decimal
from bc_waas2 import (
    DestinationTypeEnum,
    EOAFeeDestinationAccountRequest,
    EOAFeeDestinationUTXORequest,
    EOAFeeSourceAccountRequest,
    EOAFeeSourceUTXORequest,
    EOAPreTransferDestinationAccountRequest,
    EOAPreTransferDestinationUTXORequest,
    EOAPreTransferSourceAccountRequest,
    EOAPreTransferSourceUTXORequest,
    EOATransferDestinationUTXOOutputRequest,
    SourceTypeEnum,
)
from cobo_libs.api.restful.exceptions import ApiInvalidParamException, ApiWaaS2Exception
from cobo_libs.coin.codes import COIN_BTC, COIN_ETH, COIN_TRON, COIN_TTRON
from cobo_libs.utils.exceptions.tools import check
from cobo_libs.utils.time.time import dt_to_ms, ts_to_dt
from django.conf import settings
from django.db import transaction
from rest_framework.status import HTTP_401_UNAUTHORIZED

from custody.api.exception import CustodyException, CustodyWebException
from custody.cobo.utils import (
    calculate_partition_offset_limit,
    combine_string_to_decimal,
    divide_decimal_to_string,
    paginate_by_list,
    paging,
    paging_by_offset,
)
from custody.coin.data.objects import AssetCoinInfo
from custody.coin.managers import CustodyCoinManager, SuspendCoinManager
from custody.custody.dao.address import CustodyWalletCoinAddressDao
from custody.custody.dao.custody_user import CustodyUserDao
from custody.custody.dao.custody_wallet import (
    CustodyWalletDao,
    CustodyWalletMPCVaultRelationDao,
)
from custody.custody.dao.mpc_vault import MPCVaultDao
from custody.custody.dao.organization import OrganizationDao
from custody.custody.dao.transaction import EOATransactionDetailDao
from custody.custody.dao.tss_node_v2 import TSSGroupV2Dao
from custody.custody.dao.withdraw_request import WithdrawRequestDao
from custody.custody.data.enums import (
    AutoFuelType,
    EOAEventDirection,
    FeeModelType,
    SignVersionEnum,
    TSSGroupStatusEnum,
    TSSGroupTypeEnum,
)
from custody.custody.exceptions import CustodyApiException
from custody.custody.managers.blockchain import (
    BlockchainManager,
    BlockchainMPCClientManager,
)
from custody.custody.managers.config import CustodyWalletConfigManager
from custody.custody.managers.custody_wallet import CustodyWalletManager
from custody.custody.managers.custody_wallet.mpc import MPCCustodyWalletManager
from custody.custody.managers.requests.withdraw.mpc import MPCWithdrawRequestManager
from custody.custody.managers.tools import CustodyTools
from custody.custody.models.custody_user import CustodyUser
from custody.custody.models.custody_wallet import CustodyWallet
from custody.custody.services.event import WalletEventService
from custody.custody.utils.sign_message import validate_structured_data
from custody.custody.utils.switch import mpc_list_balance_details_sum_all_token_balance
from custody.marketdata.controllers import (
    CoinCurrencyRateManager,
    FiatCurrencyRateController,
)
from custody.wallet.dao.address import MPCWalletAddressExtraInfoDao, WalletAddressDao
from custody.wallet.managers.address import WalletAddressManager
from custody.wallet.models.address import WalletAddress
from custody.wallet.models.wallet import TokenAsset
from custody.wallet.utils import coin_requires_memo
from custody.web3.controllers.transaction_request import TransactionRequestController
from custody.web3.dao.transaction_request import TransactionRequestDao
from custody.web3.data.enums import TransactionRequestOperation, TransactionRequestType
from custody.web3.data.objects import MPCExtraParameters
from custody.web3.managers.balance.manager import BalanceManager
from waas2.address.dao.address import WalletChainIdentifierAddressDao
from waas2.address.enums.address import AddressSource
from waas2.address.models.address import WalletChainIdentifierAddress
from waas2.address_book.processors.address_book import AddressBookBizProcessor
from waas2.auto_sweep.dao.address import AutoSweepCollectionAddressDao
from waas2.auto_sweep.processor.address import AutoSweepCollectAddressProcessor
from waas2.auto_sweep.processor.balance import AutoSweepBalanceProcessor
from waas2.auto_sweep.processor.policy import AutoSweepPolicyProcessor
from waas2.base.bo.asset import (
    Asset,
    AssetMeta,
    ChainMeta,
    Coin,
    CoinMeta,
    WalletTokenBalance,
)
from waas2.base.bo.common import (
    PaginationByPage,
    WaaS2BalanceDetailBO,
    WaaS2QueryBalanceDetailBO,
)
from waas2.base.bo.transactions import (
    CancelTransactionBO,
    EstimateFeeBO,
    FeeAmountBO,
    FeeDetailBO,
    FeeEIP1559ParamBO,
    FeeLegacyParamBO,
    RBFTransactionBO,
    ResendTransactionBO,
    TransactionCreateBO,
    TransferToBO,
    TransferToParamBO,
    Web3SignMessageBO,
    Web3TransactionBO,
    Web3TransactionCreateBO,
)
from waas2.base.bo.wallet import (
    ExtendedChainInfo,
    FeeDetail,
    MaxTransferableValue,
    MaxTransferableValueBO,
    QueryWalletTokensRequestBO,
    QueryWalletTokensResponseBO,
    WaaS2QuerySupportedAssetsBO,
    WaaS2QuerySupportedChainsBO,
    WaaS2QueryTokenAddressBO,
    WaaS2QueryWalletAddressBO,
    WaaS2QueryWalletAssetsBO,
    WaaS2QueryWalletBO,
    WaaS2QueryWalletDetailBO,
    WaaS2UpdateWalletAddressBO,
    WaaS2WalletAddressBO,
    WaaS2WalletAddressSummaryBO,
    WaaS2WalletBaseSummaryBO,
    WaaS2WalletCreateAddressBO,
    WaaS2WalletDetailBO,
    WaaS2WalletListBO,
    WaaS2WalletTokenAddressBO,
    WaaS2WalletTypeSummaryBO,
    WalletAddressSummaryResponseBO,
    WalletForbidden,
)
from waas2.base.bo.wallets.common import (
    WaaS2OcwWalletByOrgIdBO,
    WaaS2QueryWalletSummaryByWalletTypeBO,
    WaaS2UcwWalletByOrgIdBO,
    WaaS2WalletAssetTokensBO,
    WaaS2WalletAssetTokensQueryBO,
    WaaS2WalletByOrgIdBO,
    WaaS2WalletByWalletAddressBO,
    WaaS2WalletByWalletIdBO,
)
from waas2.base.bo.wallets.custodial import (
    WaaS2CustodialTopAssetBO,
    WaaS2CustodialWalletListBO,
)
from waas2.base.bo.wallets.mpc import WaaS2EOAWalletCreateBO
from waas2.base.dao.wallet_source import WalletSourceDao
from waas2.base.enums.common import AddressEncoding, SortBy, SortDirection
from waas2.base.enums.transfer import TransferToSupportedType
from waas2.base.enums.wallet import WalletSubtype, WalletType
from waas2.base.exceptions.exceptions import (
    ApiExceptionCode,
    ApiExistedInvalidParamException,
    ApiInvalidActionException,
)
from waas2.base.exceptions.transfer import PortalTransferException
from waas2.base.managers.blockchain.blockchain import BlockchainFeeManagerEOAMPC
from waas2.base.managers.blockchain.entity import FeeDetail as BlockChainEntityFeeDetail
from waas2.base.managers.blockchain.interface import (
    EstimateRBFTxFeeDetailParams,
    EstimateRBFTxFeeParams,
    EstimateTxFeeDetailParams,
    EstimateTxFeeParams,
    GetMaxTransferableAmountParams,
    SourceDestination,
)
from waas2.base.managers.wallet_source import WalletSourceManager
from waas2.base.processors.wallets.commons import BaseWalletProcessor
from waas2.base.processors.wallets.interface import WalletProcessor
from waas2.base.views.utils import get_address_memo
from waas2.coins.bo import WrappedToken
from waas2.coins.bo_v2 import WaaSChainInfo
from waas2.coins.managers_v2 import WaaSChainManager, WaaSTokenManager
from waas2.coins.utils_v2 import WaaSChainUtils, WaaSTokenUtils
from waas2.custodial.processors.utils import CustodialUtils
from waas2.data.managers.package import OrgPackageManager
from waas2.eoa.managers.eoa_address_manager import EOAAddressManager
from waas2.eoa.managers.eoa_fee_model_manager import EOAFeeModelManager
from waas2.fee_station.enums.common import PaymentPreferenceValue
from waas2.fee_station.managers.fee_station_manager import FeeStationManagerV2
from waas2.mpc.enums.mpc_vault import MPCVaultType
from waas2.mpc.processors.dev.utils import MPCWalletUtils as MPCWalletDevUtils
from waas2.mpc.processors.transaction import MPCWalletTransactionProcessor
from waas2.mpc.processors.utils import MPCWalletUtils
from waas2.transaction_query.bo.transaction import (
    WaaS2QueryTransactionExtraBO,
    WaaS2TransactionDetailBO,
    WaaS2TransactionExtraBO,
    WaaS2UpdateTransactionBO,
)
from waas2.transaction_query.bo.transaction_query import QueryTransactionRecord
from waas2.transaction_query.enums import QueryTransactionSourceType
from waas2.transaction_query.managers.transaction_query import TransactionQueryManager
from waas2.transactions.dev.bo.transaction_fee import (
    TransactionFeeFIL,
    TransactionFeeSOL,
)
from waas2.transactions.dev.enums.fee_type import FeeType
from waas2.transactions.mongo.manager.transaction_client import TransactionMongoClient
from waas2.transactions.mongo.models.transaction import TransactionDetailData

logger = logging.getLogger("app.custody")

ALL_EVM = "All EVM Networks"


class MPCWalletProcessor(WalletProcessor):
    @classmethod
    def _search_custody_wallets(
        cls, org_id: int, balance_detail: WaaS2QueryBalanceDetailBO
    ):
        searched_custody_wallets = []
        if balance_detail.wallet_id:
            custody_wallet = CustodyWalletDao.get_by_uuid(balance_detail.wallet_id)
            custody_wallets = [custody_wallet] if custody_wallet else []
            return searched_custody_wallets, custody_wallets

        if balance_detail.vault_id:
            mpc_vault = MPCVaultDao.get_by_uuid(balance_detail.vault_id)
            if not mpc_vault:
                raise CustodyApiException(
                    CustodyWebException.ERROR_INVALID_PARAM,
                    f"vault {balance_detail.vault_id} not exist",
                )
            custody_wallets = [
                r.custody_wallet
                for r in CustodyWalletMPCVaultRelationDao.list_vault_wallets(
                    mpc_vault.id
                )
            ]
            if balance_detail.keyword:
                searched_custody_wallets = [
                    w for w in custody_wallets if balance_detail.keyword in w.name
                ]
                if searched_custody_wallets:
                    custody_wallets = searched_custody_wallets
            return searched_custody_wallets, custody_wallets

        if balance_detail.keyword:
            searched_vaults = MPCVaultDao.search_by_org_and_name(
                org_id=org_id, name=balance_detail.keyword
            )
            if searched_vaults:
                searched_custody_wallets = [
                    r.custody_wallet
                    for r in CustodyWalletMPCVaultRelationDao.list_vault_wallets(
                        [v.id for v in searched_vaults]
                    )
                ]
            if not searched_custody_wallets:
                searched_custody_wallets = list(
                    CustodyWalletDao.queryset_by_org_id_config_type(
                        org_id=org_id,
                        config_type=[CustodyWallet.WT_MPC, CustodyWallet.WT_MPC_WEB3],
                        name=balance_detail.keyword,
                    )
                )
            if searched_custody_wallets:
                custody_wallets = searched_custody_wallets
                return searched_custody_wallets, custody_wallets

        custody_wallets = CustodyWalletDao.list_by_org_id_config_type(
            org_id=org_id,
            config_type=[CustodyWallet.WT_MPC, CustodyWallet.WT_MPC_WEB3],
        )
        return searched_custody_wallets, custody_wallets

    @classmethod
    def list_balance_details(
        cls, balance_detail: WaaS2QueryBalanceDetailBO
    ) -> Tuple[List[WaaS2BalanceDetailBO], PaginationByPage]:
        org = OrganizationDao.query_by_uuid(balance_detail.org_id)
        searched_custody_wallets, custody_wallets = cls._search_custody_wallets(
            org.id, balance_detail
        )
        address_source = balance_detail.source if balance_detail.source else None

        # 处理scope过滤
        if balance_detail.scope:
            sources = WalletSourceManager.list_by_scope_params_and_filter(
                org_id=balance_detail.org_id,
                scoped_wallet_types=balance_detail.scope.wallet_types,
                scoped_wallet_subtypes=balance_detail.scope.wallet_subtypes,
                scoped_project_ids=balance_detail.scope.project_ids,
                scoped_vault_ids=balance_detail.scope.vault_ids,
                scoped_wallet_ids=balance_detail.scope.wallet_ids,
                query_wallet_types=[WalletType.MPC],
            )
            wallet_id_in_scope = {s.wallet_id for s in sources}

            searched_custody_wallets = [
                w for w in searched_custody_wallets if w.uuid in wallet_id_in_scope
            ]
            custody_wallets = [
                w for w in custody_wallets if w.uuid in wallet_id_in_scope
            ]

        custody_wallet_vault = {
            r.custody_wallet_id: r.mpc_vault
            for r in CustodyWalletMPCVaultRelationDao.list_vault_by_wallet(
                [w.id for w in custody_wallets]
            )
        }

        if balance_detail.wallet_subtype:
            target_vault_type = (
                MPCVaultType.OrgControlled
                if balance_detail.wallet_subtype == WalletSubtype.OrgControlled
                else MPCVaultType.UserControlled
            )
            custody_wallets = [
                w
                for w in custody_wallets
                if custody_wallet_vault.get(w.id)
                and custody_wallet_vault.get(w.id).vault_type == target_vault_type
            ]

        custody_wallet_maps = {x.uuid: x for x in custody_wallets}

        # ucw的钱包，不能从web端发起提币, from address需要过滤掉ucw钱包地址，to address可以选择ucw地址
        if not balance_detail.for_to_address:
            custody_wallets = [
                w
                for w in custody_wallets
                if not custody_wallet_vault.get(w.id)
                or custody_wallet_vault.get(w.id).vault_type
                != MPCVaultType.UserControlled.value
            ]

        filtered_chains = None
        if balance_detail.chain_id:
            filtered_chains = [balance_detail.chain_id]
        if balance_detail.token_id:
            filtered_chains = [
                WaaSTokenManager.get_token(balance_detail.token_id).chain_id
            ]
        filtered_chain_identifiers = (
            list(
                {
                    c.chain_identifier
                    for c in WaaSChainManager.list_chains(chain_ids=filtered_chains)
                }
            )
            if filtered_chains
            else []
        )

        addresses = []
        search_keyword = (
            balance_detail.keyword if not searched_custody_wallets else None
        )
        if search_keyword:
            address_books = AddressBookBizProcessor.get_by_label(
                org.uuid, balance_detail.keyword
            )
            addresses = [b.address for b in address_books] if address_books else []

        recent_wallet_address = []
        if balance_detail.current_page == 0:
            direction = EOAEventDirection.WITHDRAW
            if balance_detail.for_to_address:
                direction = EOAEventDirection.DEPOSIT
            recent_wallet_address = cls._get_recent_address(
                org.id,
                custody_wallets,
                filtered_chains,
                search_keyword,
                direction,
                addresses,
                address_source,
            )

        has_balance_addresses = []
        filter_address = None
        address_balance_mapping = {}
        items_in_current_page = 0
        item_offset = 0

        wallet_balances = None
        if balance_detail.token_id:
            token_chain_identifier = WaaSTokenManager.get_token(
                token_id=balance_detail.token_id
            ).chain_identifier
            web_addresses = {
                a.address
                for a in WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=[w.uuid for w in custody_wallets],
                    chain_identifier=[token_chain_identifier],
                    source=AddressSource.WEB.value,
                )
            }

            wallet_balances = BalanceManager.get_wallet_balances(
                org_uuid=org.uuid,
                org_id=org.pk,
                wallet_ids=[w.id for w in custody_wallets],
                token_id=balance_detail.token_id,
                use_available=False,
            )

            token_assets = [
                token_asset
                for wallet_balance in wallet_balances.values()
                for token_asset in wallet_balance.filter_token_assets()
            ]

            if search_keyword:
                token_assets = [
                    t
                    for t in token_assets
                    if t.address in addresses or search_keyword in t.address
                ]
            if address_source == AddressSource.WEB.value:
                token_assets = [t for t in token_assets if t.address in web_addresses]
            elif address_source == AddressSource.APIKEY.value:
                token_assets = [
                    t for t in token_assets if t.address not in web_addresses
                ]
            token_assets.sort(key=lambda a: a.balance, reverse=True)
            _, _, current_token_assets = paginate_by_list(
                token_assets, balance_detail.current_page, balance_detail.page_size
            )
            filter_address = [t.address for t in token_assets]
            item_offset += len(token_assets)

            if current_token_assets:
                has_balance_addresses = WalletChainIdentifierAddressDao.list_by_chain_identifier_and_addresses(
                    token_chain_identifier,
                    [t.address for t in current_token_assets],
                )
                items_in_current_page += len(has_balance_addresses)
        elif mpc_list_balance_details_sum_all_token_balance():
            address_balance_mapping = (
                MPCWalletUtils.get_address_currency_balance_mapping(
                    balance_detail=balance_detail,
                    address_source=address_source,
                    custody_wallets=custody_wallets,
                )
            )
            if search_keyword:
                address_balance_mapping = {
                    k: v
                    for k, v in address_balance_mapping.items()
                    if k in addresses or search_keyword in k
                }

            filter_address = list(address_balance_mapping.keys())
            item_offset += len(address_balance_mapping)

            address_balances = sorted(
                address_balance_mapping.items(),
                key=lambda x: x[1],
                reverse=True,
            )
            _, _, current_address_balances = paginate_by_list(
                address_balances, balance_detail.current_page, balance_detail.page_size
            )

            if current_address_balances:
                has_balance_addresses = (
                    WalletChainIdentifierAddressDao.list_by_addresses(
                        list({a[0] for a in current_address_balances}),
                        source=address_source,
                    )
                )
                items_in_current_page += len(current_address_balances)

        total_count, page_count, _ = paging(
            WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                [w.uuid for w in custody_wallets],
                chain_identifier=filtered_chain_identifiers,
                addresses=addresses,
                address_keyword=search_keyword,
                source=address_source,
            ),
            balance_detail.current_page,
            balance_detail.page_size,
        )

        if items_in_current_page >= balance_detail.page_size:
            wallet_addresses = recent_wallet_address + has_balance_addresses
        else:
            offset, limit = calculate_partition_offset_limit(
                current_page=balance_detail.current_page,
                page_size=balance_detail.page_size,
                other_partition_data_count=item_offset,
            )
            _, _, _, wallet_addresses = paging_by_offset(
                WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                    [w.uuid for w in custody_wallets],
                    chain_identifier=filtered_chain_identifiers,
                    addresses=addresses,
                    address_keyword=search_keyword,
                    filter_id=[w.id for w in recent_wallet_address if w],
                    filter_addresses=filter_address,
                    source=address_source,
                ),
                offset=offset,
                limit=limit,
            )
            wallet_addresses = (
                recent_wallet_address + list(wallet_addresses) + has_balance_addresses
            )
        token_asset_map = {}
        coin_rates = {}
        if balance_detail.token_id:
            if not wallet_balances:
                wallet_balances = BalanceManager.get_wallet_balances(
                    org_uuid=org.uuid,
                    org_id=org.pk,
                    wallet_ids=[w.id for w in custody_wallets],
                    token_id=balance_detail.token_id,
                    use_available=False,
                )

            token_assets = [
                token_asset
                for wallet_balance in wallet_balances.values()
                for token_asset in wallet_balance.filter_token_assets(
                    [
                        lambda x: x.address in [a.address for a in wallet_addresses],
                    ]
                )
            ]

            token_asset_map = {t.address: t for t in token_assets}
            coin_rates = CoinCurrencyRateManager.get_rates(
                coins=[t.asset_code for t in token_assets],
                currency=settings.CURRENCY_USD,
            )

        address_books = AddressBookBizProcessor.get_by_addresses_and_chain(
            org_id=org.uuid, addresses=[a.address for a in wallet_addresses]
        )
        address_book_dict = {a.address: a for a in address_books}

        extra_info_map = {
            (e.chain_coin, e.address): e
            for e in MPCWalletAddressExtraInfoDao.query_by_custody_wallet_and_coins(
                [w.id for w in custody_wallets],
                [w.chain_coin for w in wallet_addresses],
                [w.address for w in wallet_addresses],
            )
        }

        result = []
        filter_address = set()
        recent_wallet_address_ids = [w.id for w in recent_wallet_address]

        chain_info_map = (
            WaaSChainManager.list_chains(
                # TODO: 这里需要传ref_org_id, 确保返回给前端的数据中，chain的supported_address_encoding正确
                chain_ids=[w.chain_coin for w in wallet_addresses],
                ignore_address_encoding=False,
                ignore_explorer_tx_url=False,
            )
            .key_by(
                lambda x: x.chain_id,
            )
            .map_values(
                WaaSChainUtils.to_chain_info,
            )
            .to_dict()
        )
        collect_address = {
            (a.chain_id, a.address): a
            for a in AutoSweepCollectionAddressDao.list_by_wallet(
                [w.uuid for w in custody_wallets]
            )
        }
        for wallet_address in wallet_addresses:
            if wallet_address.address in filter_address:
                continue
            filter_address.add(wallet_address.address)
            custody_wallet = custody_wallet_maps.get(wallet_address.wallet_id)
            vault = custody_wallet_vault.get(custody_wallet.id)
            token_asset = token_asset_map.get(wallet_address.address, None)
            token_balance_detail = None
            chain_info = chain_info_map.get(wallet_address.chain_coin)
            extra_info = extra_info_map.get(
                (wallet_address.chain_coin, wallet_address.address), None
            )
            withdrawable = True
            wallet_config = CustodyWalletConfigManager.get_config_from_custody_wallet(
                custody_wallet
            )
            if wallet_config.st_mpc_enable_auto_sweep:
                chain_id = chain_info.chain_id
                if chain_info.chain_identifier == COIN_ETH:
                    chain_id = COIN_ETH
                withdrawable = (
                    chain_id,
                    wallet_address.address,
                ) in collect_address.keys()
            if balance_detail.token_id:
                token_info = WaaSTokenManager.get_token(
                    token_id=balance_detail.token_id
                )
                abs_balance = Decimal(0)
                value = Decimal(0)
                transferable_balance = Decimal(0)
                if token_asset:
                    transferable_balance = max(
                        token_asset.balance - Decimal(token_info.reserved_balance),
                        Decimal(0),
                    )
                    abs_balance, value = MPCWalletUtils.calc_token_value_balance(
                        coin_rates.get(token_asset.asset_code, Decimal(0)),
                        token_info,
                        int(transferable_balance),
                    )
                token_balance_detail = WalletTokenBalance(
                    token_id=token_info.token_id,
                    chain_id=token_info.chain_id,
                    balance=int(token_asset.balance) if token_asset else 0,
                    transferable_balance=int(transferable_balance),
                    abs_balance=abs_balance,
                    currency_balance=value,
                    last_activity_timestamp=int(token_asset.modified_time.timestamp())
                    * 1000
                    if token_asset
                    else None,
                    symbol=token_info.symbol,
                )

            address_book = address_book_dict.get(wallet_address.address)
            wallet_name_info = custody_wallet.name
            vault_id = None
            project_id = None
            if vault:
                wallet_name_info = f"{vault.name}/{custody_wallet.name}"
                vault_id = vault.uuid
                project_id = vault.project.uuid if vault.project else None
            recent = False
            if wallet_address.id in recent_wallet_address_ids:
                recent = True

            currency_value = Decimal(0)
            if token_balance_detail:
                currency_value = token_balance_detail.currency_balance
            elif wallet_address.address in address_balance_mapping:
                currency_value = address_balance_mapping[
                    wallet_address.address
                ].normalize()

            result.append(
                WaaS2BalanceDetailBO(
                    wallet_type=WalletType.MPC,
                    wallet_name_info=wallet_name_info,
                    wallet_id=custody_wallet.uuid,
                    currency_value=currency_value,
                    address=wallet_address.address,
                    encoding=MPCWalletUtils.get_encoding(extra_info),
                    token_balance_detail=token_balance_detail,
                    chain=chain_info,
                    recent=recent,
                    label=address_book.label if address_book else "",
                    wallet_subtype=WalletSubtype.OrgControlled
                    if vault and vault.vault_type == MPCVaultType.OrgControlled
                    else WalletSubtype.UserControlled,
                    source=wallet_address.source,
                    vault_id=vault_id,
                    project_id=project_id,
                    withdrawable=withdrawable,
                )
            )

        result.sort(
            key=lambda x: (
                x.recent,
                x.currency_value,
                x.token_balance_detail.balance if x.token_balance_detail else 0,
            ),
            reverse=True,
        )

        return result, PaginationByPage(
            total_records=total_count,
            total_pages=page_count,
            current_page=balance_detail.current_page + 1,
            page_size=balance_detail.page_size,
        )

    @classmethod
    def _get_recent_address(
        cls,
        org_id: int,
        custody_wallets: List[CustodyWallet],
        filter_chains: List[str],
        search_keyword: str,
        direction: int,
        searched_address: List[str],
        source: int = None,
    ) -> List[Union[WalletAddress, WalletChainIdentifierAddress]]:
        recent_addresses = []
        all_recent_txs = EOATransactionDetailDao.list_recent_txs(
            org_id=org_id,
            custody_wallet_ids=[w.id for w in custody_wallets],
            direction=direction,
            token=filter_chains,
        )
        if direction == EOAEventDirection.DEPOSIT:
            all_recent_addresses = [
                (t.chain_coin, t.to_address) for t in all_recent_txs if t.to_address
            ]
        else:
            all_recent_addresses = [
                (t.chain_coin, t.from_address) for t in all_recent_txs if t.from_address
            ]
        if search_keyword:
            all_recent_addresses = [
                a
                for a in all_recent_addresses
                if a[1] in searched_address or search_keyword in a[1]
            ]
        for address in all_recent_addresses:
            if len(recent_addresses) >= 3:
                break
            if address not in recent_addresses:
                recent_addresses.append(address)
        if recent_addresses:
            chain_coins = {a[0] for a in recent_addresses}
            chain_identifier_map = (
                WaaSChainManager.list_chains(
                    chain_ids=list(chain_coins),
                )
                .key_by(lambda x: x.chain_id)
                .map_values(
                    lambda x: x.chain_identifier,
                )
                .to_dict()
            )
            return WalletChainIdentifierAddressDao.list_by_address_keys(
                [(a[1], chain_identifier_map.get(a[0])) for a in recent_addresses],
                source=source,
            )
        return []

    @classmethod
    def pre_transfer(
        cls, org_id, transfer_request: TransactionCreateBO
    ) -> EstimateFeeBO:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            transfer_request.from_wallet_id
        )
        token_info = WaaSTokenManager.get_token(
            token_id=transfer_request.token_id or transfer_request.token_id
        )
        supported_fee_models = EOAFeeModelManager.get_web_eoa_supported_fee_models(
            org_id=transfer_request.biz_org_id, chain_id=token_info.chain_id
        )

        use_new_fee_model = MPCWalletDevUtils.use_new_fee_model(
            org_id=transfer_request.biz_org_id,
            chain_coin=token_info.chain_id,
            origin_cobo_id=transfer_request.origin_cobo_id,
        )

        if transfer_request.from_address:
            transfer_request.from_address = EOAAddressManager.convert_eoa_address(
                org_id=transfer_request.biz_org_id,
                chain_id=token_info.chain_id,
                address=transfer_request.from_address,
                check_switch=False,
            )

        account_sd = (
            SourceDestination(
                source=EOAFeeSourceAccountRequest(
                    source_type=SourceTypeEnum.SRC_MODEL_ACCOUNT,
                    wallet_id=str(custody_wallet.remote_wallet_id),
                    address=transfer_request.from_address,
                ),
                destination=EOAFeeDestinationAccountRequest(
                    destination_type=DestinationTypeEnum.DST_MODEL_ACCOUNT,
                    address=transfer_request.to_address,
                    amount=str(transfer_request.amount),
                    memo=transfer_request.memo,
                ),
            )
            if not token_info.is_utxo
            else None
        )

        utxo_sd = (
            SourceDestination(
                source=EOAFeeSourceUTXORequest(
                    source_type=SourceTypeEnum.SRC_MODEL_UTXO,
                    wallet_id=str(custody_wallet.remote_wallet_id),
                    address=transfer_request.from_address,
                ),
                destination=EOAFeeDestinationUTXORequest(
                    destination_type=DestinationTypeEnum.DST_MODEL_UTXO,
                    outputs=[
                        EOATransferDestinationUTXOOutputRequest(
                            address=transfer_request.to_address,
                            amount=str(transfer_request.amount),
                        )
                    ],
                    change_address=transfer_request.from_address,
                ),
            )
            if token_info.is_utxo
            else None
        )

        fee_type = None
        origin_tr = None
        if transfer_request.origin_cobo_id:
            origin_tr = TransactionRequestDao.get_by_cobo_id(
                transfer_request.origin_cobo_id
            )
            extra_args = MPCExtraParameters.from_json(origin_tr.extra_args)
            fee_type = FeeType.from_value(extra_args.fee_type)
            if not fee_type:
                if token_info.is_utxo:
                    fee_type = FeeType.UTXO
                elif FeeModelType.TYPE_FIXED.value in supported_fee_models:
                    fee_type = FeeType.FIXED
                elif origin_tr.fee_rate:
                    fee_type = FeeType.EVM_LEGACY
                elif origin_tr.max_fee or origin_tr.max_priority_fee:
                    fee_type = FeeType.EVM_EIP_1559
            if use_new_fee_model:
                estimate_tx_fee_result = BlockchainFeeManagerEOAMPC.estimate_rbf_tx_fee(
                    params=EstimateRBFTxFeeParams(
                        origin_cobo_id=transfer_request.origin_cobo_id,
                        rbf_type=transfer_request.rbf_type,
                        fee_types=[FeeType.populate_to_fee_model_type(fee_type)],
                    )
                )
            else:
                estimate_tx_fee_result = (
                    BlockchainFeeManagerEOAMPC.estimate_mpc_rbf_tx_fee(
                        custody_wallet,
                        transfer_request,
                        FeeType.populate_to_fee_model_type(fee_type),
                    )
                )
        else:
            estimate_tx_fee_result = BlockchainFeeManagerEOAMPC.estimate_tx_fee(
                params=EstimateTxFeeParams(
                    fee_types=supported_fee_models,
                    asset_coin=token_info.token_id,
                    chain_coin=token_info.chain_id,
                    account=account_sd,
                    utxo=utxo_sd,
                )
            )

        fee_token_info = WaaSTokenManager.get_token(
            token_info.fee_token_id,
            ignore_token_rate=False,
        )
        fee_usd_rate = Decimal(fee_token_info.usd_rate)

        fee_amount = None

        if (
            transfer_request.utxo
            or transfer_request.legacy
            or transfer_request.eip1559
            or transfer_request.fil_fee
            or transfer_request.sol_fee
        ):
            custom_fee = BlockChainEntityFeeDetail(
                fee_type=FeeModelType.TYPE_FIXED,
                fee_token_id=token_info.fee_token_id,
            )

            if transfer_request.utxo:
                custom_fee.fee_type = FeeModelType.TYPE_UTXO
                custom_fee.fee_rate = transfer_request.utxo.fee_rate

            if transfer_request.eip1559:
                custom_fee.fee_type = FeeModelType.TYPE_EVM_EIP_1559
                custom_fee.max_priority_fee_per_gas = str(
                    transfer_request.eip1559.max_priority_fee
                )
                custom_fee.max_fee_per_gas = str(transfer_request.eip1559.max_fee)
                custom_fee.gas_limit = str(transfer_request.eip1559.gas_limit)

            if transfer_request.legacy:
                custom_fee.fee_type = FeeModelType.TYPE_EVM_LEGACY
                custom_fee.gas_price = str(transfer_request.legacy.gas_price)
                custom_fee.gas_limit = str(transfer_request.legacy.gas_limit)

            if transfer_request.fil_fee:
                custom_fee.fee_type = FeeModelType.TYPE_FIL
                custom_fee.fee_fil = transfer_request.fil_fee

            if transfer_request.sol_fee:
                custom_fee.fee_type = FeeModelType.TYPE_SOL
                custom_fee.fee_sol = transfer_request.sol_fee

            if transfer_request.origin_cobo_id:
                if use_new_fee_model:
                    custom_fee_detail = (
                        BlockchainFeeManagerEOAMPC.estimate_per_rbf_tx_fee(
                            params=EstimateRBFTxFeeDetailParams(
                                rbf_type=transfer_request.rbf_type,
                                origin_cobo_id=transfer_request.origin_cobo_id,
                                custom_fee=custom_fee,
                            )
                        )
                    )
                else:
                    custom_fee_detail = (
                        BlockchainFeeManagerEOAMPC.estimate_mpc_per_rbf_tx_fee(
                            custody_wallet,
                            transfer_request,
                            FeeType.populate_to_fee_model_type(fee_type),
                        )
                    )
            else:
                custom_fee_detail = BlockchainFeeManagerEOAMPC.estimate_per_tx_fee(
                    params=EstimateTxFeeDetailParams(
                        asset_coin=token_info.token_id,
                        chain_coin=token_info.chain_id,
                        custom_fee=custom_fee,
                        account=account_sd,
                        utxo=utxo_sd,
                    )
                )

            fee_detail = custom_fee_detail.calc_fee_amount(
                fee_token_info.decimal, fee_usd_rate
            )
            fee_amount = fee_detail.to_bo()

        fee_token_balance = 0
        fee_token_abs_balance = Decimal(0)
        fee_value = Decimal(0)
        last_activity_timestamp = 0

        from_address = transfer_request.from_address
        if origin_tr:
            if origin_tr.from_address:
                from_address = origin_tr.from_address
            if token_info.is_utxo:
                from_address = None

        if from_address:
            token_address_balance = BalanceManager.get_token_address_balance(
                org_id=custody_wallet.org.pk,
                org_uuid=custody_wallet.org.uuid,
                token_id=fee_token_info.token_id,
                address=transfer_request.from_address,
                custody_wallet_id=custody_wallet.pk,
            )
            last_activity_timestamp = (
                int(token_address_balance.modified_time.timestamp()) * 1000
                if token_address_balance.modified_time
                else 0
            )
            fee_token_balance = int(token_address_balance.available_balance)
            fee_token_abs_balance, fee_value = MPCWalletUtils.calc_token_value_balance(
                Decimal(fee_usd_rate), fee_token_info, int(fee_token_balance)
            )
        else:
            token_wallet_balance = BalanceManager.get_token_wallet_balance(
                org_id=custody_wallet.org.pk,
                org_uuid=custody_wallet.org.uuid,
                token_id=fee_token_info.token_id,
                custody_wallet_id=custody_wallet.pk,
            )
            last_activity_timestamp = token_wallet_balance.last_modified_timestamp
            fee_token_balance = int(token_wallet_balance.available_balance)
            fee_token_abs_balance, fee_value = MPCWalletUtils.calc_token_value_balance(
                Decimal(fee_usd_rate), fee_token_info, int(fee_token_balance)
            )

        if FeeModelType.TYPE_FIXED in supported_fee_models:
            fixed_fee = estimate_tx_fee_result.standard.to_fixed_bo(
                fee_token_info.decimal, fee_usd_rate
            )
            fee_amount = FeeAmountBO(
                fee_amount=fixed_fee.fee_amount,
                usd_amount=fixed_fee.usd_amount,
            )

        return EstimateFeeBO(
            fee_token_balance=WalletTokenBalance(
                token_id=fee_token_info.token_id,
                chain_id=fee_token_info.chain_id,
                balance=fee_token_balance,
                transferable_balance=fee_token_balance,
                abs_balance=fee_token_abs_balance,
                currency_balance=fee_value,
                last_activity_timestamp=last_activity_timestamp,
            ),
            fee=FeeDetailBO(
                eip1559=estimate_tx_fee_result.to_eip1559(
                    fee_token_info.decimal, fee_usd_rate
                ),
                legacy=estimate_tx_fee_result.to_legacy(
                    fee_token_info.decimal, fee_usd_rate
                )
                or estimate_tx_fee_result.to_fix(fee_token_info.decimal, fee_usd_rate),
                utxo=estimate_tx_fee_result.to_utxo(
                    fee_token_info.decimal, fee_usd_rate
                ),
                fil=estimate_tx_fee_result.to_fil(fee_token_info.decimal, fee_usd_rate),
                sol=estimate_tx_fee_result.to_sol(fee_token_info.decimal, fee_usd_rate),
                fee=fee_amount if fee_amount else None,
                supported_fee_models=supported_fee_models,
                fee_decimal=fee_token_info.decimal,
            ),
        )

    @classmethod
    def transfer_to(cls, params: TransferToParamBO) -> TransferToBO:
        return TransferToBO(
            supported_type=TransferToSupportedType.get_all_values(),
        )

    @classmethod
    @transaction.atomic
    def create_wallet(cls, create_bo: WaaS2EOAWalletCreateBO) -> WaaS2WalletListBO:
        check(
            create_bo.vault_id,
            ApiInvalidParamException,
            "vault_id",
        )
        mpc_vault = MPCVaultDao.get_by_uuid(create_bo.vault_id)
        check(
            mpc_vault,
            ApiInvalidParamException,
            "vault_id",
        )
        if mpc_vault.org.uuid != create_bo.org_id:
            raise ApiWaaS2Exception(
                ApiWaaS2Exception.ERROR_AUTHENTICATION,
                "unauthorized, please check your permission",
                HTTP_401_UNAUTHORIZED,
            )
        MPCWalletUtils.check_vault_backed_up(mpc_vault)
        custody_wallet = MPCCustodyWalletManager.create_wallet_for_waas2(
            mpc_vault.org.id, create_bo.name, mpc_vault.id
        )
        if create_bo.enable_auto_sweep:
            CustodyWalletConfigManager.update_config(
                custody_wallet.id, st_mpc_enable_auto_sweep=True
            )
            AutoSweepCollectAddressProcessor.create_collect_address_for_wallet(
                custody_wallet
            )
            AutoSweepPolicyProcessor.create_default_policy(custody_wallet)

        return WaaS2WalletListBO(
            wallet_id=custody_wallet.uuid,
            type=WalletType.MPC,
            wallet_type=WalletType.MPC,
            name=custody_wallet.name,
        )

    @classmethod
    def summarize(
        cls, query: WaaS2QueryWalletSummaryByWalletTypeBO
    ) -> WaaS2WalletTypeSummaryBO:
        if query.token_id or query.chain_id:
            enabled_tokens = WaaSTokenManager.list_enabled_tokens_by_wallet_type(
                ref_org_id=query.ref_org_id,
                wallet_type=WalletType.MPC,
                chain_ids=query.chain_id,
                token_ids=[query.token_id] if query.token_id else None,
            )
            if not enabled_tokens:
                return WaaS2WalletTypeSummaryBO(
                    wallet_type=query.wallet_type,
                    wallet_count=0,
                    token_count=0,
                    address_count=0,
                    currency_value=Decimal(0),
                )
        custody_wallets = CustodyWalletDao.list_wallets_by_org_id__config_type(
            org_id=query.biz_org_id, config_type=MPCWalletUtils.get_mpc_wallet_types()
        )

        # 处理scope过滤
        if query.scope:
            sources = WalletSourceManager.list_by_scope_params_and_filter(
                org_id=query.ref_org_id,
                scoped_wallet_types=query.scope.wallet_types,
                scoped_wallet_subtypes=query.scope.wallet_subtypes,
                scoped_project_ids=query.scope.project_ids,
                scoped_vault_ids=query.scope.vault_ids,
                scoped_wallet_ids=query.scope.wallet_ids,
                query_wallet_types=[WalletType.MPC],
            )
            wallet_id_in_scope = {s.wallet_id for s in sources}
            custody_wallets = [
                c for c in custody_wallets if c.uuid in wallet_id_in_scope
            ]

        relations = CustodyWalletMPCVaultRelationDao.list_vault_by_wallet(
            [w.id for w in custody_wallets]
        )
        org_controlled_wallets = [
            r.custody_wallet
            for r in relations
            if r.mpc_vault.vault_type == MPCVaultType.OrgControlled.value
        ]
        user_controlled_wallets = []
        if query.from_wallet_id or (
            query.wallet_subtype
            and query.wallet_subtype == WalletSubtype.UserControlled
        ):
            user_controlled_wallets = [
                r.custody_wallet
                for r in relations
                if r.mpc_vault.vault_type == MPCVaultType.UserControlled.value
            ]
        org_controlled_token_count = cls._summary_wallets_token(
            query.ref_org_id, custody_wallets=org_controlled_wallets
        )
        org_controlled_address_count = cls._summary_wallets_address(
            custody_wallets=org_controlled_wallets
        )
        org_controlled_currency_value = cls._summary_wallets_value(
            query.ref_org_id, custody_wallets=org_controlled_wallets
        )
        user_controlled_token_count = cls._summary_wallets_token(
            query.ref_org_id, custody_wallets=user_controlled_wallets
        )
        user_controlled_address_count = cls._summary_wallets_address(
            custody_wallets=user_controlled_wallets
        )
        user_controlled_currency_value = cls._summary_wallets_value(
            query.ref_org_id, custody_wallets=user_controlled_wallets
        )

        wallet_subtype_summary = [
            WaaS2WalletBaseSummaryBO(
                wallet_type=WalletSubtype.OrgControlled,
                wallet_count=len(org_controlled_wallets),
                token_count=org_controlled_token_count,
                address_count=org_controlled_address_count,
                currency_value=org_controlled_currency_value,
            )
        ]
        if user_controlled_wallets:
            wallet_subtype_summary.append(
                WaaS2WalletBaseSummaryBO(
                    wallet_type=WalletSubtype.UserControlled,
                    wallet_count=len(user_controlled_wallets),
                    token_count=user_controlled_token_count,
                    address_count=user_controlled_address_count,
                    currency_value=user_controlled_currency_value,
                )
            )

        return WaaS2WalletTypeSummaryBO(
            wallet_type=query.wallet_type,
            wallet_count=len(org_controlled_wallets) + len(user_controlled_wallets),
            token_count=org_controlled_token_count + user_controlled_token_count,
            address_count=org_controlled_address_count + user_controlled_address_count,
            currency_value=org_controlled_currency_value
            + user_controlled_currency_value,
            wallet_subtype_summary=wallet_subtype_summary,
        )

    @classmethod
    def summarize_subtype_summary(
        cls, query: WaaS2QueryWalletSummaryByWalletTypeBO
    ) -> Optional[WaaS2WalletBaseSummaryBO]:
        if not query.wallet_subtype:
            return None

        custody_wallets = CustodyWalletDao.list_wallets_by_org_id__config_type(
            org_id=query.biz_org_id, config_type=MPCWalletUtils.get_mpc_wallet_types()
        )

        # 处理scope过滤
        if query.scope:
            sources = WalletSourceManager.list_by_scope_params_and_filter(
                org_id=query.ref_org_id,
                scoped_wallet_types=query.scope.wallet_types,
                scoped_wallet_subtypes=query.scope.wallet_subtypes,
                scoped_project_ids=query.scope.project_ids,
                scoped_vault_ids=query.scope.vault_ids,
                scoped_wallet_ids=query.scope.wallet_ids,
                query_wallet_types=[WalletType.MPC],
            )
            wallet_id_in_scope = {s.wallet_id for s in sources}
            custody_wallets = [
                c for c in custody_wallets if c.uuid in wallet_id_in_scope
            ]
        relations = CustodyWalletMPCVaultRelationDao.list_vault_by_wallet(
            [w.id for w in custody_wallets]
        )
        if query.wallet_subtype == WalletSubtype.OrgControlled:
            wallets = [
                r.custody_wallet
                for r in relations
                if r.mpc_vault.vault_type == MPCVaultType.OrgControlled.value
            ]
        elif query.wallet_subtype == WalletSubtype.UserControlled:
            wallets = [
                r.custody_wallet
                for r in relations
                if r.mpc_vault.vault_type == MPCVaultType.UserControlled.value
            ]
        else:
            return None

        user_controlled_token_count = cls._summary_wallets_token(
            query.ref_org_id, custody_wallets=wallets
        )
        user_controlled_address_count = cls._summary_wallets_address(
            custody_wallets=wallets
        )
        user_controlled_currency_value = cls._summary_wallets_value(
            query.ref_org_id, custody_wallets=wallets
        )

        return WaaS2WalletBaseSummaryBO(
            wallet_type=WalletSubtype.OrgControlled,
            wallet_count=len(wallets),
            token_count=user_controlled_token_count,
            address_count=user_controlled_address_count,
            currency_value=user_controlled_currency_value,
        )

    @classmethod
    def summarize_by_wallet_id(cls, wallet_id) -> WaaS2WalletBaseSummaryBO:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(wallet_id)
        token_count = cls._summary_wallets_token(
            custody_wallet.org.uuid, custody_wallets=[custody_wallet]
        )
        address_count = cls._summary_wallets_address(custody_wallets=[custody_wallet])
        currency_value = cls._summary_wallets_value(
            custody_wallet.org.uuid, custody_wallets=[custody_wallet]
        )
        return WaaS2WalletBaseSummaryBO(
            wallet_type=WalletType.MPC.value,
            wallet_count=1,
            token_count=token_count,
            address_count=address_count,
            currency_value=currency_value,
        )

    @classmethod
    def supported_assets(cls, query: WaaS2QuerySupportedAssetsBO) -> List[AssetMeta]:
        # TODO PM need to supported asset to token mapping data
        filtered_chains = None
        if query.from_address:
            wallet_addresses = WalletAddressDao.list_by_address(
                address=query.from_address
            )
            filtered_chains = {a.chain_coin for a in wallet_addresses}
        filtered_coins = BlockchainManager.get_mpc_support_asset_coins()
        result: Dict[str, AssetMeta] = {}
        for coin in filtered_coins:
            coin_info = CustodyCoinManager.get_asset_coin(asset_coin=coin)
            if filtered_chains and coin_info.chain_coin not in filtered_chains:
                continue
            coin_info = CustodyCoinManager.get_asset_coin(
                asset_coin=coin, nullable=True
            )
            if not coin_info:
                continue
            asset_code = coin.split("_")[-1]
            result.setdefault(
                asset_code,
                AssetMeta(
                    code=asset_code,
                    display_code=coin_info.display_code,
                    icon_url=coin_info.icon_url,
                ),
            )
            result[asset_code].coins.append(cls._to_coin_meta(coin_info=coin_info))
        return list(result.values())

    @classmethod
    def supported_networks(
        cls, query: WaaS2QuerySupportedChainsBO
    ) -> Tuple[List[ChainMeta], PaginationByPage]:
        # TODO PM need give support chain list or get by package
        chains = BlockchainManager.get_mpc_support_chain_coins()
        result: List[ChainMeta] = []
        for chain_code in chains:
            chain_info = CustodyCoinManager.get_asset_coin(chain_code, nullable=True)
            if not chain_info:
                continue
            chain_meta = ChainMeta(
                code=chain_info.chain_coin,
                display_code=chain_info.display_code,
                icon_url=chain_info.icon_url,
                description=chain_info.description,
            )
            result.append(chain_meta)

        count, page_count, page_chains = paginate_by_list(
            result, query.current_page - 1, query.page_size
        )
        page = PaginationByPage(
            total_records=count,
            total_pages=page_count,
            current_page=query.current_page,
            page_size=query.page_size,
        )
        return page_chains, page

    @classmethod
    def query_assets(
        cls, query: WaaS2QueryWalletAssetsBO
    ) -> Tuple[List[Union[Asset, Coin]], PaginationByPage]:
        # 临时返回空数组，不block scw联调
        asset_result = []
        count, page_count, page_assets = paginate_by_list(
            asset_result, query.current_page, query.page_size
        )
        page = PaginationByPage(
            total_records=count,
            total_pages=page_count,
            current_page=query.current_page,
            page_size=query.page_size,
        )
        return page_assets, page

    @classmethod
    def query_wallet_ids_by_uuid(cls, uuid: str):
        org = OrganizationDao.query_by_uuid(uuid)
        custody_wallets = CustodyWalletDao.list_by_org_id_config_type(
            org_id=org.id,
            config_type=[CustodyWallet.WT_MPC, CustodyWallet.WT_MPC_WEB3],
        )
        return [wallet.id for wallet in custody_wallets]

    @classmethod
    def query_wallets(
        cls, query: WaaS2QueryWalletBO
    ) -> Tuple[List[WaaS2WalletListBO], PaginationByPage]:
        if query.vault_id:
            vault = MPCVaultDao.get_by_uuid(query.vault_id)
            if not vault:
                raise CustodyApiException(
                    CustodyApiException.ERROR_INVALID_PARAM,
                    "vault not exist",
                )
            vault_wallets = CustodyWalletMPCVaultRelationDao.list_vault_wallets(
                vault.id
            )
            custody_wallets = [v.custody_wallet for v in vault_wallets]
        else:
            biz_org_id = query.biz_org_id
            if not biz_org_id:
                org = OrganizationDao.query_by_uuid(query.org_id)
                biz_org_id = org.id
            custody_wallets = CustodyWalletDao.list_by_org_id_config_type(
                org_id=biz_org_id,
                config_type=[CustodyWallet.WT_MPC, CustodyWallet.WT_MPC_WEB3],
                reverse=True,
            )
        if query.keyword:
            filtered_wallet_ids = set()
            if query.search_name:
                filtered_wallet_ids.update(
                    [w.id for w in custody_wallets if query.keyword in w.name]
                )
            if query.search_address:
                filtered_wallet_ids.update(
                    [
                        a.custody_wallet_id
                        for a in WalletAddressDao.list_by_address(query.keyword)
                    ]
                )
            custody_wallets = [
                w for w in custody_wallets if w.id in filtered_wallet_ids
            ]

        custody_wallet_addresses = {}
        if custody_wallets:
            custody_wallet_source = {
                s.wallet_id: s
                for s in WalletSourceDao.list_by_wallet_ids(
                    [w.uuid for w in custody_wallets]
                )
            }
            wallet_addresses = WalletChainIdentifierAddressDao.list_by_custody_wallet(
                [w.uuid for w in custody_wallets]
            )
            for wallet_address in wallet_addresses:
                custody_wallet_addresses.setdefault(wallet_address.wallet_id, [])
                custody_wallet_addresses[wallet_address.wallet_id].append(
                    wallet_address
                )
            enabled_tokens = WaaSTokenManager.list_enabled_tokens_by_wallet_type(
                ref_org_id=custody_wallets[0].org.uuid,
                wallet_type=WalletType.MPC,
                chain_ids=query.chain_id,
                token_ids=query.token_ids,
            )
            wallet_balances = BalanceManager.get_wallet_balances(
                org_uuid=query.org_id,
                org_id=query.biz_org_id,
                wallet_ids=[x.id for x in custody_wallets],
                token_ids=[t.token_id for t in enabled_tokens],
            )
            token_ids = [
                each.token_id
                for wallet_balance in wallet_balances.values()
                for each in wallet_balance
            ]

            coin_rates = CoinCurrencyRateManager.get_rates(
                coins=token_ids,
                currency=settings.CURRENCY_USD,
            )
        else:
            return [], PaginationByPage(
                current_page=query.current_page + 1,
                page_size=query.page_size,
            )

        # 处理scope过滤
        wallet_id_in_scope = set()
        if query.scope:
            sources = WalletSourceManager.list_by_scope_params_and_filter(
                org_id=query.org_id,
                scoped_wallet_types=query.scope.wallet_types,
                scoped_wallet_subtypes=query.scope.wallet_subtypes,
                scoped_project_ids=query.scope.project_ids,
                scoped_vault_ids=query.scope.vault_ids,
                scoped_wallet_ids=query.scope.wallet_ids,
                query_wallet_types=[WalletType.MPC],
                query_wallet_subtypes=[query.wallet_subtype]
                if query.wallet_subtype
                else None,
            )
            wallet_id_in_scope = {s.wallet_id for s in sources}

        result = []

        wallet_uuid_to_id = {}
        for custody_wallet in custody_wallets:
            custody_wallet_id = (
                custody_wallet.uuid if custody_wallet.uuid else custody_wallet.id
            )
            wallet_uuid_to_id[custody_wallet_id] = custody_wallet.id
            wallet_source = custody_wallet_source.get(custody_wallet.uuid)

            # 处理scope过滤
            if query.scope and custody_wallet_id not in wallet_id_in_scope:
                continue

            if query.auto_sweep:
                wallet_config = (
                    CustodyWalletConfigManager.get_config_from_custody_wallet(
                        custody_wallet
                    )
                )
                enable_auto_sweep = query.auto_sweep == "enable"
                config_enable_auto_sweep = (
                    wallet_config.st_mpc_enable_auto_sweep or False
                )
                if enable_auto_sweep != config_enable_auto_sweep:
                    continue

            wallet_balance = wallet_balances.get(custody_wallet.pk)
            wallet_balance.add_coin_rates(coin_rates=coin_rates)
            currency_value = wallet_balance.currency_value
            if query.min_value:
                if currency_value < query.min_value:
                    continue
            if query.max_value:
                if currency_value > query.max_value:
                    continue

            last_activity_time = wallet_balance.last_activity_time
            if query.min_activity_timestamp:
                if last_activity_time < query.min_activity_timestamp * 1000:
                    continue
            if query.max_activity_timestamp:
                if last_activity_time > query.max_activity_timestamp * 1000:
                    continue

            wallet_config = CustodyWalletConfigManager.get_config_from_custody_wallet(
                custody_wallet
            )
            bo = WaaS2CustodialWalletListBO(
                wallet_id=str(custody_wallet_id),
                type=WalletType.MPC,
                wallet_type=WalletType.MPC,
                wallet_subtype=WalletSubtype.from_value(wallet_source.subtype)
                if wallet_source
                else None,
                name=custody_wallet.name,
                create_time=custody_wallet.created_time,
                last_activity_timestamp=last_activity_time,
                address_count=len(
                    custody_wallet_addresses.get(custody_wallet.uuid, [])
                ),
                currency_value=currency_value,
                token_count=wallet_balance.no_zero_balance_token_count,
                no_zero_balance_tokens=MPCWalletUtils.build_token_assets_v2(
                    wallet_balance=wallet_balance
                ),
                enable_auto_sweep=wallet_config.st_mpc_enable_auto_sweep,
            )
            result.append(bo)

        if query.sort_by:
            if query.sort_by == SortBy.TotalValue:
                result.sort(
                    key=lambda x: x.currency_value,
                    reverse=query.sort_direction == SortDirection.DESC,
                )
            elif query.sort_by == SortBy.LastActivityTimestamp:
                result.sort(
                    key=lambda x: x.last_activity_timestamp,
                    reverse=query.sort_direction == SortDirection.DESC,
                )
            elif query.sort_by == SortBy.CreateTime:
                result.sort(
                    key=lambda x: x.create_time,
                    reverse=query.sort_direction == SortDirection.DESC,
                )
            elif query.sort_by == SortBy.Name:
                result.sort(
                    key=lambda x: x.name,
                    reverse=query.sort_direction == SortDirection.DESC,
                )
        count, page_count, page_assets = paginate_by_list(
            result, query.current_page, query.page_size
        )

        for i, w in enumerate(page_assets):
            wallet_balance = wallet_balances.get(wallet_uuid_to_id[w.wallet_id])
            wallet_balance.add_coin_rates(coin_rates=coin_rates)
            page_assets[i] = page_assets[i].with_updated_screening_balance(
                wallet_balance.get_screening_balance()
            )

        page = PaginationByPage(
            total_records=count,
            total_pages=page_count,
            current_page=query.current_page + 1,
            page_size=query.page_size,
        )
        return page_assets, page

    @classmethod
    def get_transaction_details(
        cls, org_id, record: QueryTransactionRecord
    ) -> WaaS2TransactionDetailBO:
        return MPCWalletTransactionProcessor.get_transaction_details(org_id, record)

    @classmethod
    def update_transaction(
        cls,
        org_id,
        record: TransactionDetailData,
        update_bo: WaaS2UpdateTransactionBO,
    ) -> None:
        MPCWalletTransactionProcessor.update_transaction(org_id, record, update_bo)

    @classmethod
    @transaction.atomic
    def update_wallet(
        cls, wallet_id, wallet_bo: WaaS2EOAWalletCreateBO
    ) -> WaaS2WalletDetailBO:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(wallet_id)
        if wallet_bo.name:
            existed_wallets = CustodyWalletManager.list_custody_wallets_by_org_id__name(
                org_id=wallet_bo.biz_org_id, wallet_name=wallet_bo.name
            )
            check(
                len(existed_wallets) == 0,
                ApiExistedInvalidParamException,
                f"The wallet name: {wallet_bo.name} exist",
            )
            MPCCustodyWalletManager.update_wallet_name(custody_wallet, wallet_bo.name)

        wallet_config = CustodyWalletConfigManager.get_config_from_custody_wallet(
            custody_wallet
        )
        if wallet_bo.enable_auto_sweep and not wallet_config.st_mpc_enable_auto_sweep:
            CustodyWalletConfigManager.update_config(
                custody_wallet.id, st_mpc_enable_auto_sweep=wallet_bo.enable_auto_sweep
            )
            AutoSweepCollectAddressProcessor.create_collect_address_for_wallet(
                custody_wallet
            )
            AutoSweepPolicyProcessor.create_default_policy(custody_wallet)

        wallet_vault_relation = CustodyWalletMPCVaultRelationDao.get_vault_by_wallet(
            custody_wallet.id
        )
        return WaaS2WalletDetailBO(
            org_id=wallet_bo.org_id,
            wallet_id=custody_wallet.uuid,
            name=wallet_bo.name,
            type=WalletType.MPC,
            subtype=WalletSubtype.OrgControlled
            if wallet_vault_relation.mpc_vault.vault_type
            == MPCVaultType.OrgControlled.name
            else WalletSubtype.UserControlled,
            enable_auto_sweep=wallet_config.st_mpc_enable_auto_sweep,
        )

    @classmethod
    def generate_new_address(cls, create_bo: WaaS2WalletCreateAddressBO):
        return cls.generate_new_addresses(create_bo)

    @classmethod
    def generate_new_addresses(
        cls, create_bo: WaaS2WalletCreateAddressBO
    ) -> List[WaaS2WalletAddressBO]:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            create_bo.wallet_id
        )
        MPCWalletUtils.check_wallet_vault_backed_up(custody_wallet.id)
        chain = create_bo.chain_id or create_bo.chain
        if chain == "All EVM Networks":
            enable_evm_tokens = [
                token
                for token in WaaSTokenManager.list_enabled_tokens_by_wallet_type(
                    ref_org_id=create_bo.ref_org_id, wallet_type=WalletType.MPC
                )
                if token.chain_identifier == COIN_ETH
            ]
            check(
                enable_evm_tokens,
                ApiInvalidParamException,
                "chain network not enable",
            )
            chain = enable_evm_tokens[0].chain_id

        if chain in settings.DOUBLE_ENCODING_ADDRESS_COINS:
            check(
                create_bo.encodings,
                ApiInvalidParamException,
                "encodings can not be null",
            )
        if create_bo.encodings is not None:
            addresses = []
            for encoding in create_bo.encodings:
                addresses.extend(
                    MPCCustodyWalletManager.generate_new_addresses(
                        custody_wallet,
                        chain,
                        create_bo.count,
                        encoding=encoding.value,
                        source=AddressSource.WEB,
                        creator=str(create_bo.custody_user.id)
                        if create_bo.custody_user
                        else None,
                    )
                )
        else:
            addresses = MPCCustodyWalletManager.generate_new_addresses(
                custody_wallet,
                chain,
                create_bo.count,
                source=AddressSource.WEB,
                creator=str(create_bo.custody_user.id)
                if create_bo.custody_user
                else None,
            )
        wallet_address = WalletAddressDao.list_by_coin_and_addresses(
            chain_coin=create_bo.chain, addresses=[a["address"] for a in addresses]
        )
        if create_bo.custody_user:
            for address in wallet_address:
                CustodyWalletCoinAddressDao.create(
                    wallet_id=custody_wallet.pk,
                    coin=address.chain_coin,
                    creater=create_bo.custody_user,
                    address=address.address,
                )

        chain_info_mapping: Dict[str, WaaSChainInfo] = {
            _.chain_id: _
            for _ in WaaSChainManager.list_chains(
                chain_ids=list({a.chain_coin for a in wallet_address if a.chain_coin})
            )
        }
        res = []
        for a in wallet_address:
            chain_info = chain_info_mapping.get(a.chain_coin)
            if not chain_info:
                raise ApiInvalidParamException(f"chain {a.chain_coin} not supported")
            bo = WaaS2WalletAddressBO(
                wallet_id=custody_wallet.uuid,
                chain_id=chain_info.chain_id,
                chain_icon_url=chain_info.icon_url,
                org_id=custody_wallet.org.uuid,
                address=a.address,
                wallet_type=WalletType.MPC,
                create_timestamp=int(a.created_time.timestamp()) * 1000,
            )
            res.append(bo)
        return res

    @classmethod
    def transfer(cls, create_transaction: TransactionCreateBO):
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            create_transaction.from_wallet_id
        )
        MPCWalletUtils.check_wallet_vault_backed_up(custody_wallet.id)
        if not SuspendCoinManager.can_withdraw(create_transaction.token_id):
            raise PortalTransferException(PortalTransferException.ERROR_TOKEN_SUSPENDED)
        enabled_tokens = WaaSTokenManager.list_enabled_tokens_by_wallet_type(
            ref_org_id=custody_wallet.org.uuid, wallet_type=WalletType.MPC
        )
        if create_transaction.token_id not in [x.token_id for x in enabled_tokens]:
            raise PortalTransferException(PortalTransferException.ERROR_TOKEN_SUSPENDED)
        relation = CustodyWalletMPCVaultRelationDao.get_vault_by_wallet(
            custody_wallet.id
        )
        groups = TSSGroupV2Dao.list_by_vault(
            relation.mpc_vault.id,
            group_type=[
                TSSGroupTypeEnum.TYPE_MPC_OWNER,
                TSSGroupTypeEnum.TYPE_MPC_SIGNER,
            ],
            include_status=[TSSGroupStatusEnum.STATUS_ACTIVE],
        )
        if not groups:
            raise PortalTransferException(
                PortalTransferException.ERROR_KEY_GROUP_UNAVAILABLE,
            )
        request_id = create_transaction.request_id
        request_type = TransactionRequestType.TYPE_MPC_WEB
        if not request_id:
            request_id = CustodyTools.create_web_request_id_with_user(
                create_transaction.request_user
            )
        raw_amount = create_transaction.amount
        sign_info = create_transaction.sign_info
        asset_coin = create_transaction.token_id or create_transaction.token_id
        token_info = WaaSTokenManager.get_token(asset_coin)
        supported_fee_models = WaaSChainUtils.get_chain_supported_fee_models(
            chain_id=token_info.chain_id
        )
        fee_rate = None
        gas_limit = None
        max_fee_per_gas = None
        max_priority_fee_per_gas = None
        fee_amount = None
        fee_type = None
        extra_args = MPCExtraParameters()
        if create_transaction.legacy:
            fee_rate = create_transaction.legacy.gas_price
            gas_limit = create_transaction.legacy.gas_limit
            fee_type = FeeType.EVM_LEGACY
            # todo mpc 前端目前使用的是legacy参数，来传递的fix fee，后续需要推送迁移
            if FeeModelType.TYPE_FIXED.value in supported_fee_models:
                fee_type = FeeType.FIXED
        elif create_transaction.utxo:
            fee_rate = create_transaction.utxo.fee_rate
            fee_amount = (
                MPCWithdrawRequestManager.coin_amount(
                    token_info.token_id, create_transaction.utxo.fee_amount
                )
                if create_transaction.utxo.fee_amount
                else None
            )
            fee_type = FeeType.UTXO
        elif create_transaction.eip1559:
            max_fee_per_gas = create_transaction.eip1559.max_fee
            max_priority_fee_per_gas = create_transaction.eip1559.max_priority_fee
            gas_limit = create_transaction.eip1559.gas_limit
            fee_type = FeeType.EVM_EIP_1559
        elif create_transaction.fixed_fee:
            fee_type = FeeType.FIXED
            # 兼容使用1.0接口的情况
            fee_rate = create_transaction.fixed_fee.fee_amount
            gas_limit = 1
            extra_args.utxo_max_fee_amount = create_transaction.fixed_fee.fee_amount
        elif create_transaction.fil_fee:
            extra_args.fee_param = TransactionFeeFIL(
                token_id=token_info.fee_token_id,
                gas_premium=create_transaction.fil_fee.gas_premium,
                gas_fee_cap=create_transaction.fil_fee.gas_fee_cap,
                gas_limit=str(create_transaction.fil_fee.gas_limit),
            ).to_dict()
            fee_type = FeeType.FIL
        elif create_transaction.sol_fee:
            extra_args.fee_param = TransactionFeeSOL(
                token_id=token_info.fee_token_id,
                compute_unit_price=create_transaction.sol_fee.compute_unit_price,
                compute_unit_limit=create_transaction.sol_fee.compute_unit_limit,
            )
            fee_type = FeeType.SOL

        if not fee_type:
            if token_info.is_utxo:
                fee_type = FeeType.UTXO
            elif FeeModelType.TYPE_FIXED.value in supported_fee_models:
                fee_type = FeeType.FIXED
            else:
                fee_type = FeeType.EVM_LEGACY

        extra_args.fee_type = fee_type

        MPCWalletUtils.check_for_auto_sweep(
            custody_wallet, create_transaction.from_address, token_info.token_id
        )

        tr, _ = TransactionRequestController.create_request(
            org_id=custody_wallet.org.pk,
            request_id=request_id,
            from_custody_wallet=custody_wallet,
            from_address=create_transaction.from_address,
            chain_coin=token_info.chain_id,
            asset_coin=asset_coin,
            to_address=create_transaction.to_address,
            amount=raw_amount,
            fee_rate=fee_rate,
            max_fee=max_fee_per_gas,
            max_priority_fee=max_priority_fee_per_gas,
            gas_limit=gas_limit,
            sign_info=sign_info,
            api_request_info=create_transaction.api_request_info,
            memo=create_transaction.memo,
            extra_args=extra_args.to_json(),
            operation=TransactionRequestOperation.OPERATION_TRANSFER,
            request_type=request_type,
            user=create_transaction.request_user,
            remark=create_transaction.description,
            tags=json.dumps(create_transaction.category_names)
            if create_transaction.category_names
            else "",
            fee_amount=fee_amount,
        )
        wr = WithdrawRequestDao.get_by_org_and_request_id_or_null(
            tr.org_id, tr.request_id
        )
        WalletEventService.dispatch_withdraw_request(wr)

        ref_transactions = TransactionQueryManager.create_transaction(
            source=QueryTransactionSourceType.WithdrawRequest.value, ref_id=wr.id
        )
        txid = (
            ref_transactions[0].uuid
            if ref_transactions and len(ref_transactions) > 0
            else ""
        )
        tx = TransactionMongoClient.get_transaction_by_transaction_id(txid)
        return {
            "request_id": wr.request_id,
            "txid": txid,
            "cobo_id": tr.cobo_id,
            "status": tx.status if tx else "",
        }

    @classmethod
    def sign_message(cls, sign_request: Web3SignMessageBO) -> None:
        chain_coin = sign_request.chain_id
        request_id = sign_request.request_id
        from_address = sign_request.address
        sign_version = sign_request.sign_version
        request_info = sign_request.api_request_info
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            sign_request.wallet_id
        )

        if not request_id:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"request_id: {request_id} is illegal",
            )
        if not from_address:
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"from_address: {from_address} is illegal",
            )

        if sign_version not in SignVersionEnum.get_all_version():
            raise CustodyApiException(
                CustodyException.ERROR_INVALID_PARAM,
                f"sign_version: {sign_version} is illegal",
            )

        if not sign_request.message:
            raise CustodyApiException(
                error_code=CustodyApiException.ERROR_INVALID_PARAM,
                error_message="message should not be none",
            )

        mpc_extra_parameters = MPCExtraParameters()
        if sign_version == SignVersionEnum.EIP_191:
            request_type = TransactionRequestType.TYPE_MPC_WEB3_API_EIP_191
            mpc_extra_parameters.message = sign_request.message
        else:
            request_type = TransactionRequestType.TYPE_MPC_WEB3_API_EIP_712
            mpc_extra_parameters.structured_data = sign_request.message
            try:
                structured_data = json.loads(mpc_extra_parameters.structured_data)
            except json.JSONDecodeError:
                raise CustodyApiException(
                    error_code=CustodyApiException.ERROR_INVALID_PARAM,
                    error_message="message must be json string",
                )

            try:
                validate_structured_data(structured_data=structured_data)
            except Exception:
                raise CustodyApiException(
                    error_code=CustodyApiException.ERROR_INVALID_PARAM,
                    error_message="message is illegal",
                )

        TransactionRequestController.create_request(
            org_id=custody_wallet.org.pk,
            request_id=request_id,
            chain_coin=chain_coin,
            asset_coin=chain_coin,
            from_custody_wallet=custody_wallet,
            from_address=from_address,
            sign_info=sign_request.sign_info,
            api_request_info=request_info,
            request_type=request_type,
            operation=TransactionRequestOperation.OPERATION_SIGN_MESSAGE,
            extra_args=mpc_extra_parameters.to_json(),
        )

    @classmethod
    def get_wallet_detail(
        cls, wallet_id, query_wallet: WaaS2QueryWalletDetailBO
    ) -> WaaS2WalletDetailBO:
        custody_wallet = BaseWalletProcessor.get_wallet_info(wallet_id)
        org = custody_wallet.org
        # biz_org_id和 org_id 不是必填项，主动做下填充
        if not query_wallet.biz_org_id:
            query_wallet.biz_org_id = org.pk
        if not query_wallet.org_id:
            query_wallet.org_id = org.uuid
        sub_type = MPCWalletUtils.get_wallet_subtype([custody_wallet.id])[
            custody_wallet.id
        ]
        wallet_config = CustodyWalletConfigManager.get_config_from_custody_wallet(
            custody_wallet
        )
        return_bo = WaaS2WalletDetailBO(
            org_id=query_wallet.org_id,
            wallet_id=wallet_id,
            name=custody_wallet.name,
            type=query_wallet.wallet_type,
            subtype=sub_type,
            token_count=cls._summary_wallets_token(
                custody_wallet.org.uuid, [custody_wallet]
            ),
            address_count=cls._summary_wallets_address([custody_wallet]),
            currency_value=cls._summary_wallets_value(
                custody_wallet.org.uuid, [custody_wallet]
            ),
            create_timestamp=str(dt_to_ms(custody_wallet.created_time)),
            enable_auto_sweep=wallet_config.st_mpc_enable_auto_sweep,
            auto_sweep_balance=AutoSweepBalanceProcessor.get_wallet_balance(
                custody_wallet
            ),
            auto_sweep_address=AutoSweepCollectAddressProcessor.get_wallet_address_summary(
                custody_wallet
            ),
        )

        fee_station = FeeStationManagerV2.get_fee_station(query_wallet.biz_org_id)
        if fee_station:
            is_forbidden, reason = fee_station.forbidden_withdraw()
            if is_forbidden:
                return_bo.withdraw_forbidden = WalletForbidden(reason=reason)

        return return_bo

    @classmethod
    def list_deposit_addresses(
        cls, wallet_id, query_address: WaaS2QueryWalletAddressBO
    ) -> Tuple[List[WaaS2WalletAddressBO], PaginationByPage]:
        if query_address.chain_id == ALL_EVM:
            query_address.chain_id = COIN_ETH

        chains = query_address.chain_id.split(",") if query_address.chain_id else []
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            query_address.wallet_id
        )
        token_ids = []
        if query_address.token_id:
            token_ids = [query_address.token_id]

        logger.info(f"mpc_wallet param {token_ids} {chains}")
        token_infos = WaaSTokenManager.list_supported_tokens_by_wallet_type(
            ref_org_id=custody_wallet.org.uuid,
            wallet_type=WalletType.MPC,
            token_ids=token_ids if token_ids else None,
            chain_ids=chains if chains else None,
        )
        logger.info(f"mpc_wallet {token_infos}")
        if chains or query_address.token_id:
            cls._generate_address_for_token(
                custody_wallet,
                chains or token_ids,
                query_address.custody_user,
            )
        org = custody_wallet.org
        reverse_id = (
            query_address.sort_by == SortBy.CreateTime
            and query_address.sort_direction == SortDirection.DESC
        )
        address_search = None
        if query_address.address_search:
            address_books = AddressBookBizProcessor.get_by_label(
                org.uuid, query_address.address_search
            )
            address_search = [a.address for a in address_books]
            address_search.append(query_address.address_search)

        min_last_activity_time = (
            ts_to_dt(query_address.min_last_activity_timestamp / 1000)
            if query_address.min_last_activity_timestamp
            else None
        )
        max_last_activity_time = (
            ts_to_dt(query_address.max_last_activity_timestamp / 1000)
            if query_address.max_last_activity_timestamp
            else None
        )
        collection_address_map = {}
        if query_address.web_only:
            query_set = (
                WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=custody_wallet.uuid,
                    chain_identifier=[t.chain_identifier for t in token_infos],
                    reverse_flag=reverse_id,
                    addresses=address_search,
                    source=AddressSource.WEB,
                    min_last_activity_time=min_last_activity_time,
                    max_last_activity_time=max_last_activity_time,
                )
            )
            total_count, page_count, coin_address_list = paging(
                query_set,
                query_address.current_page,
                query_address.page_size,
            )
        elif query_address.main_address:
            main_address = (
                MPCWalletAddressExtraInfoDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=custody_wallet.id,
                    chain_coin=[t.chain_id for t in token_infos],
                    reverse_flag=reverse_id,
                    addresses=address_search,
                    is_main=True,
                )
            )
            query_set = (
                WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=custody_wallet.uuid,
                    chain_identifier=[t.chain_identifier for t in token_infos],
                    reverse_flag=reverse_id,
                    addresses=[a.address for a in main_address],
                    check_addresses_none=True,
                    source=query_address.source if query_address.source else None,
                    min_last_activity_time=min_last_activity_time,
                    max_last_activity_time=max_last_activity_time,
                )
            )
            total_count, page_count, coin_address_list = paging(
                query_set,
                query_address.current_page,
                query_address.page_size,
            )
        elif query_address.main_address is None:
            filter_addresses = None
            contain_addresses = address_search
            check_addresses_none = False
            source = query_address.source or []
            collection_addresses = []
            chain_ids = []
            if isinstance(source, int):
                source = [source]
            if query_address.sweep_address_type or AddressSource.System.value in source:
                chain_ids = [
                    t.chain_id if t.chain_identifier != COIN_ETH else COIN_ETH
                    for t in token_infos
                ]
                collection_addresses = AutoSweepCollectionAddressDao.list_by_wallet(
                    custody_wallet.uuid, status=query_address.collection_address_status
                )
                collection_address_map = {
                    (a.chain_identifier, a.address): a for a in collection_addresses
                }

            if query_address.sweep_address_type == "deposit":
                filter_addresses = [a.address for a in collection_addresses]
            elif query_address.sweep_address_type == "collection":
                check_addresses_none = True
                collection_addresses = [
                    a.address for a in collection_addresses if a.chain_id in chain_ids
                ]
                if address_search:
                    contain_addresses = list(
                        set(address_search).intersection(set(collection_addresses))
                    )
                else:
                    contain_addresses = collection_addresses
            elif AddressSource.System.value in source:
                # 防止两条链，地址相同
                chain_collection_address = [
                    a.address for a in collection_addresses if a.chain_id in chain_ids
                ]
                filter_addresses = [
                    a.address
                    for a in collection_addresses
                    if a.chain_id not in chain_ids
                    and a.address not in chain_collection_address
                ]
            query_set = (
                WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=custody_wallet.uuid,
                    chain_identifier=[t.chain_identifier for t in token_infos],
                    reverse_flag=reverse_id,
                    addresses=contain_addresses,
                    check_addresses_none=check_addresses_none,
                    filter_addresses=filter_addresses,
                    source=query_address.source if query_address.source else None,
                    min_last_activity_time=min_last_activity_time,
                    max_last_activity_time=max_last_activity_time,
                )
            )

            total_count, page_count, coin_address_list = paging(
                query_set,
                query_address.current_page,
                query_address.page_size,
            )
        else:
            main_address = (
                MPCWalletAddressExtraInfoDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=custody_wallet.id,
                    chain_coin=chains,
                    reverse_flag=reverse_id,
                    addresses=address_search,
                    is_main=True,
                )
            )
            query_set = (
                WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=custody_wallet.uuid,
                    chain_identifier=[t.chain_identifier for t in token_infos],
                    reverse_flag=reverse_id,
                    addresses=address_search,
                    filter_addresses=[a.address for a in main_address],
                    source=query_address.source if query_address.source else None,
                    min_last_activity_time=min_last_activity_time,
                    max_last_activity_time=max_last_activity_time,
                )
            )

            total_count, page_count, coin_address_list = paging(
                query_set,
                query_address.current_page,
                query_address.page_size,
            )
        address_list = [a.address for a in coin_address_list]
        # 通过 new address book 获取
        address_books = AddressBookBizProcessor.get_by_addresses_and_chain(
            org_id=org.uuid, addresses=address_list
        )
        address_book_dict = {a.address: a for a in address_books}

        extra_info_map = {
            (e.chain_coin, e.address): e
            for e in MPCWalletAddressExtraInfoDao.query_by_custody_wallet_and_coins(
                [custody_wallet.id], chains, address_list
            )
        }

        chain_info_map = (
            WaaSChainManager.list_chains(
                # TODO: 这里需要传ref_org_id, 确保返回给前端的数据中，chain的supported_address_encoding正确
                chain_ids=[coin.chain_coin for coin in coin_address_list],
                ignore_address_encoding=False,
                ignore_explorer_tx_url=False,
            )
            .key_by(lambda x: x.chain_id)
            .map_values(WaaSChainUtils.to_chain_info)
            .to_dict()
        )

        addresses = []
        user_map = {
            str(u.id): u
            for u in CustodyUserDao.list_by_ids(
                [
                    a.creator
                    for a in coin_address_list
                    if a.creator and a.source == AddressSource.WEB.value
                ]
            )
        }
        address_auto_sweep_balance_map = AutoSweepBalanceProcessor.list_address_balance(
            custody_wallet,
            [(a.chain_identifier, a.address) for a in coin_address_list],
        )

        wallet_balance = BalanceManager.get_wallet_balance_by_wallet_id(
            org_uuid=custody_wallet.org.uuid,
            org_id=custody_wallet.org.pk,
            wallet_id=custody_wallet.pk,
        )

        tokens = wallet_balance.filter_token_assets(
            [
                lambda x: x.address in [a.address for a in coin_address_list],
            ]
        )

        # TODO: 这个get rates效率比较低，后续需要优化
        coin_rates = CoinCurrencyRateManager.get_rates(
            [t.asset_code for t in tokens], currency=settings.CURRENCY_USD
        )

        wallet_balance.add_coin_rates(coin_rates=coin_rates)

        for address in coin_address_list:
            coin_info = CustodyCoinManager.get_asset_coin(asset_coin=address.chain_coin)
            address_book = address_book_dict.get(address.address)
            label = address_book.label if address_book else ""
            extra_info = extra_info_map.get((address.chain_coin, address.address), None)
            address_str = address.address
            address_str = address_str.split("|")
            creator = user_map.get(address.creator)
            collection_address = collection_address_map.get(
                (address.chain_identifier, address.address)
            )
            bo = WaaS2WalletAddressBO(
                wallet_id=wallet_id,
                chain_id=coin_info.chain_coin,
                chain_icon_url=coin_info.icon_url,
                org_id=org.uuid,
                address=address_str[0],
                memo=address_str[1] if len(address_str) > 1 else "",
                wallet_type=WalletType.MPC,
                subtype=MPCWalletUtils.get_wallet_subtype([custody_wallet.id])[
                    custody_wallet.id
                ],
                label=label,
                label_entry_id=address_book.uuid if address_book else None,
                create_timestamp=int(address.created_time.timestamp()) * 1000,
                encoding=MPCWalletUtils.get_encoding(extra_info),
                is_main=extra_info.is_main if extra_info else False,
                chain_info=chain_info_map.get(address.chain_coin, None),
                source=address.source,
                path=extra_info.path if extra_info else "",
                created_by=creator.email if creator else address.creator,
                last_activity_timestamp=int(
                    address.last_activity_time.timestamp() * 1000
                )
                if address.last_activity_time
                else None,
                auto_sweep_balance=address_auto_sweep_balance_map.get(
                    (address.chain_identifier, address.address)
                ),
                screening_balance=wallet_balance.get_address_screening_balance(
                    address=address_str[0]
                )
                if wallet_balance
                else None,
                collection_address_status=collection_address.status
                if collection_address
                else None,
            )
            addresses.append(bo)

        address_token_map: Dict[Tuple[str, str], List[TokenAsset]] = {}

        chain_identifier_map = (
            WaaSChainManager.list_chains(chain_ids=[a.chain_coin for a in tokens])
            .key_by(lambda x: x.chain_id)
            .map_values(lambda x: x.chain_identifier)
            .to_dict()
        )
        for t in tokens:
            _chain_identifier = chain_identifier_map.get(t.chain_coin, None)
            if not _chain_identifier:
                continue
            address_token_map.setdefault((_chain_identifier, t.address), [])
            address_token_map[(_chain_identifier, t.address)].append(t)

        token_info_map = (
            WaaSTokenManager.list_tokens(token_ids=[t.asset_code for t in tokens])
            .key_by(lambda x: x.token_id)
            .to_dict()
        )

        for a in addresses:
            tokens = address_token_map.get(
                (a.chain_info.chain_identifier, a.address), []
            )
            tokens.sort(key=lambda x: x.balance, reverse=True)
            top_assets = []
            total_value = 0
            token_balance = None
            token_abs_balance = None
            for t in tokens:
                if query_address.token_id and t.asset_code != query_address.token_id:
                    continue

                token_info = token_info_map.get(t.asset_code)
                if not token_info:
                    continue
                balance, value = MPCWalletUtils.calc_token_value_balance(
                    coin_rates.get(t.asset_code, Decimal(0)), token_info, int(t.balance)
                )
                total_value += value
                if query_address.token_id and t.asset_code == query_address.token_id:
                    token_balance = int(t.balance)
                    token_abs_balance = balance
                if len(top_assets) >= 3:
                    continue
                top_assets.append(
                    WaaS2CustodialTopAssetBO(
                        code=t.asset_code,
                        total_balance=balance,
                        total_value=value,
                        icon_url=token_info.icon_url,
                    )
                )
            a.token_count = len(address_token_map.get((a.chain_id, a.address), []))
            a.top_assets = top_assets
            a.currency_value = total_value
            a.token_balance = token_balance
            a.token_abs_balance = token_abs_balance

        pagination = PaginationByPage(
            total_records=total_count,
            total_pages=page_count,
            current_page=query_address.current_page + 1,
            page_size=query_address.page_size,
        )
        return addresses, pagination

    @classmethod
    @transaction.atomic()
    def update_addresses(
        cls, update_bo: WaaS2UpdateWalletAddressBO
    ) -> WaaS2WalletAddressBO:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            update_bo.wallet_id
        )
        wallet_address = WalletAddressDao.get_by_coin_and_address(
            update_bo.chain_id, update_bo.address
        )
        if not wallet_address:
            raise CustodyApiException(
                CustodyWebException.ERROR_INVALID_PARAM,
                f"address {update_bo.address} not exist",
            )
        if update_bo.action != "setAsChangeAddress":
            CustodyApiException(
                CustodyWebException.ERROR_INVALID_PARAM,
                f"action {update_bo.action} not support",
            )
        exist_change_address = (
            MPCWalletAddressExtraInfoDao.query_by_custody_wallet_and_coins(
                [custody_wallet.id], chain_coin=[update_bo.chain_id], is_main=True
            )[0]
        )
        extra_infos = MPCWalletAddressExtraInfoDao.query_by_custody_wallet_and_coins(
            [custody_wallet.id],
            chain_coin=[update_bo.chain_id],
            addresses=[update_bo.address],
        )
        if not extra_infos:
            raise CustodyApiException(
                CustodyWebException.ERROR_INVALID_PARAM,
                f"address {update_bo.address} not exist",
            )
        extra_info = extra_infos[0]
        BlockchainMPCClientManager.mpc_set_wallet_change_address(
            update_bo.chain_id, custody_wallet.remote_wallet_id, update_bo.address
        )
        MPCWalletAddressExtraInfoDao.update_by_id(
            exist_change_address.id, is_main=False
        )
        MPCWalletAddressExtraInfoDao.update_by_id(extra_info.id, is_main=True)
        chain_icon_url = WaaSTokenManager.get_token(
            token_id=update_bo.chain_id
        ).icon_url  # 主链币的icon_url
        return WaaS2WalletAddressBO(
            wallet_id=custody_wallet.uuid,
            chain_id=update_bo.chain_id,
            chain_icon_url=chain_icon_url,
            org_id=custody_wallet.org.uuid,
            address=update_bo.address,
            wallet_type=WalletType.MPC,
            subtype=MPCWalletUtils.get_wallet_subtype([custody_wallet.id])[
                custody_wallet.id
            ],
            create_timestamp=int(wallet_address.created_time.timestamp()) * 1000,
        )

    @classmethod
    def list_token_deposit_addresses(
        cls, wallet_id, query_address: WaaS2QueryTokenAddressBO
    ) -> Tuple[List[WaaS2WalletTokenAddressBO], PaginationByPage]:
        chain = query_address.chain
        coin_token = query_address.token
        custody_wallet_id = BaseWalletProcessor.get_custody_wallet_and_check(
            wallet_id
        ).id
        chain_support_tokens = CustodialUtils.get_tokens_by_chain(chain)
        is_valid_token = coin_token in set(chain_support_tokens)
        check(
            is_valid_token,
            ApiInvalidParamException,
            f"token:{coin_token}",
        )
        reverse_id = (
            query_address.sort_by == SortBy.CreateTime
            and query_address.sort_direction == SortDirection.DESC
        )
        keyword = query_address.keyword
        address_keyword = None
        query_addresses = []
        if keyword:
            is_hit, query_address_list = cls._query_by_keyword(
                keyword,
                query_address.ref_org_id,
            )
            if is_hit:
                query_addresses = query_address_list
            else:
                address_keyword = query_address_list[0]
        else:
            query_addresses = []

        query_set = WalletAddressDao.query_by_custody_wallet_and_coins(
            custody_wallet_id=custody_wallet_id,
            chain_coin=[chain],
            addresses=query_addresses,
            address_keyword=address_keyword,
            reverse_flag=reverse_id,
        )
        total_count, page_count, coin_address_list = paging(
            query_set,
            query_address.current_page,
            query_address.page_size,
        )
        address_list = [a.address for a in coin_address_list]
        address_books = AddressBookBizProcessor.get_by_addresses_and_chain(
            org_id=query_address.ref_org_id, addresses=address_list, chain=chain
        )
        address_book_dict = {a.address: a for a in address_books}
        addresses = []
        coin_to_coin_info_dict: Dict[str, AssetCoinInfo] = {}
        for address in coin_address_list:
            coin_info = CustodialUtils.get_asset_coin_info(
                address.chain_coin, coin_to_coin_info_dict
            )
            address_book = address_book_dict.get(address.address)
            label = address_book.label if address_book else ""
            token = (
                address.chain_coin if address.chain_coin != coin_info.chain_coin else ""
            )
            chain_coin_info = CustodialUtils.get_asset_coin_info(
                coin_info.chain_coin, coin_to_coin_info_dict
            )
            bo = WaaS2WalletTokenAddressBO(
                token=token,
                chain=coin_info.chain_coin,
                chain_icon_url=chain_coin_info.icon_url,
                address=address.address,
                created_time=str(dt_to_ms(address.created_time)),
                label=label,
            )
            addresses.append(bo)
        pagination = PaginationByPage(
            total_records=total_count,
            total_pages=page_count,
            current_page=query_address.current_page + 1,
            page_size=query_address.page_size,
        )
        return addresses, pagination

    @classmethod
    def delete_wallet(cls, wallet_id: str):
        raise NotImplementedError

    @classmethod
    def query_tokens_by_asset(
        cls, query_bo: WaaS2WalletAssetTokensQueryBO
    ) -> Tuple[List[WaaS2WalletAssetTokensBO], PaginationByPage]:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            query_bo.wallet_id
        )
        (
            actual_coins,
            coin_rates,
        ) = CustodialUtils.get_filtered_tokens_and_rates_by_asset(query_bo.asset)
        actual_coins = set(actual_coins)

        wallet_balance = BalanceManager.get_wallet_balance_by_wallet_id(
            org_id=custody_wallet.org.pk,
            org_uuid=custody_wallet.org.uuid,
            wallet_id=custody_wallet.pk,
            use_available=False,
        )
        tokens = wallet_balance.filter_token_assets(
            [
                lambda x: x.asset_code in actual_coins,
            ]
        )
        res = []
        currency_unit = query_bo.custody_user.currency_unit
        currency_rate = FiatCurrencyRateController.get_rate(
            base_currency=settings.CURRENCY_USD, quote_currency=currency_unit
        )
        coin_to_coin_info_dict: Dict[str, AssetCoinInfo] = {}
        for token in tokens:
            _, asset_code = CustodialUtils.get_asset_coin(token.asset_code)
            if not asset_code:
                continue
            if query_bo.asset != asset_code:
                continue
            coin_info = CustodialUtils.get_asset_coin_info(
                token.asset_code, coin_to_coin_info_dict
            )
            if query_bo.chain and query_bo.chain != coin_info.chain_coin:
                continue
            balance, value = CustodialUtils.calc_coin_value(
                coin_rates, coin_info, token.asset_code, int(token.balance)
            )
            if query_bo.hide_zero_balance and balance.is_zero():
                continue
            if not CustodialUtils.greater_than_min_value(query_bo.min_value, balance):
                continue
            if not CustodialUtils.less_than_max_value(query_bo.max_value, balance):
                continue
            chain_coin_info = CustodialUtils.get_asset_coin_info(
                coin_info.chain_coin, coin_to_coin_info_dict
            )
            token_bo = WaaS2WalletAssetTokensBO(
                wallet_id=query_bo.wallet_id,
                asset=query_bo.asset,
                token=token.asset_code,
                chain=coin_info.chain_coin,
                chain_icon_url=chain_coin_info.icon_url,
                icon_url=coin_info.icon_url,
                display_code=coin_info.display_code,
                description=coin_info.description,
                value=value / currency_rate,
                balance=balance,
            )
            res.append(token_bo)
        if query_bo.sort_by == SortBy.TotalValue:
            res.sort(
                key=lambda x: x.value,
                reverse=query_bo.sort_direction == SortDirection.DESC,
            )
        elif query_bo.sort_by == SortBy.Token:
            res.sort(
                key=lambda x: x.token,
                reverse=query_bo.sort_direction == SortDirection.DESC,
            )
        count, page_count, token_dict_list = paginate_by_list(
            res, query_bo.current_page - 1, query_bo.page_size
        )
        page = PaginationByPage(
            total_records=count,
            total_pages=page_count,
            current_page=query_bo.current_page,
            page_size=query_bo.page_size,
        )
        return token_dict_list, page

    @classmethod
    def query_by_wallet_uuids(cls, wallet_uuids: List[str]) -> Dict[str, str]:
        return {w.uuid: w.name for w in CustodyWalletDao.get_by_uuids(wallet_uuids)}

    @classmethod
    def query_by_wallet_addresses(
        cls, wallet_addresses: List[str]
    ) -> Dict[str, WaaS2WalletByWalletAddressBO]:
        extra_info_map = {
            (info.chain_coin, info.address): info
            for info in MPCWalletAddressExtraInfoDao.list_by_only_addresses(
                wallet_addresses
            )
        }
        wallet_addresses = WalletAddressDao.list_by_address(wallet_addresses)
        wallet_ids = [w.custody_wallet_id for w in wallet_addresses]
        sub_types = MPCWalletUtils.get_wallet_subtype(wallet_ids)
        custody_wallet_map: Dict[int, CustodyWallet] = {
            w.id: w for w in CustodyWalletDao.list_by_ids(wallet_ids)
        }
        res = {}
        for address in wallet_addresses:
            custody_wallet = custody_wallet_map.get(address.custody_wallet_id, None)
            if not custody_wallet or custody_wallet.config_type not in [
                CustodyWallet.WT_MPC,
                CustodyWallet.WT_MPC_WEB3,
            ]:
                continue
            token_info = WaaSTokenManager.get_token(token_id=address.chain_coin)  # 主链币
            extra_info = extra_info_map.get((address.chain_coin, address.address), None)
            encoding = MPCWalletUtils.get_encoding(extra_info, token_info)
            address_str, memo = get_address_memo(address.address)
            res[address.address] = WaaS2WalletByWalletAddressBO(
                wallet_id=custody_wallet.uuid,
                wallet_name=custody_wallet.name,
                wallet_type=WalletType.MPC,
                wallet_subtype=sub_types.get(custody_wallet.id),
                encoding=encoding,
                memo=memo,
            )
        return res

    @classmethod
    def query_wallet_by_org_id(cls, org_id: str) -> List[WaaS2WalletByOrgIdBO]:
        org = OrganizationDao.query_by_uuid(org_id)
        custody_wallets = CustodyWalletDao.list_by_org_id_config_type(
            org_id=org.id, config_type=MPCWalletUtils.get_mpc_wallet_types()
        )
        results = []
        sub_types = MPCWalletUtils.get_wallet_subtype([w.id for w in custody_wallets])
        vault_project_info = MPCWalletUtils.get_vault_and_project_by_wallet(
            custody_wallets
        )
        for wallet in custody_wallets:
            wallet_dict = dict(
                wallet_id=wallet.uuid,
                wallet_name=wallet.name,
                wallet_type=WalletType.MPC,
                wallet_subtype=sub_types.get(wallet.id),
                vault_id=vault_project_info.get(wallet.uuid, {}).get("vault_id"),
                vault_name=vault_project_info.get(wallet.uuid, {}).get("vault_name"),
            )
            if sub_types.get(wallet.id) == WalletSubtype.OrgControlled:
                bo = WaaS2OcwWalletByOrgIdBO(**wallet_dict)
            else:
                bo = WaaS2UcwWalletByOrgIdBO(
                    **wallet_dict,
                    project_id=vault_project_info.get(wallet.uuid, {}).get(
                        "project_id"
                    ),
                    project_name=vault_project_info.get(wallet.uuid, {}).get(
                        "project_name"
                    ),
                )
            results.append(bo)
        return results

    @classmethod
    def get_wallet_by_wallet_uuid(
        cls, wallet_id: str
    ) -> Optional[WaaS2WalletByWalletIdBO]:
        wallet = CustodyWalletDao.get_by_uuid(wallet_uuid=wallet_id)
        if not wallet:
            return None

        sub_type_map = MPCWalletUtils.get_wallet_subtype([wallet.id])
        return WaaS2WalletByWalletIdBO(
            wallet_id=wallet.uuid,
            wallet_name=wallet.name,
            wallet_type=WalletType.MPC,
            wallet_subtype=sub_type_map.get(wallet.id),
        )

    @classmethod
    def send_transaction(
        cls, web3_transaction: Web3TransactionCreateBO
    ) -> Web3TransactionBO:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            web3_transaction.wallet_id
        )
        request_id = web3_transaction.request_id
        chain_coin = MPCWalletUtils.get_chain_code_by_id(
            web3_transaction.tx_params["chainId"]
        )
        check(chain_coin)
        tr, _ = TransactionRequestController.create_request(
            org_id=custody_wallet.org.pk,
            request_id=request_id,
            from_custody_wallet=custody_wallet,
            from_address=web3_transaction.tx_params["from"],
            chain_coin=chain_coin,
            asset_coin=chain_coin,
            fee_rate=web3_transaction.tx_params.get("gasPrice"),
            gas_limit=web3_transaction.tx_params.get("gas"),
            max_fee=web3_transaction.tx_params.get("maxFeePerGas"),
            max_priority_fee=web3_transaction.tx_params.get("maxPriorityFeePerGas"),
            to_address=web3_transaction.tx_params["to"],
            extra_args=MPCExtraParameters(
                calldata=web3_transaction.tx_params["data"]
            ).to_json(),
            operation=TransactionRequestOperation.OPERATION_CONTRACT_CALL,
            request_type=TransactionRequestType.TYPE_MPC_WEB
            if web3_transaction.web_request
            else TransactionRequestType.TYPE_MPC_API,
            user=web3_transaction.request_user,
            sign_info=web3_transaction.sign_info,
            api_request_info=web3_transaction.api_request_info,
            remark=web3_transaction.description,
        )
        wr = WithdrawRequestDao.get_by_org_and_request_id_or_null(
            tr.org_id, tr.request_id
        )
        WalletEventService.dispatch_withdraw_request(wr)

        ref_transactions = TransactionQueryManager.create_transaction(
            source=QueryTransactionSourceType.WithdrawRequest.value, ref_id=wr.id
        )
        txid = (
            ref_transactions[0].uuid
            if ref_transactions and len(ref_transactions) > 0
            else ""
        )
        return Web3TransactionBO(transaction_id=txid)

    @classmethod
    def pre_transaction(
        cls, web3_transaction: Web3TransactionCreateBO
    ) -> EstimateFeeBO:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            web3_transaction.wallet_id
        )
        chain_coin = MPCWalletUtils.get_chain_code_by_id(
            web3_transaction.tx_params["chainId"]
        )
        check(chain_coin)

        token_info = WaaSTokenManager.get_token(token_id=chain_coin)  # 主链币

        supported_fee_models = WaaSChainUtils.get_chain_supported_fee_models(
            chain_id=chain_coin
        )

        account_sd = SourceDestination(
            source=EOAFeeSourceAccountRequest(
                source_type=SourceTypeEnum.SRC_MODEL_ACCOUNT,
                wallet_id=str(custody_wallet.remote_wallet_id),
                address=web3_transaction.tx_params["from"],
            ),
            destination=EOAFeeDestinationAccountRequest(
                destination_type=DestinationTypeEnum.DST_MODEL_ACCOUNT,
                address=web3_transaction.tx_params["to"],
                amount=str(web3_transaction.tx_params.get("value", "0")),
                calldata=web3_transaction.tx_params["data"],
            ),
        )

        estimate_tx_fee_result = BlockchainFeeManagerEOAMPC.estimate_tx_fee(
            params=EstimateTxFeeParams(
                fee_types=supported_fee_models,
                asset_coin=token_info.token_id,
                chain_coin=token_info.chain_id,
                account=account_sd,
            )
        )

        fee_token_info = WaaSTokenManager.get_token(
            token_info.fee_token_id,
            ignore_token_rate=False,
        )
        coin_rates = CoinCurrencyRateManager.get_rates(
            coins=[fee_token_info.token_id], currency=settings.CURRENCY_USD
        )
        fee_coin_rate = coin_rates.get(fee_token_info.token_id, Decimal(0))

        fee_detail_bo = FeeDetailBO(
            legacy=estimate_tx_fee_result.to_legacy(
                fee_token_info.decimal, fee_coin_rate
            ),
            eip1559=estimate_tx_fee_result.to_eip1559(
                fee_token_info.decimal, fee_coin_rate
            ),
            # fee_unit=fee_token_info,
            fee_decimal=fee_token_info.decimal,
        )

        fee_amount = FeeAmountBO(
            fee_amount=fee_detail_bo.legacy.standard.fee_amount,
            usd_amount=fee_detail_bo.legacy.standard.usd_amount,
        )

        if web3_transaction.tx_params.get("maxFeePerGas"):
            fee_amount = FeeAmountBO(
                fee_amount=fee_detail_bo.eip1559.standard.fee_amount,
                usd_amount=fee_detail_bo.eip1559.standard.usd_amount,
            )
        if web3_transaction.tx_params.get("gasPrice") or web3_transaction.tx_params.get(
            "maxFeePerGas"
        ):
            custom_fee = BlockChainEntityFeeDetail(
                fee_type=FeeModelType.TYPE_EVM_EIP_1559,
                fee_token_id=token_info.fee_token_id,
            )
            if web3_transaction.tx_params.get("maxFeePerGas"):
                custom_fee.fee_type = FeeModelType.TYPE_EVM_EIP_1559
                custom_fee.max_priority_fee_per_gas = str(
                    web3_transaction.tx_params.get("maxPriorityFeePerGas")
                )
                custom_fee.max_fee_per_gas = str(
                    web3_transaction.tx_params.get("maxFeePerGas")
                )

            if web3_transaction.tx_params.get("gasPrice"):
                custom_fee.fee_type = FeeModelType.TYPE_EVM_LEGACY
                custom_fee.gas_price = str(web3_transaction.tx_params.get("gasPrice"))

            custom_fee_detail = BlockchainFeeManagerEOAMPC.estimate_per_tx_fee(
                params=EstimateTxFeeDetailParams(
                    asset_coin=token_info.token_id,
                    chain_coin=token_info.chain_id,
                    custom_fee=custom_fee,
                    account=account_sd,
                )
            )
            fee_detail = custom_fee_detail.calc_fee_amount(
                fee_token_info.decimal, fee_coin_rate
            )
            fee_amount = fee_detail.to_bo()

        fee_detail_bo.fee = fee_amount

        fee_token_address_balance = BalanceManager.get_token_address_balance(
            org_id=custody_wallet.org.pk,
            org_uuid=custody_wallet.org.uuid,
            token_id=fee_token_info.token_id,
            address=web3_transaction.tx_params["from"],
            custody_wallet_id=custody_wallet.pk,
        )
        fee_token_balance = 0
        fee_token_abs_balance = Decimal(0)
        fee_value = Decimal(0)
        last_activity_timestamp = 0
        if fee_token_address_balance:
            last_activity_timestamp = (
                int(fee_token_address_balance.modified_time.timestamp()) * 1000
                if fee_token_address_balance.modified_time
                else 0
            )
            fee_token_abs_balance = fee_token_address_balance.available_balance
            fee_token_balance, fee_value = MPCWalletUtils.calc_token_value_balance(
                Decimal(fee_token_info.usd_rate),
                fee_token_info,
                int(fee_token_abs_balance),
            )
        return EstimateFeeBO(
            fee_token_balance=WalletTokenBalance(
                token_id=token_info.token_id,
                chain_id=fee_token_info.chain_id,
                balance=fee_token_balance,
                transferable_balance=fee_token_balance,
                abs_balance=fee_token_abs_balance,
                currency_balance=fee_value,
                last_activity_timestamp=last_activity_timestamp,
            ),
            fee=fee_detail_bo,
        )

    @classmethod
    def list_wallet_tokens(
        cls, query: QueryWalletTokensRequestBO
    ) -> Tuple[List[QueryWalletTokensResponseBO], PaginationByPage]:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            query.wallet_id
        )

        chain_identifier = None

        if query.address:
            query_set = (
                WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                    custody_wallet_id=custody_wallet.uuid,
                    addresses=[query.address],
                )
            )

            address_list = list(query_set)
            if not address_list:
                return [], PaginationByPage(
                    page_size=query.page_size,
                    current_page=query.current_page + 1,
                )
            chain_identifier = address_list[0].chain_identifier

        enabled_tokens = WaaSTokenManager.list_enabled_tokens_by_wallet_type(
            ref_org_id=custody_wallet.org.uuid,
            wallet_type=WalletType.MPC,
            chain_ids=query.chain_id,
            token_ids=query.token_ids,
            chain_identifiers=chain_identifier,
        )

        if query.keyword:
            enabled_tokens = WaaSTokenManager.filter_tokens_by_keyword(
                tokens=enabled_tokens, keyword=query.keyword
            )

        chain_info_map = (
            WaaSChainManager.list_chains(
                # TODO: 这里需要传ref_org_id, 确保返回给前端的数据中，chain的supported_address_encoding正确
                chain_ids=[t.chain_id for t in enabled_tokens],
                ignore_address_encoding=False,
                ignore_explorer_tx_url=False,
            )
            .key_by(lambda x: x.chain_id)
            .map_values(WaaSChainUtils.to_chain_info)
            .to_dict()
        )
        wallet_tokens: List[QueryWalletTokensResponseBO] = []
        collection_address_map = (
            AutoSweepCollectAddressProcessor.get_wallet_token_collection_addresses(
                custody_wallet, [t.chain_id for t in enabled_tokens]
            )
        )
        wallet_balance = BalanceManager.get_wallet_balance_by_wallet_id(
            org_uuid=custody_wallet.org.uuid,
            org_id=custody_wallet.org.pk,
            wallet_id=custody_wallet.pk,
            address=query.address,
        )

        # TODO: 这个get rates效率比较低，后续需要优化
        coin_rates = CoinCurrencyRateManager.get_rates(
            coins=[t.asset_code for t in wallet_balance.filter_token_assets()],
            currency=settings.CURRENCY_USD,
        )

        token_auto_sweep_balance_map = (
            AutoSweepBalanceProcessor.get_wallet_token_balance(
                wallet=custody_wallet,
                token_ids=[t.token_id for t in enabled_tokens],
                wallet_balance=wallet_balance,
            )
        )

        logger.info(
            f"list_wallet_tokens compact start enabled_tokens {len(enabled_tokens)}"
        )

        suspend_deposit_tokens = set(SuspendCoinManager.get_receive_coins())
        suspend_withdraw_tokens = set(SuspendCoinManager.get_sent_coins())
        for token_info in enabled_tokens:
            token_wallet_balance = wallet_balance.get_token_wallet_balance(
                token_info.token_id
            )
            screening_balance = token_wallet_balance.to_screening_balance_bo(
                coin_rates.get(token_info.token_id, Decimal(0)),
                token_info.decimal,
            )

            balance = token_wallet_balance.total_balance
            transferable_balance = token_wallet_balance.available_balance
            (
                abs_balance,
                currency_balance,
            ) = MPCWalletUtils.calc_token_value_balance(
                coin_rates.get(token_info.token_id, Decimal(0)),
                token_info,
                int(token_wallet_balance.total_balance),
            )

            (
                transferable_abs_balance,
                transferable_currency_balance,
            ) = MPCWalletUtils.calc_token_value_balance(
                coin_rates.get(token_info.token_id, Decimal(0)),
                token_info,
                int(transferable_balance),
            )

            last_activity_timestamp = token_wallet_balance.last_modified_timestamp

            token_balance = WalletTokenBalance(
                token_id=token_info.token_id,
                chain_id=token_info.chain_id,
                balance=int(balance),
                transferable_balance=int(transferable_balance),
                abs_balance=abs_balance,
                currency_balance=currency_balance,
                last_activity_timestamp=last_activity_timestamp,
                transferable_currency_balance=transferable_currency_balance,
                transferable_abs_balance=transferable_abs_balance,
            )

            if query.min_last_activity_timestamp and (
                not token_balance.last_activity_timestamp
                or token_balance.last_activity_timestamp
                < query.min_last_activity_timestamp
            ):
                continue

            if query.max_last_activity_timestamp and (
                not token_balance.last_activity_timestamp
                or token_balance.last_activity_timestamp
                > query.max_last_activity_timestamp
            ):
                continue

            if query.min_value and token_balance.currency_balance < query.min_value:
                continue

            if query.max_value and token_balance.currency_balance > query.max_value:
                continue

            if query.hide_zero_balance and token_balance.balance <= 0:
                continue

            wallet_token = QueryWalletTokensResponseBO(
                token_balance=token_balance,
                token=WrappedToken(
                    **WaaSTokenUtils.to_token_info(token_info).to_dict(),
                    enabled=True,
                    can_deposit=token_info.token_id not in suspend_deposit_tokens,
                    can_withdraw=token_info.token_id not in suspend_withdraw_tokens,
                ),
                chain=chain_info_map.get(token_info.chain_id, None),
                collection_address=collection_address_map.get(token_info.chain_id),
                auto_sweep_balance=token_auto_sweep_balance_map.get(
                    token_info.token_id, None
                ),
                screening_balance=screening_balance,
            )

            if query.balance_type == "sweep":
                if (
                    not wallet_token.auto_sweep_balance
                    or wallet_token.auto_sweep_balance.unswept_token_count != 0
                ):
                    continue
            elif query.balance_type == "unswept":
                if (
                    not wallet_token.auto_sweep_balance
                    or wallet_token.auto_sweep_balance.unswept_token_count == 0
                ):
                    continue

            wallet_tokens.append(wallet_token)

        logger.info(f"list_wallet_tokens sort start {query.sort_by}")
        wallet_tokens.sort(key=lambda x: x.token.symbol)
        if not query.sort_direction:
            query.sort_direction = SortDirection.DESC
        # sort
        if query.sort_by == SortBy.LastActivityTimestamp:
            wallet_tokens.sort(
                key=lambda x: x.token_balance.last_activity_timestamp,
                reverse=query.sort_direction == SortDirection.DESC,
            )
        elif query.sort_by == SortBy.Withdrawable:
            wallet_tokens.sort(
                key=lambda x: x.auto_sweep_balance.swept_currency_value
                if x.auto_sweep_balance
                else 0,
                reverse=query.sort_direction == SortDirection.DESC,
            )
        elif query.sort_by == SortBy.Unswept:
            wallet_tokens.sort(
                key=lambda x: x.auto_sweep_balance.unswept_currency_value
                if x.auto_sweep_balance
                else 0,
                reverse=query.sort_direction == SortDirection.DESC,
            )
        else:
            wallet_tokens.sort(
                key=lambda x: (
                    x.token_balance.currency_balance,
                    x.token_balance.balance,
                    x.token.token_id in [COIN_BTC, COIN_ETH],
                ),
                reverse=query.sort_direction == SortDirection.DESC,
            )

        logger.info(f"list_wallet_tokens paginate start {len(wallet_tokens)}")
        count, page_count, res = paginate_by_list(
            wallet_tokens, query.current_page, query.page_size
        )
        page = PaginationByPage(
            total_records=count,
            total_pages=page_count,
            current_page=query.current_page + 1,
            page_size=query.page_size,
        )

        return res, page

    @classmethod
    def wallet_address_summary(
        cls, query: WaaS2WalletAddressSummaryBO
    ) -> Tuple[List[WalletAddressSummaryResponseBO], PaginationByPage]:
        custody_wallet = BaseWalletProcessor.get_custody_wallet_and_check(
            query.wallet_id
        )
        query_set = WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
            custody_wallet_id=custody_wallet.uuid,
            addresses=[query.address],
        )

        address_list = list(query_set)
        if not address_list:
            return [], PaginationByPage(
                page_size=query.page_size,
                current_page=query.current_page + 1,
            )
        chain_identifier = address_list[0].chain_identifier

        supported_tokens = WaaSTokenManager.list_supported_tokens_by_wallet_type(
            ref_org_id=custody_wallet.org.uuid,
            wallet_type=WalletType.MPC,
            chain_identifiers=chain_identifier,
        )

        token_info_map = {c.token_id: c for c in supported_tokens}

        wallet_balance = BalanceManager.get_wallet_balance_by_wallet_id(
            org_id=custody_wallet.org.pk,
            org_uuid=custody_wallet.org.uuid,
            wallet_id=custody_wallet.pk,
            address=query.address,
            token_ids=[t.token_id for t in supported_tokens],
            use_available=False,
        )

        token_assets = wallet_balance.filter_token_assets()

        chain_token_map: Dict[str, List[TokenAsset]] = {}
        for asset in token_assets:
            chain_token_map.setdefault(asset.chain_coin, []).append(asset)
        # TODO: 这个get rates效率比较低，后续需要优化
        coin_rates = CoinCurrencyRateManager.get_rates(
            coins=[t.asset_code for t in token_assets], currency=settings.CURRENCY_USD
        )
        # TODO: 这里不应该用OrgPackageManager，应该用list_enabled_chains_by_wallet_type
        # OrgPackageManager.get_selected_chains没有过滤掉过期套餐
        enabled_chains = OrgPackageManager.get_selected_chains(
            org_id=custody_wallet.org.uuid,
            wallet_type=WalletType.MPC,
        )
        extended_chain_info_map = {
            c.chain_id: ExtendedChainInfo(
                **WaaSChainUtils.to_chain_info(c).to_dict(),
                in_used=c.chain_id in enabled_chains,
            )
            for c in WaaSChainManager.list_supported_chains_by_wallet_type(
                ref_org_id=custody_wallet.org.uuid,
                wallet_type=WalletType.MPC,
                ignore_address_encoding=False,
                ignore_explorer_tx_url=False,
            )
            if c.chain_identifier == chain_identifier
        }
        wallet_address_summary: List[WalletAddressSummaryResponseBO] = []

        for chain_id, extended_chain_info in extended_chain_info_map.items():
            if query.chain_in_used and extended_chain_info.in_used is not True:
                continue
            token_assets = chain_token_map.get(chain_id, [])
            currency_balance = Decimal(0)
            last_activity_timestamp = 0
            for asset in token_assets:
                token_info = token_info_map.get(asset.asset_code, None)
                if not token_info:
                    continue
                (
                    token_abs_balance,
                    token_currency_balance,
                ) = MPCWalletUtils.calc_token_value_balance(
                    coin_rates.get(asset.asset_code, Decimal(0)),
                    token_info,
                    int(asset.balance),
                )
                currency_balance += token_currency_balance
                token_last_activity_timestamp = (
                    int(asset.modified_time.timestamp()) * 1000
                )
                if token_last_activity_timestamp > last_activity_timestamp:
                    last_activity_timestamp = token_last_activity_timestamp
            wallet_address_summary.append(
                WalletAddressSummaryResponseBO(
                    chain=extended_chain_info,
                    currency_balance=currency_balance,
                    last_activity_timestamp=last_activity_timestamp,
                )
            )

        if query.hide_zero_balance:
            wallet_address_summary = [
                c for c in wallet_address_summary if c.currency_balance > 0
            ]

        if not query.sort_direction:
            query.sort_direction = SortDirection.DESC
        # sort
        if query.sort_by == SortBy.LastActivityTimestamp:
            wallet_address_summary.sort(
                key=lambda x: x.last_activity_timestamp,
                reverse=query.sort_direction == SortDirection.DESC,
            )
        else:
            wallet_address_summary.sort(
                key=lambda x: (
                    x.currency_balance,
                    x.chain.chain_id in [COIN_BTC, COIN_ETH],
                    x.chain.chain_id,
                ),
                reverse=query.sort_direction == SortDirection.DESC,
            )

        count, page_count, res = paginate_by_list(
            wallet_address_summary, query.current_page, query.page_size
        )
        page = PaginationByPage(
            total_records=count,
            total_pages=page_count,
            current_page=query.current_page + 1,
            page_size=query.page_size,
        )

        return res, page

    @classmethod
    def resend_transaction(cls, params: ResendTransactionBO):
        return MPCWalletTransactionProcessor.resend_transaction(params)

    @classmethod
    def cancel_transaction(cls, params: CancelTransactionBO):
        return MPCWalletTransactionProcessor.cancel_transaction(params)

    @classmethod
    def speedup_transaction(cls, params: RBFTransactionBO):
        return MPCWalletTransactionProcessor.speedup_transaction(params)

    @classmethod
    def drop_transaction(cls, params: RBFTransactionBO):
        return MPCWalletTransactionProcessor.drop_transaction(params)

    @classmethod
    def get_max_transferable_value(cls, query_bo: MaxTransferableValueBO) -> Dict:
        token_id = query_bo.token_id
        to_address = query_bo.to_address
        from_address = query_bo.from_address
        token_info = WaaSTokenManager.get_token(token_id=token_id, nullable=True)
        if not token_info:
            raise ApiInvalidParamException(f"token of {token_id} not support")
        fee_token = WaaSTokenManager.get_token(
            token_id=token_info.fee_token_id,
            ignore_token_rate=False,
        )
        fee_usd_rate = Decimal(fee_token.usd_rate)
        custody_wallet = BaseWalletProcessor.get_wallet_info(query_bo.wallet_id)
        check(
            custody_wallet,
            ApiInvalidParamException,
            f"The wallet id of {query_bo.wallet_id} invalid",
        )

        if not WalletAddressManager.is_valid(
            coin_code=token_info.chain_id, address=to_address
        ):
            raise ApiInvalidParamException(
                f"Invalid to address:{to_address} with token:{token_id}"
            )

        if not token_info.is_utxo and not from_address:
            raise ApiInvalidParamException(f"Invalid from address for {token_id}")

        if from_address:
            balance = BalanceManager.get_token_address_balance(
                org_id=custody_wallet.org_id,
                org_uuid=query_bo.org_id,
                token_id=token_info.token_id,
                address=from_address,
                custody_wallet_id=custody_wallet.id,
            )
        else:
            balance = BalanceManager.get_token_wallet_balance(
                org_id=custody_wallet.org_id,
                org_uuid=query_bo.org_id,
                token_id=token_info.token_id,
                custody_wallet_id=custody_wallet.id,
            )

        if token_info.is_utxo and (
            balance.using_balance > 0 or balance.pending_screening_balance > 0
        ):
            raise ApiInvalidActionException(
                ApiExceptionCode.message(
                    ApiExceptionCode.EXIST_PROCESSIONG_TRANSACTION
                ),
                ApiExceptionCode.EXIST_PROCESSIONG_TRANSACTION,
            )

        use_fee_station = False
        legacy = None
        if query_bo.legacy or query_bo.eip1559 or query_bo.fixed_fee:
            legacy = query_bo.legacy
            if query_bo.fixed_fee:
                legacy = FeeLegacyParamBO(
                    gas_limit=1,
                    gas_price=combine_string_to_decimal(
                        str(query_bo.fixed_fee.fee_amount),
                        decimal_bit=token_info.decimal if token_info else 0,
                    ),
                )
            fee_station_pre_transfer = FeeStationManagerV2.pre_transfer(
                org_id=custody_wallet.org_id,
                token_id=token_id,
                from_wallet_uuid=custody_wallet.uuid,
                from_address=from_address,
                amount=int(balance.available_balance),
                legacy=legacy,
                eip1559=query_bo.eip1559,
            )
            use_fee_station = (
                fee_station_pre_transfer.is_use
                and fee_station_pre_transfer.is_sufficient
            )

        account_sd = (
            SourceDestination(
                source=EOAPreTransferSourceAccountRequest(
                    source_type=SourceTypeEnum.SRC_MODEL_ACCOUNT,
                    wallet_id=str(custody_wallet.remote_wallet_id),
                    address=query_bo.from_address,
                ),
                destination=EOAPreTransferDestinationAccountRequest(
                    destination_type=DestinationTypeEnum.DST_MODEL_ACCOUNT,
                    address=query_bo.to_address,
                    amount="0",
                ),
            )
            if not token_info.is_utxo
            else None
        )

        utxo_sd = (
            SourceDestination(
                source=EOAPreTransferSourceUTXORequest(
                    source_type=SourceTypeEnum.SRC_MODEL_UTXO,
                    wallet_id=str(custody_wallet.remote_wallet_id),
                    address=query_bo.from_address,
                ),
                destination=EOAPreTransferDestinationUTXORequest(
                    destination_type=DestinationTypeEnum.DST_MODEL_UTXO,
                    outputs=[
                        EOATransferDestinationUTXOOutputRequest(
                            address=query_bo.to_address,
                            amount="0",
                        )
                    ],
                    change_address=query_bo.from_address,
                ),
            )
            if token_info.is_utxo
            else None
        )

        custom_fee = BlockChainEntityFeeDetail(
            fee_type=FeeModelType.TYPE_FIXED,
            fee_token_id=token_info.fee_token_id,
        )

        if query_bo.utxo:
            custom_fee.fee_type = FeeModelType.TYPE_UTXO
            custom_fee.fee_rate = query_bo.utxo.fee_rate

        if query_bo.eip1559:
            custom_fee.fee_type = FeeModelType.TYPE_EVM_EIP_1559
            custom_fee.max_priority_fee_per_gas = str(query_bo.eip1559.max_priority_fee)
            custom_fee.max_fee_per_gas = str(query_bo.eip1559.max_fee)
            custom_fee.gas_limit = str(query_bo.eip1559.gas_limit)

        if query_bo.legacy:
            custom_fee.fee_type = FeeModelType.TYPE_EVM_LEGACY
            custom_fee.gas_price = str(query_bo.legacy.gas_price)
            custom_fee.gas_limit = str(query_bo.legacy.gas_limit)

        if query_bo.fixed_fee:
            custom_fee.fee_type = FeeModelType.TYPE_FIXED
            custom_fee.max_fee_amount = str(
                combine_string_to_decimal(
                    str(query_bo.fixed_fee.fee_amount),
                    decimal_bit=token_info.decimal if token_info else 0,
                )
            )

        if query_bo.sol_fee:
            custom_fee.fee_type = FeeModelType.TYPE_SOL
            custom_fee.fee_sol = query_bo.sol_fee

        if query_bo.fil_fee:
            custom_fee.fee_type = FeeModelType.TYPE_FIL
            custom_fee.fee_fil = query_bo.fil_fee

        max_transfer_value_result = BlockchainFeeManagerEOAMPC.get_max_transfer_value(
            params=GetMaxTransferableAmountParams(
                wallet_id=str(custody_wallet.remote_wallet_id),
                chain_coin=token_info.chain_id,
                asset_coin=token_id,
                account=account_sd,
                utxo=utxo_sd,
                custom_fee=custom_fee,
            )
        )

        if token_info.is_utxo:
            send_max_value = Decimal(max_transfer_value_result.send_max_value)
        else:
            send_max_value = balance.available_balance
        if (
            token_info.token_id == fee_token.token_id
            and not token_info.is_utxo
            and not use_fee_station
        ):
            send_max_value = max(
                balance.available_balance
                - Decimal(max_transfer_value_result.fee.estimated_fee_amount),
                Decimal(0),
            )

        max_transferable_value = divide_decimal_to_string(
            send_max_value, token_info.decimal
        )

        result = MaxTransferableValue(
            max_transferable_value=max_transferable_value,
            fee=FeeDetail(
                legacy=max_transfer_value_result.fee.to_fee_legacy_bo(
                    fee_token.decimal, fee_usd_rate
                ),
                utxo=max_transfer_value_result.fee.to_fee_utxo_bo(
                    fee_token.decimal, fee_usd_rate
                ),
                eip1559=max_transfer_value_result.fee.to_fee_eip1559_bo(
                    fee_token.decimal, fee_usd_rate
                ),
                fixed_fee=max_transfer_value_result.fee.to_fixed_bo(
                    fee_token.decimal, fee_usd_rate
                ),
                fil_fee=max_transfer_value_result.fee.to_fee_fil_bo(
                    fee_token.decimal, fee_usd_rate
                ),
                sol_fee=max_transfer_value_result.fee.to_fee_sol_bo(
                    fee_token.decimal, fee_usd_rate
                ),
            ),
        )
        return result.to_dict()

    @classmethod
    def _summary_wallets_token(cls, org_id: str, custody_wallets) -> int:
        return len(cls.calc_has_balance_tokens(org_id, custody_wallets))

    @classmethod
    def _summary_wallets_address(cls, custody_wallets) -> int:
        if not custody_wallets:
            return 0
        custody_wallet_ids = [x.uuid for x in custody_wallets]
        count = WalletChainIdentifierAddressDao.count_by_wallet_ids(custody_wallet_ids)
        return count

    @classmethod
    def _summary_wallets_value(cls, org_id: str, custody_wallets) -> Decimal:
        total_value = Decimal(0)
        if not custody_wallets:
            return total_value
        custody_wallet_ids = [x.id for x in custody_wallets]

        wallet_balances = BalanceManager.get_wallet_balances(
            org_uuid=org_id,
            org_id=custody_wallets[0].org.pk,
            wallet_ids=custody_wallet_ids,
            use_available=False,
        )
        tokens = [
            token_asset
            for wallet_balance in wallet_balances.values()
            for token_asset in wallet_balance.filter_token_assets()
        ]
        coin_rates = CoinCurrencyRateManager.get_rates(
            coins=[t.asset_code for t in tokens], currency=settings.CURRENCY_USD
        )
        enabled_tokens = WaaSTokenManager.list_enabled_tokens_by_wallet_type(
            ref_org_id=org_id,
            wallet_type=WalletType.MPC,
        )
        enabled_token_ids = {t.token_id for t in enabled_tokens}
        for token in tokens:
            if token.asset_code not in enabled_token_ids:
                continue
            coin_info = CustodyCoinManager.get_asset_coin(
                asset_coin=token.asset_code, nullable=True
            )
            if not coin_info:
                continue
            balance, value = CustodialUtils.calc_coin_value(
                coin_rates, coin_info, token.asset_code, int(token.balance)
            )
            total_value += value
        return total_value

    @classmethod
    def calc_has_balance_tokens(
        cls, org_id: str, custody_wallets: List[CustodyWallet]
    ) -> List[str]:
        if not custody_wallets:
            return []
        enabled_tokens = WaaSTokenManager.list_enabled_tokens_by_wallet_type(
            ref_org_id=org_id,
            wallet_type=WalletType.MPC,
        )
        enabled_token_ids = {t.token_id for t in enabled_tokens}
        balance_tokens = set()
        wallet_balances = BalanceManager.get_wallet_balances(
            org_uuid=org_id,
            org_id=custody_wallets[0].org.pk,
            wallet_ids=[w.id for w in custody_wallets],
            use_available=False,
        )

        tokens = [
            token_asset
            for wallet_balance in wallet_balances.values()
            for token_asset in wallet_balance.filter_token_assets()
        ]
        for t in tokens:
            if t.asset_code not in enabled_token_ids:
                continue
            if t.balance > 0:
                balance_tokens.add(t.asset_code)
        return list(balance_tokens)

    @classmethod
    def _to_coin_meta(cls, coin_info: AssetCoinInfo) -> CoinMeta:
        return CoinMeta(
            code=coin_info.asset_coin,
            chain=coin_info.chain_coin,
            display_code=coin_info.display_code,
            icon_url=coin_info.icon_url,
            description=coin_info.description,
            decimal=coin_info.decimal,
            fee_code=coin_info.fee_coin,
            confirming_threshold=coin_info.confirming_threshold,
            token_address=coin_info.token_address,
            dust_threshold=int(coin_info.dust_threshold),
            require_memo=coin_requires_memo(
                coin_info.chain_coin, ignore_token_check=True
            ),
        )

    @classmethod
    def _query_by_keyword(
        cls,
        keyword: str,
        ref_org_id: str,
    ) -> Tuple[bool, List[str]]:
        is_hit = False
        address_list = []
        custodial_address_books = AddressBookBizProcessor.get_by_label(
            org_id=ref_org_id, label_search=keyword, wallet_type=WalletType.Custodial
        )
        if len(custodial_address_books) > 0:
            is_hit = True
            address_list = [a.address for a in custodial_address_books]
        if not is_hit:
            address_list = [keyword]
        return is_hit, address_list

    @classmethod
    def _generate_address_for_token(
        cls,
        custody_wallet: CustodyWallet,
        token_ids: List[str],
        custody_user: CustodyUser,
    ):
        if not custody_user:
            return
        token_infos = WaaSTokenManager.list_enabled_tokens_by_wallet_type(
            ref_org_id=custody_wallet.org.uuid,
            wallet_type=WalletType.MPC,
            token_ids=token_ids if token_ids else None,
        )
        if not token_infos:
            return

        exist_addresses = (
            WalletChainIdentifierAddressDao.query_by_custody_wallet_and_coins(
                custody_wallet_id=custody_wallet.uuid,
                chain_identifier=[t.chain_identifier for t in token_infos],
                source=AddressSource.WEB,
            )
        )
        chain_addresses = {}
        for a in exist_addresses:
            chain_addresses.setdefault(a.chain_identifier, [])
            chain_addresses[a.chain_identifier].append(a)

        handled_chain_ids = set()
        for token_info in token_infos:
            if token_info.chain_id in handled_chain_ids:
                continue
            handled_chain_ids.add(token_info.chain_identifier)
            if chain_addresses.get(token_info.chain_identifier):
                continue
            chain_info = WaaSChainManager.get_chain(token_info.chain_id)
            encoding = None
            if (
                token_info.is_utxo
                and chain_info.supported_address_encodings
                and AddressEncoding.ENCODING_P2TR.value
                in chain_info.supported_address_encodings
            ):
                encoding = AddressEncoding.ENCODING_P2TR
            cls.generate_new_addresses(
                WaaS2WalletCreateAddressBO(
                    ref_org_id=custody_wallet.org.uuid,
                    biz_org_id=custody_wallet.org.id,
                    wallet_id=custody_wallet.uuid,
                    chain_id=token_info.chain_id,
                    chain=token_info.chain_id,
                    encodings=[encoding] if encoding else None,
                    custody_user=custody_user,
                    count=1,
                )
            )

    @classmethod
    def get_transaction_extra_info(
        cls, query: WaaS2QueryTransactionExtraBO
    ) -> WaaS2TransactionExtraBO:
        return MPCWalletTransactionProcessor.get_transaction_extra_info(query)

    @classmethod
    def get_auto_fuel_status(
        cls,
        org_id: int,
        chain_id: str,
        token_id: str,
        amount: int,
        mpc_from_address: str,
        wallet: CustodyWallet,
        fee_legacy: FeeLegacyParamBO = None,
        fee_eip1559: FeeEIP1559ParamBO = None,
    ) -> int:
        if not org_id or not chain_id:
            return AutoFuelType.DO_NOT_USE_AUTO_FUEL

        if chain_id not in settings.GAS_STATION_SUPPORTED_CHAIN_COIN_LIST:
            return AutoFuelType.DO_NOT_USE_AUTO_FUEL

        if not mpc_from_address:
            logger.warning(
                "[msg: mpc from address is invalid] "
                f"[org_id: {org_id}] "
                f"[chain_id: {chain_id}] "
                f"[mpc_from_address: {mpc_from_address}] ",
            )
            return AutoFuelType.DO_NOT_USE_AUTO_FUEL
        fee_station = FeeStationManagerV2.get_fee_station(org_id=org_id)

        if fee_station and fee_station.use_cobo_gas_station(token_id=token_id):
            return AutoFuelType.PASSIVE_AUTO_FUEL

        fee_station_detail = FeeStationManagerV2.pre_transfer(
            org_id=org_id,
            token_id=token_id,
            from_wallet_uuid=wallet.uuid,
            from_address=mpc_from_address,
            amount=amount,
            legacy=FeeLegacyParamBO(
                gas_price=fee_legacy.gas_price if fee_legacy else None,
                gas_limit=fee_legacy.gas_limit if fee_legacy else None,
            )
            if fee_legacy
            else None,
            eip1559=fee_eip1559,
        )

        if (
            fee_station_detail.preference_value == PaymentPreferenceValue.USE.to_name()
            and chain_id not in [COIN_TRON, COIN_TTRON]
        ):
            """
            如果是EVM链，当前 mpc from balance 的可用余额逻辑是不准确的（包含锁定余额等)
            依赖 @zhangcong 抽象出准确的 mpc from balance 余额可以使用的情况梳理
            这里，我们返回 PASSIVE_AUTO_FUEL，由 TransactionRequestController.create_request
            来判断是否使用 fee station，与1.0保持一致
            """
            return AutoFuelType.PASSIVE_AUTO_FUEL

        if not fee_station_detail.is_use:
            return AutoFuelType.DO_NOT_USE_AUTO_FUEL

        # fee station detail is_use=True
        if not fee_station_detail.is_sufficient:
            raise PortalTransferException(
                PortalTransferException.ERROR_FEE_STATION_NEED_RECHARGE
            )

        # fee station detail is_use=True and is_sufficient=True
        # TRON链，主动使用fee station
        # EVM链，主动使用fee station，对于 passive auto fuel，get_fee_station_detail已经返回 is_use=False
        logger.info(
            "[msg: auto fuel status is proactive] "
            f"[org_id: {org_id}] "
            f"[chain_id: {chain_id}] "
            f"[fee_legacy: {fee_legacy}] "
            f"[fee_eip1559: {fee_eip1559}] "
            f"[mpc_from_address: {mpc_from_address}] "
            f"[fee_station_detail: {fee_station_detail}] ",
        )
        return AutoFuelType.PROACTIVE_AUTO_FUEL
