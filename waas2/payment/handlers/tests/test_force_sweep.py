import time
import uuid
from decimal import Decimal
from unittest.mock import patch

from django.test import TestCase

from custody.coin.data.objects import AssetCoinInfo
from custody.coin.managers import CustodyCoinManager
from custody.custody.dao.organization import OrganizationDao
from waas2.auto_sweep.bo.address import TokenCollectionAddress
from waas2.auto_sweep.enum.task import AutoSweepTaskStatus
from waas2.auto_sweep.processor.auto_sweep_task import AutoSweepTaskProcessor
from waas2.auto_sweep.processor.policy import AutoSweepPolicyProcessor
from waas2.payment.dao.accounting import AddressAccountDao, PaymentAccountDao
from waas2.payment.dao.chain_config import ChainSweepThresholdDao
from waas2.payment.dao.merchant import MerchantDao
from waas2.payment.dao.psp import PaymentServiceProviderDao
from waas2.payment.dao.settlement_order import SettlementSweepDetailDao
from waas2.payment.dao.sweep import ForceSweepRequestDao
from waas2.payment.data.sweep import ForceSweepRequestExtra
from waas2.payment.enums.order_enum import AccountType
from waas2.payment.enums.sweep import ForceSweepRequestStatus, ForceSweepSource
from waas2.payment.handlers.force_sweep import ForceSweepHandler
from waas2.payment.managers.merchant import MerchantManager
from waas2.payment.models.accounting import AddressAccount
from waas2.transaction_query.enums import QueryTransactionInitiatorType
from waas2.transactions.dev.bo.transaction_query.destination import (
    TransactionAddressesDestination,
)
from waas2.transactions.dev.bo.transaction_query.source import (
    TransactionMPCWalletSource,
)
from waas2.transactions.dev.bo.transaction_query.transaction import TransactionData
from waas2.transactions.dev.enums.transaction import (
    TransactionDestinationType,
    TransactionSourceType,
    TransactionStatus,
    TransactionType,
)


class TestForceSweepHandler(TestCase):
    def setUp(self):
        self.org_id = OrganizationDao.create_org(
            default_remote_wallet_id=time.time_ns(), name="test"
        ).uuid
        self.otc_address = str(uuid.uuid4())
        self.collection_address = str(uuid.uuid4())
        self.address_1 = str(uuid.uuid4())
        self.address_2 = str(uuid.uuid4())
        self.merchant_code_1 = str(uuid.uuid4())[:20]
        self.merchant_code_2 = str(uuid.uuid4())[:20]

        self.merchant_name_1 = str(uuid.uuid4())
        self.merchant_name_2 = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.coin_info = AssetCoinInfo(
            chain_coin=self.chain_id,
            fee_coin=self.chain_id,
            asset_coin=self.token_id,
            decimal=8,
            decimal_multi=Decimal("12"),
            confirming_threshold=1,
            token_id=self.token_id,
        )
        self.amount = Decimal("1.2")
        self.wallet_id = str(uuid.uuid4())

        self.psp = PaymentServiceProviderDao.create(
            org_id=self.org_id,
            otc_addresses=self.otc_address,
            use_dedicated_threshold_usd=Decimal("1.2"),
        )
        self.merchant_1 = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code_1,
            order_num=0,
            wallet_id=self.wallet_id,
            name=self.merchant_name_1,
        )
        self.merchant_2 = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code_2,
            order_num=0,
            wallet_id=self.wallet_id,
            name=self.merchant_name_2,
        )
        self.merchant_payment_account_1 = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant_1.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
            pending_amount=Decimal("0.94"),
        )
        self.merchant_payment_account_2 = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant_2.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
            pending_amount=Decimal("1.88"),
        )
        self.psp_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.psp.id,
            account_type=AccountType.PSP,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.merchant_address_account_1_1: AddressAccount = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_1.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        self.merchant_address_account_2_1: AddressAccount = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_2.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        self.merchant_address_account_2_2: AddressAccount = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_2.id,
            address=self.address_2,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        self.psp_address_account_1_1: AddressAccount = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.2"),
        )
        self.psp_address_account_1_2: AddressAccount = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.address_2,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.2"),
        )
        self.address_account_id_2_address_account = {
            self.merchant_address_account_1_1.id: self.merchant_address_account_1_1,
            self.merchant_address_account_2_1.id: self.merchant_address_account_2_1,
            self.merchant_address_account_2_2.id: self.merchant_address_account_2_2,
            self.psp_address_account_1_1.id: self.psp_address_account_1_1,
            self.psp_address_account_1_2.id: self.psp_address_account_1_2,
        }
        ChainSweepThresholdDao.create(
            chain_id=self.chain_id, chain_identifier=self.chain_id, min_sweep_amount=1
        )

    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(AutoSweepTaskProcessor, "get_task_status")
    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    def test_process_force_sweep(
        self,
        mock_get_asset_coin,
        mock_get_collection_address,
        mock_get_task_status,
        mock_handle_batch_manual_sweep,
    ):
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.collection_address
        )
        mock_get_asset_coin.return_value = self.coin_info

        request = ForceSweepRequestDao.create(
            org_id=self.org_id,
            request_id=str(uuid.uuid4()),
            wallet_id=self.wallet_id,
            token_id=self.token_id,
            amount=self.amount,
            source=ForceSweepSource.USER,
        )

        sweep_task_ids = ["123"]
        mock_handle_batch_manual_sweep.return_value = sweep_task_ids
        with self.subTest("process sweep"):
            request = ForceSweepHandler.process_force_sweep(request=request)
            self.assertEqual(request.status, ForceSweepRequestStatus.PENDING_SWEEP)
            extra = ForceSweepRequestExtra.from_json(request.extra)
            self.assertEqual(sweep_task_ids, extra.sweep_task_ids)
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=sweep_task_ids
            )
            self.assertEqual(len(sweep_details), 2)
            self.assertEqual(
                sweep_details[0].address_account_id,
                self.merchant_address_account_1_1.id,
            )
            self.assertEqual(
                sweep_details[0].sweep_amount, self.merchant_address_account_1_1.amount
            )
            self.assertEqual(
                sweep_details[1].address_account_id,
                self.merchant_address_account_2_1.id,
            )
            self.assertEqual(
                sweep_details[1].sweep_amount,
                self.amount - self.merchant_address_account_1_1.amount,
            )
        with self.subTest("check sweep task processing"):
            mock_get_task_status.return_value = [AutoSweepTaskStatus.Processing, []]
            request = ForceSweepHandler.process_force_sweep(request=request)
            self.assertEqual(request.status, ForceSweepRequestStatus.PENDING_SWEEP)
        with self.subTest("check sweep task failed"):
            mock_get_task_status.return_value = [AutoSweepTaskStatus.Failed, []]
            request = ForceSweepHandler.process_force_sweep(request=request)
            self.assertEqual(request.status, ForceSweepRequestStatus.SWEEP_FAILED)

            ForceSweepRequestDao.update_status(
                request_id=request.id, to_status=ForceSweepRequestStatus.PENDING_SWEEP
            )
        with self.subTest("check sweep task success and booking"):
            mock_get_task_status.return_value = [
                AutoSweepTaskStatus.Success,
                [
                    TransactionData(
                        transaction_id=str(uuid.uuid4()),
                        wallet_id=self.wallet_id,
                        type=TransactionType.WITHDRAW,
                        status=TransactionStatus.COMPLETED,
                        initiator_type=QueryTransactionInitiatorType.API,
                        source=TransactionMPCWalletSource(
                            source_type=TransactionSourceType.MPCOrgControlled,
                            wallet_id=self.wallet_id,
                        ),
                        destination=TransactionAddressesDestination(
                            destination_type=TransactionDestinationType.Address
                        ),
                        created_timestamp=int(time.time_ns()),
                        updated_timestamp=int(time.time_ns()),
                        confirmed_num=3,
                    )
                ],
            ]
            request = ForceSweepHandler.process_force_sweep(request=request)
            self.assertEqual(request.status, ForceSweepRequestStatus.COMPLETED)
            merchant_address_account_1_1: AddressAccount = (
                AddressAccountDao.get_by_id_or_raise(
                    _id=self.merchant_address_account_1_1.id
                )
            )
            self.assertEqual(merchant_address_account_1_1.amount, Decimal("0"))
            merchant_address_account_2_1: AddressAccount = (
                AddressAccountDao.get_by_id_or_raise(
                    _id=self.merchant_address_account_2_1.id
                )
            )
            self.assertEqual(
                merchant_address_account_2_1.amount,
                self.merchant_address_account_1_1.amount
                + self.merchant_address_account_2_1.amount
                - self.amount,
            )
