import logging

from cobo_libs.utils.exceptions.tools import check
from cobo_libs.utils.lock import lock_record
from django.db import transaction

from waas2.auto_sweep.enum.task import AutoSweepTaskStatus
from waas2.payment.dao.sweep import ForceSweepRequestDao
from waas2.payment.data.address_account import SweepItem
from waas2.payment.data.sweep import ForceSweepRequestExtra
from waas2.payment.enums.sweep import ForceSweepRequestStatus, SweepType
from waas2.payment.managers.address_account import SweepAddressAccountManager
from waas2.payment.models.sweep import ForceSweepRequest

logger = logging.getLogger("waas2.payment.handler.force_sweep")


class ForceSweepHandler:
    @classmethod
    @transaction.atomic
    def process_force_sweep(cls, request: ForceSweepRequest):
        request: ForceSweepRequest = lock_record(request)

        if request.status == ForceSweepRequestStatus.WAITING_SWEEP:
            sweep_type = SweepType.MERCHANT
            if request.address:
                sweep_type = SweepType.ADDRESS

            sweep_task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=request.org_id,
                sweep_type=sweep_type,
                sweep_items=[
                    SweepItem(
                        token_id=request.token_id,
                        wallet_id=request.wallet_id,
                        address=request.address,
                    )
                ],
                force_sweep=True,
                sweep_amount=request.amount,
            )

            return ForceSweepRequestDao.update_sweep_task_ids(
                request_id=request.id,
                sweep_task_ids=sweep_task_ids,
                to_status=ForceSweepRequestStatus.PENDING_SWEEP,
            )
        elif request.status == ForceSweepRequestStatus.PENDING_SWEEP:
            extra = ForceSweepRequestExtra.from_json(request.extra)
            sweep_status = SweepAddressAccountManager.check_sweep_tasks_status(
                task_ids=extra.sweep_task_ids
            )
            if sweep_status == AutoSweepTaskStatus.Success:
                SweepAddressAccountManager.process_sweep_booking(
                    sweep_task_ids=extra.sweep_task_ids
                )
                return ForceSweepRequestDao.update_status(
                    request_id=request.id, to_status=ForceSweepRequestStatus.COMPLETED
                )
            elif sweep_status == AutoSweepTaskStatus.Failed:
                logger.error(f"sweep fail, request_id: {request.id}")
                return ForceSweepRequestDao.update_status(
                    request_id=request.id,
                    to_status=ForceSweepRequestStatus.SWEEP_FAILED,
                )
            else:
                check(sweep_status == AutoSweepTaskStatus.Processing)
                return request
        else:
            raise
