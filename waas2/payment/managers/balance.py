from decimal import Decimal
from typing import Dict, List

from custody.coin.managers import CustodyCoinManager
from waas2.payment.dao.order import PaymentOrderDao
from waas2.payment.dao.payer import PaymentPayerDao
from waas2.payment.dao.refund import RefundDao
from waas2.payment.dao.transaction import PaymentTransactionDao
from waas2.payment.data.address_account import SweepItem
from waas2.payment.enums.order_enum import AcquiringType
from waas2.payment.enums.sweep import SweepType
from waas2.payment.managers.address_account import SweepAddressAccountManager
from waas2.payment.models.merchant import Merchant
from waas2.payment.utils.decimal_util import quantize


class MerchantBalanceManager:
    @classmethod
    def list_total_received_amounts(
        cls,
        org_id: str,
        merchant_ids: List[int],
        token_id: str,
        acquiring_type: AcquiringType,
    ):
        coin_info = CustodyCoinManager.get_asset_coin(asset_coin=token_id)
        merchant_id_2_total_received_amount = {}
        if acquiring_type == AcquiringType.ORDER:
            completed_orders = PaymentOrderDao.list_completed_order(
                org_id=org_id, token_id=coin_info.asset_coin, merchant_ids=merchant_ids
            )

            for completed_order in completed_orders:
                merchant_settle_rate = completed_order.fiat_amount / (
                    completed_order.fiat_amount + completed_order.fee_fiat_amount
                )

                merchant_id_2_total_received_amount.setdefault(
                    completed_order.merchant_id, Decimal("0")
                )
                merchant_id_2_total_received_amount[
                    completed_order.merchant_id
                ] += Decimal(
                    quantize(
                        amount=completed_order.received_token_amount
                        * merchant_settle_rate,
                        decimal=coin_info.decimal,
                    )
                )
        else:
            payment_payers = PaymentPayerDao.list_payer_by_merchant_ids(
                org_id=org_id, merchant_ids=merchant_ids
            )
            payer_id_2_payer = {
                payment_payer.id: payment_payer for payment_payer in payment_payers
            }
            payment_transactions = (
                PaymentTransactionDao.list_completed_by_payer_ids_and_token_id(
                    payer_ids=[payment_payer.id for payment_payer in payment_payers],
                    token_id=coin_info.asset_coin,
                )
            )
            merchant_id_2_total_received_amount = {}
            for payment_transaction in payment_transactions:
                payer = payer_id_2_payer.get(payment_transaction.payer_id)
                merchant_id_2_total_received_amount.setdefault(
                    payer.merchant_id, Decimal("0")
                )
                merchant_id_2_total_received_amount[
                    payer.merchant_id
                ] += payment_transaction.amount

        return merchant_id_2_total_received_amount

    @classmethod
    def list_collection_balances(
        cls, org_id: str, merchants: List[Merchant], token_id: str
    ) -> Dict:
        wallet_ids = list(set([merchant.wallet_id for merchant in merchants]))

        wallet_id_2_collection_balance = {}
        for wallet_id in wallet_ids:
            (
                address_accounts,
                collection_accounts,
            ) = SweepAddressAccountManager.list_sweep_address_accounts(
                org_id=org_id,
                sweep_type=SweepType.MERCHANT,
                sweep_items=[SweepItem(token_id=token_id, wallet_id=wallet_id)],
            )

            (
                sorted_sweep_keys,
                address_coin_2_amount,
                address_coin_2_address_accounts,
            ) = SweepAddressAccountManager.check_address_coin_sweep(
                address_accounts=address_accounts
            )

            wallet_id_2_collection_balance.setdefault(wallet_id, Decimal("0"))
            for address_coin_key in sorted_sweep_keys:
                amount = address_coin_2_amount.get(address_coin_key, Decimal("0"))
                wallet_id_2_collection_balance[wallet_id] += amount
            for collection_account in collection_accounts:
                wallet_id_2_collection_balance[wallet_id] += collection_account.amount

        merchant_id_2_collection_balance = {}
        for merchant in merchants:
            merchant_id_2_collection_balance[
                merchant.id
            ] = wallet_id_2_collection_balance.get(merchant.wallet_id)

        return merchant_id_2_collection_balance

    @classmethod
    def list_order_refund_amounts(
        cls,
        org_id: str,
        merchant_ids: List[int],
        token_id: str,
    ) -> Dict:
        completed_refunds = RefundDao.list_merchant_completed_refund_by_token_id(
            org_id=org_id, merchant_ids=merchant_ids, token_id=token_id
        )

        merchant_id_2_refund_amount = {}
        for completed_refund in completed_refunds:
            merchant_id_2_refund_amount.setdefault(
                completed_refund.merchant_id, Decimal("0")
            )
            merchant_id_2_refund_amount[
                completed_refund.merchant_id
            ] += completed_refund.amount

        return merchant_id_2_refund_amount


class PspBalanceManager:
    @classmethod
    def get_developer_fee_amount(cls, org_id: str, token_id: str) -> Decimal:
        coin_info = CustodyCoinManager.get_asset_coin(asset_coin=token_id)
        developer_fee_amount = Decimal("0")
        completed_orders = PaymentOrderDao.list_completed_order(
            org_id=org_id, token_id=token_id
        )
        for completed_order in completed_orders:
            merchant_settle_rate = completed_order.fee_fiat_amount / (
                completed_order.fiat_amount + completed_order.fee_fiat_amount
            )
            developer_fee_amount += Decimal(
                quantize(
                    amount=completed_order.received_token_amount * merchant_settle_rate,
                    decimal=coin_info.decimal,
                )
            )

        return developer_fee_amount

    @classmethod
    def get_collection_balance(cls, org_id: str, token_id: str) -> Decimal:
        collection_balance = Decimal("0")

        (
            address_accounts,
            collection_accounts,
        ) = SweepAddressAccountManager.list_sweep_address_accounts(
            org_id=org_id,
            sweep_type=SweepType.PSP,
            sweep_items=[SweepItem(token_id=token_id)],
        )

        (
            sorted_sweep_keys,
            address_coin_2_amount,
            address_coin_2_address_accounts,
        ) = SweepAddressAccountManager.check_address_coin_sweep(
            address_accounts=address_accounts
        )

        for address_coin_key in sorted_sweep_keys:
            amount = address_coin_2_amount.get(address_coin_key, Decimal("0"))
            collection_balance += amount
        for collection_account in collection_accounts:
            collection_balance += collection_account.amount

        return collection_balance
