import time
import uuid
from decimal import Decimal
from unittest.mock import patch

from cobo_waas2 import WalletSubtype
from django.test import TestCase

from custody.coin.data.objects import AssetCoinInfo
from custody.coin.managers.coin import CustodyCoinManager
from custody.custody.dao.organization import OrganizationDao
from custody.wallet.managers.address import WalletAddressManager
from waas2.auto_sweep.bo.address import TokenCollectionAddress
from waas2.auto_sweep.enum.task import AutoSweepTaskStatus
from waas2.auto_sweep.processor.policy import AutoSweepPolicyProcessor
from waas2.payment.dao.accounting import AddressAccountDao, PaymentAccountDao
from waas2.payment.dao.merchant import MerchantDao
from waas2.payment.dao.psp import PaymentServiceProviderDao
from waas2.payment.dao.settlement_order import SettlementSweepDetailDao
from waas2.payment.dao.transaction import PaymentTransactionDao
from waas2.payment.enums.order_enum import AccountType, AcquiringType, RefundType
from waas2.payment.enums.refund import RefundRequestStatus
from waas2.payment.managers.accounting import (
    AddressAccountManager,
    PaymentAccountManager,
)
from waas2.payment.managers.address_account import SweepAddressAccountManager
from waas2.payment.managers.commission_fee import CommissionFeeManager
from waas2.payment.managers.merchant import MerchantManager
from waas2.payment.managers.refund import RefundManager
from waas2.payment.managers.transaction import PaymentTransactionManager
from waas2.payment.utils.decimal_util import quantize
from waas2.payment.utils.sweep_config import SweepThresholdManager
from waas2.transactions.dev.bo.transfers.source import MpcTransferSource
from waas2.transactions.dev.controllers.transaction import TransactionController


class TestRefundManager(TestCase):
    def setUp(self):
        self.org = OrganizationDao.create_org(
            default_remote_wallet_id=time.time_ns(), name="test"
        )
        self.org_id = self.org.uuid
        self.address = str(uuid.uuid4())
        self.address_1 = str(uuid.uuid4())
        self.address_2 = str(uuid.uuid4())
        self.address_3 = str(uuid.uuid4())
        self.address_4 = str(uuid.uuid4())
        self.address_5 = str(uuid.uuid4())
        self.address_6 = str(uuid.uuid4())
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code = str(time.time_ns())
        self.wallet_id = str(uuid.uuid4())
        self.wallet_name = str(uuid.uuid4())
        self.merchant_name = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"
        self.coin_info = AssetCoinInfo(
            chain_coin=self.chain_id,
            fee_coin=self.chain_id,
            asset_coin=self.token_id,
            decimal=8,
            decimal_multi=Decimal("12"),
            confirming_threshold=1,
            token_id=self.token_id,
        )
        self.from_address = str(uuid.uuid4())
        self.to_address = str(uuid.uuid4())
        self.amount = Decimal("1.2")
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code = str(time.time_ns())
        self.wallet_id = str(uuid.uuid4())
        self.merchant_name = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"

        # Create test data
        self.merchant = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code,
            order_num=0,
            wallet_id=self.wallet_id,
            name=self.merchant_name,
        )

        self.psp = PaymentServiceProviderDao.create(
            org_id=self.org_id,
            otc_addresses=self.otc_address,
            use_dedicated_threshold_usd=Decimal("1.2"),
        )

        self.merchant_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
            acquiring_type=AcquiringType.ORDER,
        )
        self.address_account_1 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.address_account_2 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.address_2,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.address_account_3 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.address_3,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.psp_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.psp.id,
            account_type=AccountType.PSP,
            token_id=self.token_id,
            chain_id=self.chain_id,
            acquiring_type=AcquiringType.ORDER,
        )
        self.address_account_4 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.PSP,
            account_id=self.psp_payment_account.id,
            address=self.address_4,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.address_account_5 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.PSP,
            account_id=self.psp_payment_account.id,
            address=self.address_5,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.address_account_6 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.PSP,
            account_id=self.psp_payment_account.id,
            address=self.address_6,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.sweep_task_ids = ["task_id_1", "task_id_2"]

    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    def test_process_waiting_refund_sweep_merchant(
        self,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_collection_address,
        mock_get_asset_coin,
    ):
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        mock_get_asset_coin.return_value = self.coin_info
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PENDING_SWEEP)
        self.assertEqual(refund.api_request_info["sweep_task_ids"], self.sweep_task_ids)
        self.assertEqual(refund.api_request_info["sweep_status"], "PENDING")

        sweep_detail_address_account_2 = (
            SettlementSweepDetailDao.list_processing_details_by_address(
                sweep_address=self.address_account_2.address,
                token_id=self.token_id,
            )
        )
        sweep_detail_address_account_3 = (
            SettlementSweepDetailDao.list_processing_details_by_address(
                sweep_address=self.address_account_3.address,
                token_id=self.token_id,
            )
        )
        self.assertEqual(sweep_detail_address_account_2[0].sweep_amount, Decimal("0.5"))
        self.assertEqual(sweep_detail_address_account_3[0].sweep_amount, Decimal("0.5"))

    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    def test_process_waiting_refund_sweep_merchant_with_no_sweep_address_account(
        self,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_collection_address,
        mock_get_asset_coin,
    ):
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        mock_get_asset_coin.return_value = self.coin_info
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.4"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.4"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PENDING_COMMISSION_FEE)

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    def test_process_pending_refund_sweep_merchant(
        self,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PENDING_COMMISSION_FEE)
        self.assertEqual(refund.api_request_info["sweep_status"], "COMPLETED")
        self.address_account_2.refresh_from_db()
        self.address_account_3.refresh_from_db()
        self.assertEqual(self.address_account_2.amount, Decimal("0"))
        self.assertEqual(self.address_account_3.amount, Decimal("0"))
        self.address_account_1.refresh_from_db()
        self.assertEqual(self.address_account_1.amount, Decimal("1"))

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    def test_process_pending_refund_sweep_merchant_with_failed_sweep_task(
        self,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Failed
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        self.assertEqual(refund.status, RefundRequestStatus.SWEEP_FAILED)

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    def test_process_pending_commission_fee_merchant(
        self,
        mock_charge_commission_fee,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_charge_commission_fee.return_value = Decimal("0.5")
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        refund = RefundManager.process_pending_commission_fee(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PENDING_TRANSFER_REQUEST)
        self.assertEqual(refund.api_request_info["calculate_fee_status"], "COMPLETED")

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    @patch.object(WalletAddressManager, "is_valid")
    def test_process_refund_transfer_request_merchant(
        self,
        mock_is_valid,
        mock_charge_commission_fee,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_is_valid.return_value = True
        mock_charge_commission_fee.return_value = Decimal("0.5")
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
        )
        PaymentAccountManager.add_available_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("0.2"),
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_1,
            amount=Decimal("0.2"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        refund = RefundManager.process_pending_commission_fee(refund)
        refund = RefundManager.process_refund_transfer_request(refund)
        self.assertEqual(
            refund.status, RefundRequestStatus.PENDING_TRANSFER_TRANSACTION
        )

        transfer_requests = PaymentTransactionManager.list_refund_transfer_request(
            refund_id=refund.id
        )
        self.assertEqual(len(transfer_requests), 1)
        self.assertEqual(transfer_requests[0].amount, self.amount)
        self.assertEqual(transfer_requests[0].to_address, self.to_address)
        self.assertEqual(
            transfer_requests[0].from_address, self.address_account_1.address
        )
        self.assertEqual(
            transfer_requests[0].payment_account_id, self.merchant_payment_account.id
        )
        self.assertEqual(
            transfer_requests[0].address_account_id, self.address_account_1.id
        )

        self.address_account_1.refresh_from_db()
        self.assertEqual(self.address_account_1.amount, Decimal("0"))
        self.assertEqual(self.address_account_1.locked_amount, Decimal("1.2"))

        self.merchant_payment_account.refresh_from_db()
        self.assertEqual(self.merchant_payment_account.available_amount, Decimal("0"))
        self.assertEqual(self.merchant_payment_account.locked_amount, Decimal("1.2"))

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    @patch.object(WalletAddressManager, "is_valid")
    @patch.object(TransactionController, "transfer")
    @patch.object(PaymentTransactionManager, "create_transfer_source")
    def test_process_pending_transfer_transaction_merchant(
        self,
        mock_create_transfer_source,
        mock_transfer,
        mock_is_valid,
        mock_charge_commission_fee,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_create_transfer_source.return_value = MpcTransferSource(
            source_type=WalletSubtype.ORG_CONTROLLED.value,
            wallet_id=self.wallet_id,
            address=self.address_1,
        )
        mock_transfer.return_value = {
            "transaction_id": "tx_id_1",
        }
        mock_is_valid.return_value = True
        mock_charge_commission_fee.return_value = Decimal("0.5")
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
        )
        PaymentAccountManager.add_available_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("0.2"),
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_1,
            amount=Decimal("0.2"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        refund = RefundManager.process_pending_commission_fee(refund)
        refund = RefundManager.process_refund_transfer_request(refund)
        refund = RefundManager.process_pending_transfer_transaction(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PROCESSING)

        payment_transaction = PaymentTransactionDao.get_by_tx_id(tx_id="tx_id_1")
        transfer_requests = PaymentTransactionManager.list_refund_transfer_request(
            refund_id=refund.id
        )
        self.assertEqual(
            transfer_requests[0].id, payment_transaction.transfer_request_id
        )

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    @patch.object(WalletAddressManager, "is_valid")
    @patch.object(TransactionController, "transfer")
    @patch.object(PaymentTransactionManager, "create_transfer_source")
    def test_process_pending_transfer_transaction_merchant_with_merchant_fee(
        self,
        mock_create_transfer_source,
        mock_transfer,
        mock_is_valid,
        mock_charge_commission_fee,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_create_transfer_source.return_value = MpcTransferSource(
            source_type=WalletSubtype.ORG_CONTROLLED.value,
            wallet_id=self.wallet_id,
            address=self.address_1,
        )
        mock_transfer.return_value = {
            "transaction_id": "tx_id_1",
        }
        mock_is_valid.return_value = True
        mock_charge_commission_fee.return_value = Decimal("0.5")
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.MERCHANT,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
            merchant=self.merchant,
            charge_merchant_fee=True,
            merchant_fee_amount=Decimal("0.1"),
            merchant_fee_token_id=self.token_id,
        )
        PaymentAccountManager.add_available_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("0.2"),
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.merchant_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_1,
            amount=Decimal("0.2"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_2,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_3,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        refund = RefundManager.process_pending_commission_fee(refund)
        refund = RefundManager.process_refund_transfer_request(refund)
        refund = RefundManager.process_pending_transfer_transaction(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PROCESSING)

        payment_transaction = PaymentTransactionDao.get_by_tx_id(tx_id="tx_id_1")
        transfer_requests = PaymentTransactionManager.list_refund_transfer_request(
            refund_id=refund.id
        )
        self.assertEqual(
            transfer_requests[0].id, payment_transaction.transfer_request_id
        )

        amount = quantize(
            (refund.amount - refund.merchant_fee_amount), self.coin_info.decimal
        )
        self.assertEqual(payment_transaction.amount, Decimal(amount))

    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    def test_process_waiting_refund_sweep_psp(
        self,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_collection_address,
        mock_get_asset_coin,
    ):
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_4,
        )
        mock_get_asset_coin.return_value = self.coin_info
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.PSP,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_5,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_6,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PENDING_SWEEP)
        self.assertEqual(refund.api_request_info["sweep_task_ids"], self.sweep_task_ids)
        self.assertEqual(refund.api_request_info["sweep_status"], "PENDING")

        sweep_detail_address_account_5 = (
            SettlementSweepDetailDao.list_processing_details_by_address(
                sweep_address=self.address_account_5.address,
                token_id=self.token_id,
            )
        )
        sweep_detail_address_account_6 = (
            SettlementSweepDetailDao.list_processing_details_by_address(
                sweep_address=self.address_account_6.address,
                token_id=self.token_id,
            )
        )
        self.assertEqual(sweep_detail_address_account_5[0].sweep_amount, Decimal("0.5"))
        self.assertEqual(sweep_detail_address_account_6[0].sweep_amount, Decimal("0.5"))

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    def test_process_pending_refund_sweep_psp(
        self,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_4,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.PSP,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.psp_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_5,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_6,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PENDING_COMMISSION_FEE)
        self.assertEqual(refund.api_request_info["sweep_status"], "COMPLETED")
        self.address_account_5.refresh_from_db()
        self.address_account_6.refresh_from_db()
        self.assertEqual(self.address_account_5.amount, Decimal("0"))
        self.assertEqual(self.address_account_6.amount, Decimal("0"))
        self.address_account_4.refresh_from_db()
        self.assertEqual(self.address_account_4.amount, Decimal("1"))

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    def test_process_pending_commission_fee_psp(
        self,
        mock_charge_commission_fee,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_charge_commission_fee.return_value = Decimal("0.5")
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_4,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.PSP,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.psp_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_5,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_6,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        refund = RefundManager.process_pending_commission_fee(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PENDING_TRANSFER_REQUEST)
        self.assertEqual(refund.api_request_info["calculate_fee_status"], "COMPLETED")

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    @patch.object(WalletAddressManager, "is_valid")
    def test_process_refund_transfer_request_psp(
        self,
        mock_is_valid,
        mock_charge_commission_fee,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_is_valid.return_value = True
        mock_charge_commission_fee.return_value = Decimal("0.5")
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_4,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.PSP,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
        )
        PaymentAccountManager.add_available_amount(
            payment_account=self.psp_payment_account,
            amount=Decimal("0.2"),
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.psp_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_4,
            amount=Decimal("0.2"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_5,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_6,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        refund = RefundManager.process_pending_commission_fee(refund)
        refund = RefundManager.process_refund_transfer_request(refund)
        self.assertEqual(
            refund.status, RefundRequestStatus.PENDING_TRANSFER_TRANSACTION
        )

        transfer_requests = PaymentTransactionManager.list_refund_transfer_request(
            refund_id=refund.id
        )
        self.assertEqual(len(transfer_requests), 1)
        self.assertEqual(transfer_requests[0].amount, self.amount)
        self.assertEqual(transfer_requests[0].to_address, self.to_address)
        self.assertEqual(
            transfer_requests[0].from_address, self.address_account_4.address
        )
        self.assertEqual(
            transfer_requests[0].payment_account_id, self.psp_payment_account.id
        )
        self.assertEqual(
            transfer_requests[0].address_account_id, self.address_account_4.id
        )

        self.address_account_4.refresh_from_db()
        self.assertEqual(self.address_account_4.amount, Decimal("0"))
        self.assertEqual(self.address_account_4.locked_amount, Decimal("1.2"))

        self.psp_payment_account.refresh_from_db()
        self.assertEqual(self.psp_payment_account.available_amount, Decimal("0"))
        self.assertEqual(self.psp_payment_account.locked_amount, Decimal("1.2"))

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepAddressAccountManager, "check_sweep_tasks_status")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(SweepThresholdManager, "get_threshold")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    @patch.object(WalletAddressManager, "is_valid")
    @patch.object(TransactionController, "transfer")
    @patch.object(PaymentTransactionManager, "create_transfer_source")
    def test_process_pending_transfer_transaction_psp(
        self,
        mock_create_transfer_source,
        mock_transfer,
        mock_is_valid,
        mock_charge_commission_fee,
        mock_get_threshold,
        mock_handle_batch_manual_sweep,
        mock_get_asset_coin,
        mock_check_sweep_tasks_status,
        mock_get_collection_address,
    ):
        mock_create_transfer_source.return_value = MpcTransferSource(
            source_type=WalletSubtype.ORG_CONTROLLED.value,
            wallet_id=self.wallet_id,
            address=self.address_1,
        )
        mock_transfer.return_value = {
            "transaction_id": "tx_id_2",
        }
        mock_is_valid.return_value = True
        mock_charge_commission_fee.return_value = Decimal("0.5")
        mock_get_threshold.return_value = Decimal("0.5")
        mock_handle_batch_manual_sweep.return_value = self.sweep_task_ids
        mock_get_asset_coin.return_value = self.coin_info
        mock_check_sweep_tasks_status.return_value = AutoSweepTaskStatus.Success
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_4,
        )
        request_id = str(uuid.uuid4())
        refund = RefundManager.create_refund(
            org_id=self.org_id,
            request_id=request_id,
            amount=str(self.amount),
            refund_type=RefundType.PSP,
            initiator="test_initiator",
            token_id=self.token_id,
            chain_id=self.chain_id,
            status=RefundRequestStatus.WAITING_SWEEP,
            to_address=self.to_address,
        )
        PaymentAccountManager.add_available_amount(
            payment_account=self.psp_payment_account,
            amount=Decimal("0.2"),
        )
        PaymentAccountManager.add_pending_amount(
            payment_account=self.psp_payment_account,
            amount=Decimal("1"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_4,
            amount=Decimal("0.2"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_5,
            amount=Decimal("0.5"),
        )
        AddressAccountManager.add_amount(
            address_account=self.address_account_6,
            amount=Decimal("0.5"),
        )
        refund = RefundManager.process_waiting_refund_sweep(refund)
        refund = RefundManager.process_pending_refund_sweep(refund)
        refund = RefundManager.process_pending_commission_fee(refund)
        refund = RefundManager.process_refund_transfer_request(refund)
        refund = RefundManager.process_pending_transfer_transaction(refund)
        self.assertEqual(refund.status, RefundRequestStatus.PROCESSING)

        payment_transaction = PaymentTransactionDao.get_by_tx_id(tx_id="tx_id_2")
        transfer_requests = PaymentTransactionManager.list_refund_transfer_request(
            refund_id=refund.id
        )
        self.assertEqual(
            transfer_requests[0].id, payment_transaction.transfer_request_id
        )
