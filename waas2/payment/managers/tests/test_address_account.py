import time
import uuid
from decimal import Decimal
from unittest.mock import patch

from django.test import TestCase

from custody.coin.data.objects import AssetCoinInfo
from custody.coin.managers import CustodyCoinManager
from custody.custody.dao.organization import OrganizationDao
from waas2.auto_sweep.bo.address import TokenCollectionAddress
from waas2.auto_sweep.processor.policy import AutoSweepPolicyProcessor
from waas2.payment.dao.accounting import AddressAccountDao, PaymentAccountDao
from waas2.payment.dao.chain_config import ChainSweepThresholdDao
from waas2.payment.dao.merchant import MerchantDao
from waas2.payment.dao.psp import PaymentServiceProviderDao
from waas2.payment.dao.settlement_order import SettlementSweepDetailDao
from waas2.payment.data.address_account import SweepItem
from waas2.payment.enums.order_enum import AccountType
from waas2.payment.enums.settlement import SweepDetailStatus
from waas2.payment.enums.sweep import SweepType
from waas2.payment.managers.address_account import SweepAddressAccountManager
from waas2.payment.managers.merchant import MerchantManager


class TestSweepAddressAccountManager(TestCase):
    def setUp(self):
        self.org_id = OrganizationDao.create_org(
            default_remote_wallet_id=time.time_ns(), name="test"
        ).uuid
        self.otc_address = str(uuid.uuid4())
        self.address_1 = str(uuid.uuid4())
        self.address_2 = str(uuid.uuid4())
        self.address_3 = str(uuid.uuid4())
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code_1 = str(uuid.uuid4())[:20]
        self.merchant_code_2 = str(uuid.uuid4())[:20]
        self.merchant_code_3 = str(uuid.uuid4())[:20]
        self.wallet_id_1 = str(uuid.uuid4())
        self.wallet_id_3 = str(uuid.uuid4())
        self.merchant_name_1 = str(uuid.uuid4())
        self.merchant_name_2 = str(uuid.uuid4())
        self.merchant_name_3 = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"
        self.coin_info = AssetCoinInfo(
            chain_coin=self.chain_id,
            fee_coin=self.chain_id,
            asset_coin=self.token_id,
            decimal=8,
            decimal_multi=Decimal("12"),
            confirming_threshold=1,
            token_id=self.token_id,
        )
        self.from_address = str(uuid.uuid4())
        self.to_address = str(uuid.uuid4())
        self.amount = Decimal("1.2")
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code = str(time.time_ns())
        self.wallet_id = str(uuid.uuid4())
        self.merchant_name = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"
        self.sweep_task_id = str(time.time_ns())

        self.psp = PaymentServiceProviderDao.create(
            org_id=self.org_id,
            otc_addresses=self.otc_address,
            use_dedicated_threshold_usd=Decimal("1.2"),
        )
        self.merchant_1_1 = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code_1,
            order_num=0,
            wallet_id=self.wallet_id_1,
            name=self.merchant_name_1,
        )
        self.merchant_2_1 = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code_2,
            order_num=0,
            wallet_id=self.wallet_id_1,
            name=self.merchant_name_2,
        )
        self.merchant_3_3 = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code_3,
            order_num=0,
            wallet_id=self.wallet_id_3,
            name=self.merchant_name_3,
        )
        self.merchant_payment_account_1_1 = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant_1_1.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.merchant_payment_account_2_1 = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant_2_1.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.merchant_payment_account_3_3 = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant_3_3.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.psp_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.psp.id,
            account_type=AccountType.PSP,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.merchant_address_account_1_1_1 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id_1,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_1_1.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        self.merchant_address_account_2_1_1 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id_1,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_2_1.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        self.merchant_address_account_2_1_2 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id_1,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_2_1.id,
            address=self.address_2,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        self.merchant_address_account_3_3_3 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id_3,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_3_3.id,
            address=self.address_3,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.7"),
        )
        self.psp_address_account_1_1_1 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id_1,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.2"),
        )
        self.psp_address_account_1_1_2 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id_1,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.address_2,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.2"),
        )
        self.psp_address_account_1_3_3 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id_3,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.address_3,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.2"),
        )
        self.address_account_id_2_address_account = {
            self.merchant_address_account_1_1_1.id: self.merchant_address_account_1_1_1,
            self.merchant_address_account_2_1_1.id: self.merchant_address_account_2_1_1,
            self.merchant_address_account_2_1_2.id: self.merchant_address_account_2_1_2,
            self.merchant_address_account_3_3_3.id: self.merchant_address_account_3_3_3,
            self.psp_address_account_1_1_1.id: self.psp_address_account_1_1_1,
            self.psp_address_account_1_1_2.id: self.psp_address_account_1_1_2,
            self.psp_address_account_1_3_3.id: self.psp_address_account_1_3_3,
        }
        ChainSweepThresholdDao.create(
            chain_id=self.chain_id, chain_identifier=self.chain_id, min_sweep_amount=1
        )

    @patch.object(MerchantManager, "get_collection_address")
    def test_list_sweep_address_accounts(self, mock_get_collection_address):
        with self.subTest("psp sweep, not token_id, collection address is None"):
            mock_get_collection_address.return_value = None
            (
                standard_address_account,
                collection_address_account,
            ) = SweepAddressAccountManager.list_sweep_address_accounts(
                org_id=self.org_id, sweep_type=SweepType.PSP, sweep_items=[]
            )
            self.assertEqual(len(standard_address_account), 7)
            self.assertEqual(len(collection_address_account), 0)

        with self.subTest(
            "psp sweep, not token_id, collection address is other address"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=str(uuid.uuid4())
            )
            (
                standard_address_account,
                collection_address_account,
            ) = SweepAddressAccountManager.list_sweep_address_accounts(
                org_id=self.org_id, sweep_type=SweepType.PSP, sweep_items=[]
            )
            self.assertEqual(len(standard_address_account), 7)
            self.assertEqual(len(collection_address_account), 0)

        with self.subTest("psp sweep, not token_id, collection address is address_1"):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_1
            )
            (
                standard_address_account,
                collection_address_account,
            ) = SweepAddressAccountManager.list_sweep_address_accounts(
                org_id=self.org_id, sweep_type=SweepType.PSP, sweep_items=[]
            )
            self.assertEqual(len(standard_address_account), 4)
            self.assertEqual(len(collection_address_account), 3)

        with self.subTest("psp sweep, not token_id, collection address is address_2"):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_2
            )
            (
                standard_address_account,
                collection_address_account,
            ) = SweepAddressAccountManager.list_sweep_address_accounts(
                org_id=self.org_id, sweep_type=SweepType.PSP, sweep_items=[]
            )
            self.assertEqual(len(standard_address_account), 5)
            self.assertEqual(len(collection_address_account), 2)

        with self.subTest(
            "merchant_1 sweep, not token_id, collection address is address_1"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_1
            )
            (
                standard_address_account,
                collection_address_account,
            ) = SweepAddressAccountManager.list_sweep_address_accounts(
                org_id=self.org_id,
                sweep_type=SweepType.MERCHANT,
                sweep_items=[SweepItem(wallet_id=self.merchant_1_1.wallet_id)],
            )
            self.assertEqual(len(standard_address_account), 2)
            self.assertEqual(len(collection_address_account), 3)

        with self.subTest(
            "merchant_1 sweep, not token_id, collection address is address_2"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_2
            )
            (
                standard_address_account,
                collection_address_account,
            ) = SweepAddressAccountManager.list_sweep_address_accounts(
                org_id=self.org_id,
                sweep_type=SweepType.MERCHANT,
                sweep_items=[SweepItem(wallet_id=self.merchant_1_1.wallet_id)],
            )
            self.assertEqual(len(standard_address_account), 3)
            self.assertEqual(len(collection_address_account), 2)

    def test_check_address_coin_sweep(self):
        with self.subTest("force sweep"):
            address_accounts = [
                self.merchant_address_account_1_1_1,
                self.merchant_address_account_2_1_1,
                self.merchant_address_account_2_1_2,
                self.merchant_address_account_3_3_3,
                self.psp_address_account_1_1_1,
                self.psp_address_account_1_1_2,
                self.psp_address_account_1_3_3,
            ]
            (
                sorted_sweep_keys,
                address_coin_2_amount,
                address_coin_2_address_accounts,
            ) = SweepAddressAccountManager.check_address_coin_sweep(
                address_accounts=address_accounts, force_sweep=True
            )
            self.assertEqual(len(sorted_sweep_keys), 3)
            self.assertEqual(sorted_sweep_keys[0], f"{self.address_1}_{self.token_id}")
            self.assertEqual(
                address_coin_2_amount.get(f"{self.address_1}_{self.token_id}"),
                self.merchant_address_account_1_1_1.amount
                + self.merchant_address_account_2_1_1.amount
                + self.psp_address_account_1_1_1.amount,
            )
            self.assertEqual(
                address_coin_2_amount.get(f"{self.address_2}_{self.token_id}"),
                self.merchant_address_account_2_1_2.amount
                + self.psp_address_account_1_1_2.amount,
            )
            self.assertEqual(
                address_coin_2_amount.get(f"{self.address_3}_{self.token_id}"),
                self.merchant_address_account_3_3_3.amount
                + self.psp_address_account_1_3_3.amount,
            )
            self.assertEqual(
                len(
                    address_coin_2_address_accounts.get(
                        f"{self.address_1}_{self.token_id}"
                    )
                ),
                3,
            )
            self.assertEqual(
                len(
                    address_coin_2_address_accounts.get(
                        f"{self.address_2}_{self.token_id}"
                    )
                ),
                2,
            )
            self.assertEqual(
                len(
                    address_coin_2_address_accounts.get(
                        f"{self.address_3}_{self.token_id}"
                    )
                ),
                2,
            )

        with self.subTest("don't force sweep"):
            address_accounts = [
                self.merchant_address_account_1_1_1,
                self.merchant_address_account_2_1_1,
                self.merchant_address_account_2_1_2,
                self.merchant_address_account_3_3_3,
                self.psp_address_account_1_1_1,
                self.psp_address_account_1_1_2,
                self.psp_address_account_1_3_3,
            ]
            (
                sorted_sweep_keys,
                address_coin_2_amount,
                address_coin_2_address_accounts,
            ) = SweepAddressAccountManager.check_address_coin_sweep(
                address_accounts=address_accounts
            )
            self.assertEqual(len(sorted_sweep_keys), 2)

            self.assertEqual(
                address_coin_2_amount.get(f"{self.address_1}_{self.token_id}"),
                self.merchant_address_account_1_1_1.amount
                + self.merchant_address_account_2_1_1.amount
                + self.psp_address_account_1_1_1.amount,
            )
            self.assertEqual(
                address_coin_2_amount.get(f"{self.address_2}_{self.token_id}"),
                self.merchant_address_account_2_1_2.amount
                + self.psp_address_account_1_1_2.amount,
            )
            self.assertEqual(
                address_coin_2_amount.get(f"{self.address_3}_{self.token_id}"),
                self.merchant_address_account_3_3_3.amount
                + self.psp_address_account_1_3_3.amount,
            )
            self.assertEqual(
                len(
                    address_coin_2_address_accounts.get(
                        f"{self.address_1}_{self.token_id}"
                    )
                ),
                3,
            )
            self.assertEqual(
                len(
                    address_coin_2_address_accounts.get(
                        f"{self.address_2}_{self.token_id}"
                    )
                ),
                2,
            )
            self.assertEqual(
                len(
                    address_coin_2_address_accounts.get(
                        f"{self.address_3}_{self.token_id}"
                    )
                ),
                2,
            )

    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(AutoSweepPolicyProcessor, "handle_batch_manual_sweep")
    @patch.object(CustodyCoinManager, "get_asset_coin")
    def test_sweep_address_account(
        self,
        mock_get_asset_coin,
        mock_handle_batch_manual_sweep,
        mock_get_collection_address,
    ):
        mock_get_asset_coin.return_value = self.coin_info

        with self.subTest(
            "psp sweep address account, force sweep, collection address is collection_address"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.collection_address
            )
            mock_handle_batch_manual_sweep.return_value = [
                time.time_ns(),
                time.time_ns(),
                time.time_ns(),
            ]
            task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=self.org_id,
                sweep_type=SweepType.PSP,
                sweep_items=[],
                force_sweep=True,
            )
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=task_ids
            )
            self.assertEqual(len(sweep_details), 7)
            for sweep_detail in sweep_details:
                SettlementSweepDetailDao.update_by_id_directly(
                    _id=sweep_detail.id, status=SweepDetailStatus.SUCCESS
                )

        with self.subTest(
            "psp sweep address account, force sweep, collection address is address_1"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_1
            )
            mock_handle_batch_manual_sweep.return_value = [
                time.time_ns(),
                time.time_ns(),
            ]
            task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=self.org_id,
                sweep_type=SweepType.PSP,
                sweep_items=[],
                force_sweep=True,
            )
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=task_ids
            )
            self.assertEqual(len(sweep_details), 4)
            for sweep_detail in sweep_details:
                address_account = self.address_account_id_2_address_account.get(
                    sweep_detail.address_account_id
                )
                self.assertEqual(address_account.amount, sweep_detail.sweep_amount)
                SettlementSweepDetailDao.update_by_id_directly(
                    _id=sweep_detail.id, status=SweepDetailStatus.SUCCESS
                )

        with self.subTest(
            "psp sweep address account, don't force sweep, collection address is address_1"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_1
            )
            mock_handle_batch_manual_sweep.return_value = [time.time_ns()]
            task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=self.org_id, sweep_type=SweepType.PSP, sweep_items=[]
            )
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=task_ids
            )
            self.assertEqual(len(sweep_details), 2)
            for sweep_detail in sweep_details:
                address_account = self.address_account_id_2_address_account.get(
                    sweep_detail.address_account_id
                )
                self.assertEqual(address_account.amount, sweep_detail.sweep_amount)
                SettlementSweepDetailDao.update_by_id_directly(
                    _id=sweep_detail.id, status=SweepDetailStatus.SUCCESS
                )

        with self.subTest(
            "address sweep address account, force sweep, collection address is collection_address"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.collection_address
            )
            mock_handle_batch_manual_sweep.return_value = [
                time.time_ns(),
            ]
            task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=self.org_id,
                sweep_type=SweepType.ADDRESS,
                sweep_items=[SweepItem(address=self.address_1)],
                force_sweep=True,
            )
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=task_ids
            )
            self.assertEqual(len(sweep_details), 3)
            for sweep_detail in sweep_details:
                SettlementSweepDetailDao.update_by_id_directly(
                    _id=sweep_detail.id, status=SweepDetailStatus.SUCCESS
                )

        with self.subTest(
            "merchant sweep address account, force sweep, collection address is collection_address"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.collection_address
            )
            mock_handle_batch_manual_sweep.return_value = [
                time.time_ns(),
                time.time_ns(),
            ]
            task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=self.org_id,
                sweep_type=SweepType.MERCHANT,
                sweep_items=[SweepItem(wallet_id=self.wallet_id_1)],
                force_sweep=True,
            )
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=task_ids
            )
            self.assertEqual(len(sweep_details), 5)
            for sweep_detail in sweep_details:
                SettlementSweepDetailDao.update_by_id_directly(
                    _id=sweep_detail.id, status=SweepDetailStatus.SUCCESS
                )

        with self.subTest(
            "merchant sweep address account, force sweep, collection address is address_1"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_1
            )
            mock_handle_batch_manual_sweep.return_value = [time.time_ns()]
            task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=self.org_id,
                sweep_type=SweepType.MERCHANT,
                sweep_items=[SweepItem(wallet_id=self.wallet_id_1)],
                force_sweep=True,
            )
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=task_ids
            )
            self.assertEqual(len(sweep_details), 2)
            for sweep_detail in sweep_details:
                address_account = self.address_account_id_2_address_account.get(
                    sweep_detail.address_account_id
                )
                self.assertEqual(address_account.amount, sweep_detail.sweep_amount)
                SettlementSweepDetailDao.update_by_id_directly(
                    _id=sweep_detail.id, status=SweepDetailStatus.SUCCESS
                )

        with self.subTest(
            "merchant sweep address account, don't force sweep, collection address is address_1"
        ):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_1
            )
            mock_handle_batch_manual_sweep.return_value = [time.time_ns()]
            task_ids = SweepAddressAccountManager.sweep_address_account(
                org_id=self.org_id,
                sweep_type=SweepType.MERCHANT,
                sweep_items=[SweepItem(wallet_id=self.wallet_id_1)],
            )
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=task_ids
            )
            self.assertEqual(len(sweep_details), 2)
            for sweep_detail in sweep_details:
                address_account = self.address_account_id_2_address_account.get(
                    sweep_detail.address_account_id
                )
                self.assertEqual(address_account.amount, sweep_detail.sweep_amount)

        with self.subTest("merchant sweep address account, have processing sweep"):
            mock_get_collection_address.return_value = TokenCollectionAddress(
                address=self.address_1
            )
            mock_handle_batch_manual_sweep.return_value = [time.time_ns()]
            with self.assertRaises(Exception):
                SweepAddressAccountManager.sweep_address_account(
                    org_id=self.org_id,
                    sweep_type=SweepType.MERCHANT,
                    sweep_items=[SweepItem(wallet_id=self.wallet_id_1)],
                )

    @patch.object(MerchantManager, "get_collection_address")
    def test_process_sweep_booking(self, mock_get_collection_address):
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.collection_address
        )

        self.psp = PaymentServiceProviderDao.create(
            org_id=self.org_id,
            otc_addresses=self.otc_address,
            use_dedicated_threshold_usd=Decimal("1.2"),
        )
        self.merchant = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code,
            order_num=0,
            wallet_id=self.wallet_id,
            name=self.merchant_name,
        )
        self.merchant_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
            pending_amount=Decimal("1.23"),
            available_amount=Decimal("0.25"),
        )
        self.psp_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.psp.id,
            account_type=AccountType.PSP,
            token_id=self.token_id,
            chain_id=self.chain_id,
            pending_amount=Decimal("1.24"),
            available_amount=Decimal("0.26"),
        )
        self.merchant_address_account = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.from_address,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        self.psp_address_account = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.from_address,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.5"),
        )
        self.merchant_sweep_amount = Decimal("0.11")
        self.psp_sweep_amount = Decimal("0.33")

        SettlementSweepDetailDao.create(
            sweep_task_id=self.sweep_task_id,
            sweep_address=self.from_address,
            address_account_id=self.merchant_address_account.id,
            sweep_amount=self.merchant_sweep_amount,
        )
        SettlementSweepDetailDao.create(
            sweep_task_id=self.sweep_task_id,
            sweep_address=self.from_address,
            address_account_id=self.psp_address_account.id,
            sweep_amount=self.psp_sweep_amount,
        )
        old_merchant_address_account_amount = self.merchant_address_account.amount
        old_psp_address_account_amount = self.psp_address_account.amount
        old_merchant_account_pending_amount = (
            self.merchant_payment_account.pending_amount
        )
        old_merchant_account_available_amount = (
            self.merchant_payment_account.available_amount
        )
        old_psp_account_pending_amount = self.psp_payment_account.pending_amount
        old_psp_account_available_amount = self.psp_payment_account.available_amount
        SweepAddressAccountManager.process_sweep_booking(
            sweep_task_ids=[self.sweep_task_id]
        )
        new_merchant_address_account = AddressAccountDao.get_by_id_or_raise(
            _id=self.merchant_address_account.id
        )
        self.assertEqual(
            new_merchant_address_account.amount,
            old_merchant_address_account_amount - self.merchant_sweep_amount,
        )
        new_psp_address_account = AddressAccountDao.get_by_id_or_raise(
            _id=self.psp_address_account.id
        )
        self.assertEqual(
            new_psp_address_account.amount,
            old_psp_address_account_amount - self.psp_sweep_amount,
        )
        new_merchant_payment_account = PaymentAccountDao.get_by_id_or_raise(
            _id=self.merchant_payment_account.id
        )
        self.assertEqual(
            new_merchant_payment_account.pending_amount,
            old_merchant_account_pending_amount - self.merchant_sweep_amount,
        )
        self.assertEqual(
            new_merchant_payment_account.available_amount,
            old_merchant_account_available_amount + self.merchant_sweep_amount,
        )
        new_psp_payment_account = PaymentAccountDao.get_by_id_or_raise(
            _id=self.psp_payment_account.id
        )
        self.assertEqual(
            new_psp_payment_account.pending_amount,
            old_psp_account_pending_amount - self.psp_sweep_amount,
        )
        self.assertEqual(
            new_psp_payment_account.available_amount,
            old_psp_account_available_amount + self.psp_sweep_amount,
        )
