from decimal import Decimal
from typing import Dict, List, Optional, Tuple

from cobo_libs.utils.lock import lock_record
from django.db import transaction

from custody.coin.managers import CustodyCoinManager
from custody.custody.dao.organization import OrganizationDao
from waas2.auto_sweep.bo.policy import BatchManualSweepParamBo
from waas2.auto_sweep.enum.task import AutoSweepTaskStatus
from waas2.auto_sweep.processor.auto_sweep_task import AutoSweepTaskProcessor
from waas2.auto_sweep.processor.policy import AutoSweepPolicyProcessor
from waas2.payment.dao.accounting import AddressAccountDao, PaymentAccountDao
from waas2.payment.dao.settlement_order import SettlementSweepDetailDao
from waas2.payment.data.address_account import AddressAccountAmountItem, SweepItem
from waas2.payment.enums.settlement import SweepDetailStatus
from waas2.payment.enums.sweep import SweepType
from waas2.payment.exceptions import PaymentApiException
from waas2.payment.managers.accounting import (
    AddressAccountManager,
    PaymentAccountManager,
)
from waas2.payment.managers.merchant import MerchantManager
from waas2.payment.models.accounting import AddressAccount
from waas2.payment.utils.sweep_config import SweepThresholdManager
from waas2.transactions.dev.enums.transaction import TransactionStatus
from waas2.webhooks.utils.utils import check


class SweepAddressAccountManager:
    @classmethod
    def list_sweep_address_accounts(
        cls, org_id: str, sweep_type: SweepType, sweep_items: List[SweepItem]
    ) -> Tuple[List[AddressAccount], List[AddressAccount]]:
        if sweep_type == SweepType.PSP:
            token_ids = [
                sweep_item.token_id for sweep_item in sweep_items if sweep_item.token_id
            ]
            address_accounts = AddressAccountDao.list_by_token_ids_with_positive_amount(
                org_id=org_id, token_ids=token_ids
            )
        elif sweep_type == SweepType.MERCHANT:
            wallet_id_2_token_ids = {}
            for sweep_item in sweep_items:
                check(sweep_item.wallet_id)

                wallet_id_2_token_ids.setdefault(sweep_item.wallet_id, [])
                if sweep_item.token_id:
                    wallet_id_2_token_ids[sweep_item.wallet_id].append(
                        sweep_item.token_id
                    )

            wallet_ids = list(wallet_id_2_token_ids.keys())
            filter_address_accounts = (
                AddressAccountDao.list_by_wallet_ids_with_positive_amount(
                    org_id=org_id, wallet_ids=wallet_ids
                )
            )
            address_accounts = []
            for address_account in filter_address_accounts:
                token_ids = wallet_id_2_token_ids.get(address_account.wallet_id)
                if not token_ids or address_account.token_id in token_ids:
                    address_accounts.append(address_account)
        elif sweep_type == SweepType.ADDRESS:
            address_2_token_ids = {}
            for sweep_item in sweep_items:
                check(sweep_item.address)

                address_2_token_ids.setdefault(sweep_item.address, [])
                if sweep_item.token_id:
                    address_2_token_ids[sweep_item.address].append(sweep_item.token_id)

            addresses = list(address_2_token_ids.keys())
            filter_address_accounts = (
                AddressAccountDao.list_addresses_with_positive_amount(
                    org_id=org_id, addresses=addresses
                )
            )
            address_accounts = []
            for address_account in filter_address_accounts:
                token_ids = address_2_token_ids.get(address_account.address)
                if not token_ids or address_account.token_id in token_ids:
                    address_accounts.append(address_account)
        else:
            raise

        standard_address_account = []
        collection_address_account = []
        for address_account in address_accounts:
            address = address_account.address
            token_id = address_account.token_id

            collection_address = MerchantManager.get_collection_address(
                wallet_id=address_account.wallet_id, token_id=token_id
            )
            if collection_address and collection_address.address == address:
                collection_address_account.append(address_account)
            else:
                standard_address_account.append(address_account)

        return standard_address_account, collection_address_account

    # 如果不能归集，就不会在sorted_sweep_keys，但会在address_coin_2_amount和address_coin_2_address_accounts中
    @classmethod
    def check_address_coin_sweep(
        cls, address_accounts: List[AddressAccount], force_sweep: bool = False
    ) -> Tuple[List, Dict, Dict]:
        address_coin_2_amount = {}
        address_coin_2_sweep = {}
        address_coin_2_address_accounts = {}

        for address_account in address_accounts:
            address = address_account.address
            token_id = address_account.token_id
            address_coin_key = f"{address}_{token_id}"

            address_coin_2_address_accounts.setdefault(address_coin_key, [])
            address_coin_2_address_accounts[address_coin_key].append(address_account)
            address_coin_2_amount.setdefault(address_coin_key, Decimal("0"))
            address_coin_2_amount[address_coin_key] += address_account.amount

            if force_sweep:
                address_coin_2_sweep[address_coin_key] = True
            else:
                threshold = SweepThresholdManager.get_threshold(
                    chain_id=address_account.chain_id
                )
                check(
                    threshold,
                    PaymentApiException,
                    f"Sweep is not support the chain: {address_account.chain_id}",
                )
                if address_coin_2_amount[address_coin_key] >= threshold:
                    address_coin_2_sweep[address_coin_key] = True

        # 按照地址上可归集金额进行倒序排序，方便指定金额进行归集
        sorted_keys = sorted(
            address_coin_2_amount.keys(),
            key=lambda k: address_coin_2_amount[k],
            reverse=True,
        )
        sorted_sweep_keys = [
            sorted_key
            for sorted_key in sorted_keys
            if address_coin_2_sweep.get(sorted_key)
        ]

        return (
            sorted_sweep_keys,
            address_coin_2_amount,
            address_coin_2_address_accounts,
        )

    # org维度加锁，同一个org同时受理归集。
    # 同一个address+token_id不能同时处理归集中。
    @classmethod
    def sweep_address_account(
        cls,
        org_id: str,
        sweep_type: SweepType,
        sweep_items: List[SweepItem],
        force_sweep: bool = False,
        sweep_amount: Decimal = None,
    ) -> Optional[List[str]]:
        # 归集做org维度加锁
        org = OrganizationDao.get_by_uuid(uuid=org_id)
        lock_record(org)

        address_accounts, _ = cls.list_sweep_address_accounts(
            org_id=org_id,
            sweep_type=sweep_type,
            sweep_items=sweep_items,
        )

        (
            address_coin_keys,
            address_coin_2_amount,
            address_coin_2_address_accounts,
        ) = cls.check_address_coin_sweep(
            address_accounts=address_accounts, force_sweep=force_sweep
        )

        need_sweep_amount = sweep_amount
        sweep_params = []
        sweep_address_accounts = []
        for address_coin_key in address_coin_keys:
            amount = address_coin_2_amount.get(address_coin_key)

            # 如果有归集金额限制，归集金额达到限制后退出。
            if need_sweep_amount is not None:
                if need_sweep_amount <= Decimal("0"):
                    break
                else:
                    amount = min(amount, need_sweep_amount)
                    need_sweep_amount -= amount

            address_accounts = address_coin_2_address_accounts.get(address_coin_key)
            address_account: AddressAccount = address_accounts[0]
            coin_info = CustodyCoinManager.get_asset_coin(
                asset_coin=address_account.token_id
            )

            # 注意：address+token_id 不能处在归集中。
            processing_sweep_details = (
                SettlementSweepDetailDao.list_processing_details_by_address(
                    sweep_address=address_account.address,
                    token_id=address_account.token_id,
                )
            )
            check(not processing_sweep_details)

            sweep_params.append(
                BatchManualSweepParamBo(
                    wallet_id=address_account.wallet_id,
                    address=address_account.address,
                    token_id=address_account.token_id,
                    amount=int(amount * 10**coin_info.decimal),
                )
            )

            sweep_address_accounts.append(address_accounts)

        if not sweep_params:
            return None

        task_ids = AutoSweepPolicyProcessor.handle_batch_manual_sweep(sweep_params)

        sweep_details = []
        for index in range(len(task_ids)):
            task_id = task_ids[index]
            exchange_accounts: List[AddressAccount] = sweep_address_accounts[index]

            if sweep_amount:
                total_sweep_amount = Decimal("0")
                # 当设置sweep_amount时，存在exchange_accounts的amount加和大于实际要归集的金额，在这里过滤。
                for exchange_account in exchange_accounts:
                    if total_sweep_amount >= sweep_amount:
                        break

                    if total_sweep_amount + exchange_account.amount > sweep_amount:
                        address_account_sweep_amount = sweep_amount - total_sweep_amount
                    else:
                        address_account_sweep_amount = exchange_account.amount

                    total_sweep_amount += address_account_sweep_amount
                    sweep_details.append(
                        SettlementSweepDetailDao.new(
                            sweep_task_id=str(task_id),
                            sweep_address=exchange_account.address,
                            token_id=exchange_account.token_id,
                            address_account_id=exchange_account.id,
                            sweep_amount=address_account_sweep_amount,
                            status=SweepDetailStatus.PROCESSING,
                        )
                    )
            else:
                for exchange_account in exchange_accounts:
                    sweep_details.append(
                        SettlementSweepDetailDao.new(
                            sweep_task_id=str(task_id),
                            sweep_address=exchange_account.address,
                            token_id=exchange_account.token_id,
                            address_account_id=exchange_account.id,
                            sweep_amount=exchange_account.amount,
                            status=SweepDetailStatus.PROCESSING,
                        )
                    )

        SettlementSweepDetailDao.bulk_create(sweep_details)

        return task_ids

    @classmethod
    def check_sweep_tasks_status(cls, task_ids: List[str]) -> AutoSweepTaskStatus:
        for task_id in task_ids:
            status, portal_transactions = AutoSweepTaskProcessor.get_task_status(
                task_id
            )
            if status in [AutoSweepTaskStatus.Failed, AutoSweepTaskStatus.Processing]:
                return status

            if not portal_transactions:
                return AutoSweepTaskStatus.Processing
            else:
                for portal_transaction in portal_transactions:
                    if portal_transaction.status not in [
                        TransactionStatus.CONFIRMING,
                        TransactionStatus.COMPLETED,
                    ]:
                        return AutoSweepTaskStatus.Processing
                    if portal_transaction.confirmed_num <= 0:
                        return AutoSweepTaskStatus.Processing

        # 所有任务都已完成
        return AutoSweepTaskStatus.Success

    @classmethod
    @transaction.atomic
    def process_sweep_booking(cls, sweep_task_ids: List[str]):
        sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
            sweep_task_ids=sweep_task_ids
        )
        for sweep_detail in sweep_details:
            # todo 后续加上
            # check(sweep_detail.status == SweepDetailStatus.PROCESSING)
            SettlementSweepDetailDao.update_by_id_directly(
                _id=sweep_detail.id, status=SweepDetailStatus.SUCCESS
            )

        address_accounts = AddressAccountDao.list_by_ids(
            [sweep_detail.address_account_id for sweep_detail in sweep_details]
        )
        address_account_id_2_sweep_detail = {
            sweep_detail.address_account_id: sweep_detail
            for sweep_detail in sweep_details
        }
        for address_account in address_accounts:
            collection_address = MerchantManager.get_collection_address(
                wallet_id=address_account.wallet_id, token_id=address_account.token_id
            )

            address_account = lock_record(address_account)
            payment_account = PaymentAccountDao.get_by_id_or_raise(
                _id=address_account.account_id
            )
            payment_account = lock_record(payment_account)

            collection_account, _ = AddressAccountDao.get_or_create(
                org_id=address_account.org_id,
                wallet_id=address_account.wallet_id,
                owner_type=address_account.owner_type,
                account_id=address_account.account_id,
                address=collection_address.address,
                token_id=address_account.token_id,
                chain_id=address_account.chain_id,
                acquiring_type=address_account.acquiring_type,
            )
            collection_account = lock_record(collection_account)

            sweep_detail = address_account_id_2_sweep_detail.get(address_account.id)
            AddressAccountManager.reduce_amount(
                address_account=address_account, amount=sweep_detail.sweep_amount
            )
            AddressAccountManager.add_amount(
                address_account=collection_account,
                amount=sweep_detail.sweep_amount,
            )

            PaymentAccountManager.reduce_pending_amount(
                payment_account=payment_account, amount=sweep_detail.sweep_amount
            )
            PaymentAccountManager.add_available_amount(
                payment_account=payment_account, amount=sweep_detail.sweep_amount
            )

    @classmethod
    def filter_address_accounts_by_crypto_amount(
        cls, address_accounts: List[AddressAccount], amount: Decimal
    ) -> Optional[List[AddressAccountAmountItem]]:
        address_accounts.sort(
            key=lambda x: x.amount,
            reverse=True,
        )

        need_filter_amount = amount
        address_account_amount_items = []
        for address_account in address_accounts:
            sweep_amount = min(address_account.amount, need_filter_amount)
            if sweep_amount > Decimal("0"):
                address_account_amount_items.append(
                    AddressAccountAmountItem(
                        address_account_id=address_account.id, amount=sweep_amount
                    )
                )
                need_filter_amount -= sweep_amount
            else:
                break

        if need_filter_amount > Decimal("0"):
            return None

        return address_account_amount_items
