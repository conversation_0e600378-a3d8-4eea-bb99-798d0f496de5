from cobo_libs.api.decorator.drf import cobo_extend_schema
from cobo_libs.api.decorator.drf.drf import nowrap_serializer
from cobo_waas2 import (
    ListMerchantBalances200Response,
    ListPaymentWalletBalances200Response,
    PspBalance,
)

from waas2.authentications.universal_access_control.decorators.check_permission_scope import (
    check_permission_scope,
)
from waas2.authentications.universal_access_control.helpers.org_ids import get_org_id
from waas2.authentications.universal_access_control.permissions.operations import (
    PermissionOperation,
)
from waas2.authentications.universal_access_control.permissions.resources import (
    PermissionResource,
)
from waas2.devapi.uac_base_view2 import UacBaseAPIViewV2
from waas2.payment.controllers.balance import BalanceController
from waas2.payment.data.balance import (
    MerchantWalletBalancesParam,
    PaymentWalletBalancesParam,
    PspBalanceParam,
)


class PaymentWalletBalancesApi(UacBaseAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.PAYMENT_BALANCE,
        permission_operation=PermissionOperation.READ,
    )
    @cobo_extend_schema(
        tags=["Payments"],
        parameter=PaymentWalletBalancesParam,
        responses={200: ListPaymentWalletBalances200Response},
        response_schema_wrapper=nowrap_serializer,
    )
    def get(self, request) -> ListPaymentWalletBalances200Response:
        org_id = get_org_id(request)
        data: PaymentWalletBalancesParam = request.validated_data
        wallet_ids = (data.wallet_ids or "").split(",")
        wallet_ids = [item.strip() for item in wallet_ids]

        return BalanceController.list_payment_wallet_balances(
            org_id=org_id, token_id=data.token_id, wallet_ids=wallet_ids
        )


class MerchantBalancesApi(UacBaseAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.PAYMENT_BALANCE,
        permission_operation=PermissionOperation.READ,
    )
    @cobo_extend_schema(
        tags=["Payments"],
        parameter=MerchantWalletBalancesParam,
        responses={200: ListMerchantBalances200Response},
        response_schema_wrapper=nowrap_serializer,
    )
    def get(self, request) -> ListMerchantBalances200Response:
        org_id = get_org_id(request)
        data: MerchantWalletBalancesParam = request.validated_data
        merchant_codes = (data.merchant_ids or "").split(",")
        merchant_codes = [item.strip() for item in merchant_codes]

        return BalanceController.list_merchant_balances(
            org_id=org_id,
            token_id=data.token_id,
            merchant_codes=merchant_codes,
            acquiring_type=data.acquiring_type,
        )


class PspBalanceApi(UacBaseAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.PAYMENT_BALANCE,
        permission_operation=PermissionOperation.READ,
    )
    @cobo_extend_schema(
        tags=["Payments"],
        parameter=PspBalanceParam,
        responses={200: PspBalance},
        response_schema_wrapper=nowrap_serializer,
    )
    def get(self, request) -> PspBalance:
        org_id = get_org_id(request)
        data: PspBalanceParam = request.validated_data

        return BalanceController.get_psp_balance(
            org_id=org_id,
            token_id=data.token_id,
        )
