from decimal import Decimal

from cobo_libs.api.decorator.drf import cobo_extend_schema
from cobo_libs.api.decorator.drf.drf import nowrap_serializer
from cobo_waas2 import (
    ForcedSweep,
    ForcedSweepRequest,
    ListForcedSweepRequests200Response,
)

from waas2.authentications.universal_access_control.decorators.check_permission_scope import (
    check_permission_scope,
)
from waas2.authentications.universal_access_control.helpers.org_ids import get_org_id
from waas2.authentications.universal_access_control.permissions.operations import (
    PermissionOperation,
)
from waas2.authentications.universal_access_control.permissions.resources import (
    PermissionResource,
)
from waas2.devapi.uac_base_view2 import UacBaseAPIViewV2
from waas2.payment.controllers.force_sweep import ForceSweepController
from waas2.payment.data.data import ListForceSweepRequestParam
from waas2.payment.exceptions import PaymentApiException


class ForceSweepApi(UacBaseAPIViewV2):
    @check_permission_scope(
        permission_resource=PermissionResource.PAYMENT_FORCE_SWEEP,
        permission_operation=PermissionOperation.READ,
    )
    @cobo_extend_schema(
        tags=["Payments"],
        parameter=ListForceSweepRequestParam,
        responses={200: ListForcedSweepRequests200Response},
        response_schema_wrapper=nowrap_serializer,
    )
    def get(self, request, *args, **kwargs) -> ListForcedSweepRequests200Response:
        data: ListForceSweepRequestParam = request.validated_data
        org_id = get_org_id(request)
        return ForceSweepController.list_force_sweep_requests(
            org_id=org_id,
            request_id=data.request_id,
            limit=data.limit,
            before=data.before,
            after=data.after,
        )

    @check_permission_scope(
        permission_resource=PermissionResource.PAYMENT_FORCE_SWEEP,
        permission_operation=PermissionOperation.CREATE,
    )
    @cobo_extend_schema(
        tags=["Payments"],
        request=ForcedSweepRequest,
        responses={201: ForcedSweep},
        response_schema_wrapper=nowrap_serializer,
    )
    def post(self, request, *args, **kwargs) -> ForcedSweep:
        data: ForcedSweepRequest = request.validated_data
        org_id = get_org_id(request)

        try:
            amount = Decimal(data.amount)
        except Exception:
            raise PaymentApiException(error_message=f"amount: {data.amount} is invalid")

        return ForceSweepController.create_force_sweep_request(
            org_id=org_id,
            request_id=data.request_id,
            wallet_id=data.wallet_id,
            token_id=data.token_id,
            amount=amount,
        )
