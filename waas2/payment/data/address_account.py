from dataclasses import dataclass
from decimal import Decimal

from cobo_libs.utils.dataclass import DataDictMixin


@dataclass
class SweepItem(DataDictMixin):
    token_id: str = None
    wallet_id: str = None
    address: str = None


@dataclass
class CommissionFeeItem(DataDictMixin):
    amount: Decimal
    token_id: str


@dataclass
class AddressAccountAmountItem(DataDictMixin):
    address_account_id: int
    amount: Decimal
