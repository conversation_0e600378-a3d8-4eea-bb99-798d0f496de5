from enum import unique
from typing import List

from cobo_waas2 import RefundStatus

from waas2.base.enums.utils import StrEnum


@unique
class RefundRequestStatus(StrEnum):
    PENDING_CONFIRMATION = "PendingConfirmation"
    WAITING_SWEEP = "WaitingSweep"
    PENDING_SWEEP = "PendingSweep"
    SWEEP_FAILED = "SweepFailed"
    PENDING_COMMISSION_FEE = "PendingCommissionFee"
    PENDING_TRANSFER_REQUEST = "PendingTransferRequest"
    PENDING_TRANSFER_TRANSACTION = "PendingTransferTransaction"
    PROCESSING = "Processing"
    COMPLETED = "Completed"
    PARTIALLY_COMPLETED = "PartiallyCompleted"
    FAILED = "Failed"
    REFUND_TRANSFER_FAILED = "RefundTransferFailed"

    @classmethod
    def get_pending_status(cls) -> List["RefundRequestStatus"]:
        return [
            cls.WAITING_SWEEP,
            cls.PENDING_SWEEP,
            cls.PENDING_COMMISSION_FEE,
            cls.PENDING_TRANSFER_REQUEST,
            cls.PENDING_TRANSFER_TRANSACTION,
        ]

    @classmethod
    def translate_to_refund_status(cls, status: "RefundRequestStatus") -> RefundStatus:
        if status == cls.PENDING_CONFIRMATION:
            return RefundStatus.PENDINGCONFIRMATION
        elif status in [
            cls.WAITING_SWEEP,
            cls.PENDING_SWEEP,
            cls.SWEEP_FAILED,
            cls.PENDING_COMMISSION_FEE,
            cls.PENDING_TRANSFER_REQUEST,
            cls.PENDING_TRANSFER_TRANSACTION,
            cls.REFUND_TRANSFER_FAILED,
        ]:
            return RefundStatus.PENDING
        elif status == cls.PROCESSING:
            return RefundStatus.PROCESSING
        elif status == cls.COMPLETED:
            return RefundStatus.COMPLETED
        elif status == cls.PARTIALLY_COMPLETED:
            return RefundStatus.PARTIALLYCOMPLETED
        elif status == cls.FAILED:
            return RefundStatus.FAILED
        else:
            raise
