from enum import IntEnum, unique
from typing import List

from cobo_waas2 import ForcedSweepStatus

from waas2.base.enums.utils import StrEnum


class ForceSweepRequestStatus(StrEnum):
    WAITING_SWEEP = "WaitingSweep"
    PENDING_SWEEP = "PendingSweep"
    SWEEP_FAILED = "SweepFailed"
    COMPLETED = "Completed"

    @classmethod
    def list_pending_statuses(cls) -> List["ForceSweepRequestStatus"]:
        return [cls.WAITING_SWEEP, cls.PENDING_SWEEP]

    @classmethod
    def translate_to_force_sweep_status(
        cls, status: "ForceSweepRequestStatus"
    ) -> ForcedSweepStatus:
        if status in [cls.WAITING_SWEEP]:
            return ForcedSweepStatus.PENDING
        elif status in [cls.PENDING_SWEEP, cls.SWEEP_FAILED]:
            return ForcedSweepStatus.PROCESSING
        else:
            return ForcedSweepStatus.COMPLETED


class ForceSweepSource(IntEnum):
    USER = 1
    COBO = 2


@unique
class SweepType(StrEnum):
    MERCHANT = "Merchant"
    PSP = "Psp"
    ADDRESS = "Address"
