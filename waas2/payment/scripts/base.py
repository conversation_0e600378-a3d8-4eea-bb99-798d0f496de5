from decimal import Decimal
from typing import List

from cobo_libs.utils.exceptions.tools import check
from cobo_waas2 import TransactionStatus
from django.db import transaction

from custody.coin.managers import CustodyCoinManager
from waas2.auto_sweep.bo.policy import BatchManualSweepParamBo
from waas2.auto_sweep.enum.task import AutoSweepTaskStatus
from waas2.auto_sweep.processor.auto_sweep_task import AutoSweepTaskProcessor
from waas2.auto_sweep.processor.policy import AutoSweepPolicyProcessor
from waas2.payment.dao.accounting import AddressAccountDao
from waas2.payment.dao.settlement_order import SettlementSweepDetailDao
from waas2.payment.models.accounting import AddressAccount


@transaction.atomic
def handle_retry_sweep(sweep_task_ids: List[str]) -> List[str]:
    new_sweep_task_ids = []
    for sweep_task_id in sweep_task_ids:
        status, portal_transactions = AutoSweepTaskProcessor.get_task_status(
            task_id=sweep_task_id
        )
        if status == AutoSweepTaskStatus.Failed:
            sweep_details = SettlementSweepDetailDao.list_by_sweep_task_ids(
                sweep_task_ids=[sweep_task_id]
            )
            address_account: AddressAccount = AddressAccountDao.get_by_id_or_raise(
                _id=sweep_details[0].address_account_id
            )
            coin_info = CustodyCoinManager.get_asset_coin(
                asset_coin=address_account.token_id
            )
            amount = Decimal("0")
            for sweep_detail in sweep_details:
                amount += sweep_detail.sweep_amount
            sweep_params = [
                BatchManualSweepParamBo(
                    wallet_id=address_account.wallet_id,
                    address=address_account.address,
                    token_id=address_account.token_id,
                    amount=int(amount * 10**coin_info.decimal),
                )
            ]
            task_ids = AutoSweepPolicyProcessor.handle_batch_manual_sweep(sweep_params)
            sweep_task_id = task_ids[0]
            new_sweep_task_ids.append(task_ids[0])

            for sweep_detail in sweep_details:
                SettlementSweepDetailDao.update_by_id_directly(
                    _id=sweep_detail.id, sweep_task_id=sweep_task_id
                )
        else:
            check(status == AutoSweepTaskStatus.Success)
            new_sweep_task_ids.append(sweep_task_id)
            portal_transaction = portal_transactions[0]
            check(
                portal_transaction.status
                in [
                    TransactionStatus.CONFIRMING,
                    TransactionStatus.COMPLETED,
                ]
            )
            check(portal_transaction.confirmed_num > 0)

    return new_sweep_task_ids
