from cobo_libs.utils.exceptions.tools import check
from django.db import transaction

from waas2.payment.dao.refund import RefundDao
from waas2.payment.enums.refund import RefundRequestStatus
from waas2.payment.models.refund import Refund
from waas2.payment.scripts.base import handle_retry_sweep


@transaction.atomic
def retry_refund_sweep(refund_id: int):
    refund: Refund = RefundDao.get_by_id_or_raise(_id=refund_id)
    check(refund.status == RefundRequestStatus.SWEEP_FAILED)

    api_request_info = refund.api_request_info
    new_sweep_task_ids = handle_retry_sweep(
        sweep_task_ids=api_request_info.get("sweep_task_ids", [])
    )

    api_request_info["sweep_task_ids"] = new_sweep_task_ids
    RefundDao.update_by_id_directly(
        _id=refund.id,
        api_request_info=api_request_info,
        status=RefundRequestStatus.PENDING_SWEEP,
    )
