from cobo_libs.utils.exceptions.tools import check
from django.db import transaction

from waas2.payment.dao.sweep import ForceSweepRequestDao
from waas2.payment.data.sweep import ForceSweepRequestExtra
from waas2.payment.enums.sweep import ForceSweepRequestStatus
from waas2.payment.models.sweep import ForceSweepRequest
from waas2.payment.scripts.base import handle_retry_sweep


@transaction.atomic
def retry_force_sweep(force_sweep_request_id: int):
    sweep_request: ForceSweepRequest = ForceSweepRequestDao.get_by_id_or_raise(
        _id=force_sweep_request_id
    )
    check(sweep_request.status == ForceSweepRequestStatus.SWEEP_FAILED)

    extra = ForceSweepRequestExtra.from_json(sweep_request.extra)
    new_sweep_task_ids = handle_retry_sweep(sweep_task_ids=extra.sweep_task_ids)

    extra.sweep_task_ids = new_sweep_task_ids
    ForceSweepRequestDao.update_by_id_directly(
        _id=sweep_request.id,
        extra=extra.to_json(),
        status=ForceSweepRequestStatus.PENDING_SWEEP,
    )
