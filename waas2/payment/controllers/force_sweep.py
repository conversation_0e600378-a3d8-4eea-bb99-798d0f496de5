from decimal import Decimal
from typing import List, Optional

from cobo_libs.utils.exceptions.tools import check
from cobo_libs.utils.time import dt_to_ts
from cobo_waas2 import ForcedSweep, ListForcedSweepRequests200Response
from django.conf import settings
from django.db import transaction

from custody.coin.managers import CustodyCoinManager
from custody.custody.dao.custody_wallet import CustodyWalletDao
from waas2.fee_engine.enums import BusinessType as FeeEngineBusinessType
from waas2.payment.dao.merchant import MerchantDao
from waas2.payment.dao.sweep import ForceSweepRequestDao
from waas2.payment.data.address_account import CommissionFeeItem, SweepItem
from waas2.payment.enums.sweep import (
    ForceSweepRequestStatus,
    ForceSweepSource,
    SweepType,
)
from waas2.payment.exceptions import PaymentApiException
from waas2.payment.managers.address_account import SweepAddressAccountManager
from waas2.payment.managers.commission_fee import CommissionFeeManager
from waas2.payment.models.sweep import ForceSweepRequest


class ForceSweepController:
    @classmethod
    @transaction.atomic
    def create_force_sweep_request(
        cls,
        org_id: str,
        request_id: str,
        wallet_id: str,
        token_id: str,
        amount: Decimal,
    ) -> ForcedSweep:
        merchant = MerchantDao.get_merchant_by_wallet_id(wallet_id=wallet_id)
        check(merchant, PaymentApiException, f"wallet_id: {wallet_id} is illegal")
        custody_wallet = CustodyWalletDao.get_by_uuid(wallet_uuid=wallet_id)
        check(custody_wallet, PaymentApiException, f"wallet_id: {wallet_id} is illegal")
        check(
            custody_wallet.org.uuid == org_id,
            PaymentApiException,
            f"wallet_id: {wallet_id} is illegal",
        )
        coin_info = CustodyCoinManager.get_asset_coin(asset_coin=token_id)
        if not coin_info:
            raise PaymentApiException(f"Invalid token_id: {token_id}")
        check(
            coin_info.asset_coin in settings.PAYMENT_SUPPORTED_TOKENS,
            PaymentApiException,
            f"Invalid token_id: {token_id}",
        )
        request = ForceSweepRequestDao.get_by_org_id_and_request_id(
            org_id=org_id, request_id=request_id
        )
        if request:
            check(not request, PaymentApiException, "Exist request id")

        address_accounts, _ = SweepAddressAccountManager.list_sweep_address_accounts(
            org_id=org_id,
            sweep_type=SweepType.MERCHANT,
            sweep_items=[SweepItem(token_id=token_id, wallet_id=wallet_id)],
        )
        (
            sorted_sweep_keys,
            address_coin_2_amount,
            address_coin_2_address_accounts,
        ) = SweepAddressAccountManager.check_address_coin_sweep(
            address_accounts=address_accounts, force_sweep=True
        )
        sum_sweep_amount = Decimal("0")
        for sorted_sweep_key in sorted_sweep_keys:
            sum_sweep_amount += address_coin_2_amount.get(sorted_sweep_key)

        check(
            sum_sweep_amount >= amount,
            PaymentApiException,
            "Insufficient funds to sweep",
        )

        request = ForceSweepRequestDao.create(
            org_id=org_id,
            request_id=request_id,
            wallet_id=wallet_id,
            token_id=token_id,
            amount=amount,
            source=ForceSweepSource.USER,
        )

        # 归集计费，如果计费失败，事务回滚
        try:
            commission_fee_items = [
                CommissionFeeItem(amount=request.amount, token_id=request.token_id)
            ]
            CommissionFeeManager.charge_commission_fee(
                org_uuid=request.org_id,
                request_id=request.uuid,
                commission_fee_items=commission_fee_items,
                business_type=FeeEngineBusinessType.BUSINESS_TYPE_PAYMENT_FORCE_SWEEP,
            )
        except Exception:
            raise PaymentApiException(error_message="Insufficient fee to sweep")

        return ForcedSweep(
            forced_sweep_id=request.uuid,
            request_id=request.request_id,
            wallet_id=request.wallet_id,
            token_id=request.token_id,
            amount=str(request.amount),
            status=ForceSweepRequestStatus.translate_to_force_sweep_status(
                request.status
            ),
            created_timestamp=int(dt_to_ts(request.created_time)),
            updated_timestamp=int(dt_to_ts(request.modified_time)),
        )

    @classmethod
    def list_force_sweep_requests(
        cls,
        org_id: str,
        request_id: Optional[str] = None,
        limit: Optional[int] = None,
        before: Optional[str] = None,
        after: Optional[str] = None,
    ) -> ListForcedSweepRequests200Response:
        requests, pagination = ForceSweepRequestDao.search_user_force_sweep_request(
            org_id=org_id,
            request_id=request_id,
            limit=limit,
            before=before,
            after=after,
        )
        requests: List[ForceSweepRequest] = requests

        return ListForcedSweepRequests200Response(
            data=[
                ForcedSweep(
                    forced_sweep_id=request.uuid,
                    request_id=request.request_id,
                    wallet_id=request.wallet_id,
                    token_id=request.token_id,
                    amount=str(request.amount),
                    status=ForceSweepRequestStatus.translate_to_force_sweep_status(
                        request.status
                    ),
                    created_timestamp=int(dt_to_ts(request.created_time)),
                    updated_timestamp=int(dt_to_ts(request.modified_time)),
                )
                for request in requests
            ],
            pagination=pagination.to_dict() if pagination else None,
        )
