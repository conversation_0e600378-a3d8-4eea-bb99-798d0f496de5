import time
import uuid
from decimal import Decimal
from unittest.mock import patch

from cobo_waas2 import ForcedSweepStatus
from django.test import TestCase

from custody.coin.data.objects import AssetCoinInfo
from custody.coin.managers import CustodyCoinManager
from custody.custody.dao.custody_wallet import CustodyWalletDao
from custody.custody.dao.organization import OrganizationDao
from custody.custody.models.custody_wallet import CustodyWallet
from waas2.payment.controllers.force_sweep import ForceSweepController
from waas2.payment.dao.accounting import AddressAccountDao, PaymentAccountDao
from waas2.payment.dao.chain_config import ChainSweepThresholdDao
from waas2.payment.dao.merchant import MerchantDao
from waas2.payment.dao.psp import PaymentServiceProviderDao
from waas2.payment.dao.sweep import ForceSweepRequestDao
from waas2.payment.enums.order_enum import AccountType
from waas2.payment.exceptions import PaymentApiException
from waas2.payment.managers.commission_fee import CommissionFeeManager
from waas2.payment.models.sweep import ForceSweepRequest


class TestSettlementRequestController(TestCase):
    def setUp(self):
        self.org = OrganizationDao.create_org(
            default_remote_wallet_id=time.time_ns(), name="test"
        )
        self.org_id = self.org.uuid
        self.address = str(uuid.uuid4())
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code = str(time.time_ns())
        self.wallet_id = str(uuid.uuid4())
        self.wallet_name = str(uuid.uuid4())
        self.merchant_name = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"
        self.coin_info = AssetCoinInfo(
            chain_coin=self.chain_id,
            fee_coin=self.chain_id,
            asset_coin=self.token_id,
            decimal=8,
            decimal_multi=Decimal("12"),
            confirming_threshold=1,
            token_id=self.token_id,
        )
        self.from_address = str(uuid.uuid4())
        self.to_address = str(uuid.uuid4())
        self.amount = Decimal("1.2")
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code = str(time.time_ns())
        self.wallet_id = str(uuid.uuid4())
        self.merchant_name = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"

    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(CommissionFeeManager, "charge_commission_fee")
    def test_create_force_sweep_request(
        self, mock_charge_commission_fee, mock_get_asset_coin
    ):
        self.custody_wallet = CustodyWalletDao.create(
            uuid=self.wallet_id,
            org=self.org,
            name=self.wallet_name,
            config_type=CustodyWallet.WT_MPC,
            config_json="",
        )
        self.psp = PaymentServiceProviderDao.create(
            org_id=self.org_id,
            otc_addresses=self.otc_address,
            use_dedicated_threshold_usd=Decimal("1.2"),
        )
        self.merchant = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code,
            order_num=0,
            wallet_id=self.wallet_id,
            name=self.merchant_name,
        )
        self.merchant_2 = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code + "_2",
            order_num=0,
            wallet_id=self.wallet_id,
            name=self.merchant_name + "_2",
        )
        self.merchant_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.merchant_payment_account_2 = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant_2.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.psp_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.psp.id,
            account_type=AccountType.PSP,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.address,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account_2.id,
            address=self.address,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.94"),
        )
        AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.address,
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.2"),
        )
        AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.psp_payment_account.id,
            address=self.address + "_2",
            token_id=self.token_id,
            chain_id=self.chain_id,
            amount=Decimal("0.2"),
        )
        ChainSweepThresholdDao.create(
            chain_id=self.chain_id, chain_identifier=self.chain_id, min_sweep_amount=1
        )

        mock_get_asset_coin.return_value = self.coin_info
        with self.subTest("merchant is not exist"):
            mock_get_merchant_by_wallet_id = patch.object(
                MerchantDao, "get_merchant_by_wallet_id", return_value=None
            )
            mock_get_merchant_by_wallet_id.start()
            with self.assertRaises(PaymentApiException):
                ForceSweepController.create_force_sweep_request(
                    org_id=self.org_id,
                    request_id=str(uuid.uuid4()),
                    wallet_id=self.wallet_id,
                    token_id=self.token_id,
                    amount=self.amount,
                )
            mock_get_merchant_by_wallet_id.stop()

        with self.subTest("custody wallet is not exist"):
            mock_get_wallet_by_uuid = patch.object(
                CustodyWalletDao, "get_by_uuid", return_value=None
            )
            mock_get_wallet_by_uuid.start()
            with self.assertRaises(PaymentApiException):
                ForceSweepController.create_force_sweep_request(
                    org_id=self.org_id,
                    request_id=str(uuid.uuid4()),
                    wallet_id=self.wallet_id,
                    token_id=self.token_id,
                    amount=self.amount,
                )
            mock_get_wallet_by_uuid.stop()

        with self.subTest("exist request id"):
            mock_get_request_by_org_id_and_request_id = patch.object(
                ForceSweepRequestDao,
                "get_by_org_id_and_request_id",
                return_value=ForceSweepRequest(
                    uuid=str(uuid.uuid4()),
                    org_id=self.org_id,
                    request_id=str(uuid.uuid4()),
                    wallet_id=self.wallet_id,
                    token_id=self.token_id,
                    amount=self.amount,
                ),
            )
            mock_get_request_by_org_id_and_request_id.start()
            with self.assertRaises(PaymentApiException):
                ForceSweepController.create_force_sweep_request(
                    org_id=self.org_id,
                    request_id=str(uuid.uuid4()),
                    wallet_id=self.wallet_id,
                    token_id=self.token_id,
                    amount=self.amount,
                )

            mock_get_request_by_org_id_and_request_id.stop()

        with self.subTest("charge fee exception"):
            mock_charge_commission_fee.side_effect = PaymentApiException(
                "gas station wallet not found for org_id"
            )
            with self.assertRaises(PaymentApiException):
                ForceSweepController.create_force_sweep_request(
                    org_id=self.org_id,
                    request_id=str(uuid.uuid4()),
                    wallet_id=self.wallet_id,
                    token_id=self.token_id,
                    amount=self.amount,
                )

        with self.subTest("create force sweep success"):
            mock_charge_commission_fee.side_effect = None
            force_sweep = ForceSweepController.create_force_sweep_request(
                org_id=self.org_id,
                request_id=str(uuid.uuid4()),
                wallet_id=self.wallet_id,
                token_id=self.token_id,
                amount=self.amount,
            )
            self.assertEqual(force_sweep.status, ForcedSweepStatus.PENDING)
