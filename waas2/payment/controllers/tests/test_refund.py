import time
import uuid
from decimal import Decimal
from unittest.mock import patch

from cobo_waas2 import RefundStatus
from django.test import TestCase

from custody.coin.data.objects import AssetCoinInfo
from custody.coin.managers.coin import CustodyCoinManager
from custody.custody.dao.organization import OrganizationDao
from custody.wallet.managers.address import WalletAddressManager
from waas2.auto_sweep.bo.address import TokenCollectionAddress
from waas2.payment.controllers.refund import RefundController
from waas2.payment.dao.accounting import AddressAccountDao, PaymentAccountDao
from waas2.payment.dao.merchant import MerchantDao
from waas2.payment.dao.psp import PaymentServiceProviderDao
from waas2.payment.dao.refund import RefundDao
from waas2.payment.enums.order_enum import AccountType, AcquiringType, RefundType
from waas2.payment.enums.refund import RefundRequestStatus
from waas2.payment.exceptions import PaymentApiException
from waas2.payment.managers.accounting import (
    AddressAccountManager,
    PaymentAccountManager,
)
from waas2.payment.managers.merchant import MerchantManager
from waas2.payment.utils.sweep_config import SweepThresholdManager


class TestRefundController(TestCase):
    def setUp(self):
        self.org = OrganizationDao.create_org(
            default_remote_wallet_id=time.time_ns(), name="test"
        )
        self.org_id = self.org.uuid
        self.address = str(uuid.uuid4())
        self.address_1 = str(uuid.uuid4())
        self.address_2 = str(uuid.uuid4())
        self.address_3 = str(uuid.uuid4())
        self.address_5 = str(uuid.uuid4())
        self.address_6 = str(uuid.uuid4())
        self.address_7 = str(uuid.uuid4())
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code = str(time.time_ns())
        self.wallet_id = str(uuid.uuid4())
        self.wallet_name = str(uuid.uuid4())
        self.merchant_name = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"
        self.coin_info = AssetCoinInfo(
            chain_coin=self.chain_id,
            fee_coin=self.chain_id,
            asset_coin=self.token_id,
            decimal=8,
            decimal_multi=Decimal("12"),
            confirming_threshold=1,
            token_id=self.token_id,
        )
        self.from_address = str(uuid.uuid4())
        self.to_address = str(uuid.uuid4())
        self.amount = Decimal("1.2")
        self.collection_address = str(uuid.uuid4())
        self.otc_address = str(uuid.uuid4())
        self.crypto_address_id = str(uuid.uuid4())
        self.merchant_code = str(time.time_ns())
        self.wallet_id = str(uuid.uuid4())
        self.merchant_name = str(uuid.uuid4())
        self.token_id = "TRON_USDT"
        self.chain_id = "TRON"
        self.currency = "USD"

    def _reset_balances(self):
        """重置所有账户余额到初始状态"""
        # 重置 PaymentAccount 余额
        PaymentAccountDao.update_account(
            account_id=self.merchant_payment_account.id,
            available_amount=Decimal("0"),
            pending_amount=Decimal("0"),
            locked_amount=Decimal("0"),
            settled_amount=Decimal("0"),
        )

        PaymentAccountDao.update_account(
            account_id=self.psp_payment_account.id,
            available_amount=Decimal("0"),
            pending_amount=Decimal("0"),
            locked_amount=Decimal("0"),
            settled_amount=Decimal("0"),
        )

        # 重置 AddressAccount 余额
        AddressAccountDao.update_address_account(
            address_account_id=self.address_account_1.id,
            amount=Decimal("0"),
            locked_amount=Decimal("0"),
        )
        AddressAccountDao.update_address_account(
            address_account_id=self.address_account_2.id,
            amount=Decimal("0"),
            locked_amount=Decimal("0"),
        )
        AddressAccountDao.update_address_account(
            address_account_id=self.address_account_3.id,
            amount=Decimal("0"),
            locked_amount=Decimal("0"),
        )

        # 刷新对象
        self.merchant_payment_account.refresh_from_db()
        self.psp_payment_account.refresh_from_db()
        self.address_account_1.refresh_from_db()
        self.address_account_2.refresh_from_db()
        self.address_account_3.refresh_from_db()

    @patch.object(CustodyCoinManager, "get_asset_coin")
    @patch.object(WalletAddressManager, "is_valid")
    @patch.object(MerchantManager, "get_collection_address")
    @patch.object(SweepThresholdManager, "get_threshold")
    def test_create_refund(
        self,
        mock_get_threshold,
        mock_get_collection_address,
        mock_is_valid,
        mock_get_asset_coin,
    ):
        # Mock coin info
        mock_get_asset_coin.return_value = self.coin_info
        mock_get_threshold.return_value = Decimal("0.5")
        mock_get_collection_address.return_value = TokenCollectionAddress(
            address=self.address_1,
        )

        # Create test data
        self.merchant = MerchantDao.create(
            org_id=self.org_id,
            merchant_code=self.merchant_code,
            order_num=0,
            wallet_id=self.wallet_id,
            name=self.merchant_name,
        )

        self.psp = PaymentServiceProviderDao.create(
            org_id=self.org_id,
            otc_addresses=self.otc_address,
            use_dedicated_threshold_usd=Decimal("1.2"),
        )

        self.merchant_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.merchant.id,
            account_type=AccountType.MERCHANT,
            token_id=self.token_id,
            chain_id=self.chain_id,
            acquiring_type=AcquiringType.ORDER,
        )
        self.address_account_1 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.address_1,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.address_account_2 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.address_2,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.address_account_3 = AddressAccountDao.create(
            org_id=self.org_id,
            wallet_id=self.wallet_id,
            owner_type=AccountType.MERCHANT,
            account_id=self.merchant_payment_account.id,
            address=self.address_3,
            token_id=self.token_id,
            chain_id=self.chain_id,
        )
        self.psp_payment_account = PaymentAccountDao.create(
            org_id=self.org_id,
            account_owner_id=self.psp.id,
            account_type=AccountType.PSP,
            token_id=self.token_id,
            chain_id=self.chain_id,
            acquiring_type=AcquiringType.ORDER,
        )

        with self.subTest("Invalid to_address"):
            mock_is_valid.return_value = False
            request_id = str(uuid.uuid4())
            with self.assertRaisesRegex(PaymentApiException, "Invalid to_address"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address="invalid_address",
                    merchant_id=self.merchant_code,
                )

        with self.subTest("Invalid token_id"):
            mock_is_valid.return_value = True
            request_id = str(uuid.uuid4())
            with self.assertRaisesRegex(PaymentApiException, "Invalid token_id"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id="INVALID_TOKEN",
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=self.merchant_code,
                )

        with self.subTest("Invalid refund amount"):
            request_id = str(uuid.uuid4())
            with self.assertRaisesRegex(PaymentApiException, "Invalid refund amount"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount="0",
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=self.merchant_code,
                )

        with self.subTest("Invalid refund type"):
            request_id = str(uuid.uuid4())
            with self.assertRaisesRegex(PaymentApiException, "Invalid refund type"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type="PSP",  # mock typo
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=self.merchant_code,
                )

        with self.subTest("Missing merchant code for merchant refund"):
            request_id = str(uuid.uuid4())
            with self.assertRaisesRegex(PaymentApiException, "Missing merchant code"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=None,
                )

        with self.subTest("Invalid merchant fee amount"):
            request_id = str(uuid.uuid4())
            merchant_fee_amount = Decimal("12")
            with self.assertRaisesRegex(
                PaymentApiException,
                f"Invalid merchant fee amount: {merchant_fee_amount}",
            ):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=self.merchant_code,
                    charge_merchant_fee=True,
                    merchant_fee_amount=merchant_fee_amount,
                    merchant_fee_token_id=self.token_id,
                )

        with self.subTest("Merchant fee token mismatch"):
            request_id = str(uuid.uuid4())
            token_id = "ETH_USDT"
            with self.assertRaisesRegex(
                PaymentApiException,
                f"Both merchant_fee_token_id: {token_id} and token_id: {self.token_id} are same",
            ):
                merchant_fee_amount = Decimal("0.12")
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=self.merchant_code,
                    charge_merchant_fee=True,
                    merchant_fee_amount=merchant_fee_amount,
                    merchant_fee_token_id=token_id,
                )

        with self.subTest("Existing refund request_id"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            PaymentAccountManager.add_available_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("1.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("1.2")
            )
            RefundController.create_refund(
                org_id=self.org_id,
                request_id=request_id,
                amount=str(self.amount),
                refund_type=RefundType.MERCHANT,
                token_id=self.token_id,
                initiator="test_initiator",
                to_address=self.to_address,
                merchant_id=self.merchant_code,
            )
            with self.assertRaisesRegex(PaymentApiException, "Invalid request_id"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=self.merchant_code,
                )

        """Test for merchant refund"""
        with self.subTest("Merchant not found"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            with self.assertRaisesRegex(PaymentApiException, "Merchant not found"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id="non_existent_merchant_code",
                )

        with self.subTest("Insufficient balance"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 merchant 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("0.2")
            )
            # 这里 merchant 的 address account 可用资金为 1，因为 address_2 和 address_3 未达到可归集的阈值，所以 address_account 的总可用资金就是 1
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("1")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_2, amount=Decimal("0.1")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_3, amount=Decimal("0.1")
            )
            with self.assertRaisesRegex(PaymentApiException, "Insufficient balance"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.MERCHANT,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                    merchant_id=self.merchant_code,
                )

        with self.subTest("Create merchant refund with status PENDING_CONFIRMATION"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 merchant 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("0.2")
            )
            # 这里 merchant 的 address account 可用资金为 1，因为 address_2 和 address_3 未达到可归集的阈值，所以 address_account 的总可用资金就是 1
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_2, amount=Decimal("0.5")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_3, amount=Decimal("0.5")
            )
            refund = RefundController.create_refund(
                org_id=self.org_id,
                request_id=request_id,
                amount=str(self.amount),
                refund_type=RefundType.MERCHANT,
                token_id=self.token_id,
                initiator="test_initiator",
                merchant_id=self.merchant_code,
            )
            self.assertEqual(refund.status, RefundStatus.PENDINGCONFIRMATION)

        with self.subTest("Create merchant refund with status PENDING"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 merchant 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("0.2")
            )
            # 这里 merchant 的 address account 可用资金为 1，因为 address_2 和 address_3 未达到可归集的阈值，所以 address_account 的总可用资金就是 1
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_2, amount=Decimal("0.5")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_3, amount=Decimal("0.5")
            )
            refund = RefundController.create_refund(
                org_id=self.org_id,
                request_id=request_id,
                amount=str(self.amount),
                refund_type=RefundType.MERCHANT,
                to_address=self.to_address,
                token_id=self.token_id,
                initiator="test_initiator",
                merchant_id=self.merchant_code,
            )
            self.assertEqual(refund.status, RefundStatus.PENDING)

            refund_db = RefundDao.get_by_request_id(
                org_id=self.org_id, request_id=request_id
            )
            self.assertEqual(refund_db.status, RefundRequestStatus.WAITING_SWEEP)

        with self.subTest("Create merchant refund with status PENDING_COMMISSION_FEE"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 merchant 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.merchant_payment_account, amount=Decimal("0.2")
            )
            # 这里 merchant 的 address account 可用资金为 1，因为 address_2 和 address_3 未达到可归集的阈值，所以 address_account 的总可用资金就是 1
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("1.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_2, amount=Decimal("0.5")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_3, amount=Decimal("0.5")
            )
            refund = RefundController.create_refund(
                org_id=self.org_id,
                request_id=request_id,
                amount=str(self.amount),
                refund_type=RefundType.MERCHANT,
                to_address=self.to_address,
                token_id=self.token_id,
                initiator="test_initiator",
                merchant_id=self.merchant_code,
            )
            refund_db = RefundDao.get_by_request_id(
                org_id=self.org_id, request_id=request_id
            )
            self.assertEqual(
                refund_db.status, RefundRequestStatus.PENDING_COMMISSION_FEE
            )

        """Test for psp refund"""
        with self.subTest("Create psp refund with insufficient balance"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 psp 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.psp_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.psp_payment_account, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("0.1")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_2, amount=Decimal("0.5")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_3, amount=Decimal("0.5")
            )
            with self.assertRaisesRegex(PaymentApiException, "Insufficient balance"):
                RefundController.create_refund(
                    org_id=self.org_id,
                    request_id=request_id,
                    amount=str(self.amount),
                    refund_type=RefundType.PSP,
                    token_id=self.token_id,
                    initiator="test_initiator",
                    to_address=self.to_address,
                )

        with self.subTest("Create psp refund with Pending Confirmation"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 psp 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.psp_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.psp_payment_account, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_2, amount=Decimal("0.5")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_3, amount=Decimal("0.5")
            )
            refund = RefundController.create_refund(
                org_id=self.org_id,
                request_id=request_id,
                amount=str(self.amount),
                refund_type=RefundType.PSP,
                token_id=self.token_id,
                initiator="test_initiator",
            )
            self.assertEqual(refund.status, RefundStatus.PENDINGCONFIRMATION)

            refund_db = RefundDao.get_by_request_id(
                org_id=self.org_id, request_id=request_id
            )
            self.assertEqual(refund_db.status, RefundRequestStatus.PENDING_CONFIRMATION)

        with self.subTest("Create psp refund with Waiting Sweep"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 psp 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.psp_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.psp_payment_account, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_2, amount=Decimal("0.5")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_3, amount=Decimal("0.5")
            )
            refund = RefundController.create_refund(
                org_id=self.org_id,
                request_id=request_id,
                amount=str(self.amount),
                refund_type=RefundType.PSP,
                token_id=self.token_id,
                initiator="test_initiator",
                to_address=self.to_address,
            )
            self.assertEqual(refund.status, RefundStatus.PENDING)

            refund_db = RefundDao.get_by_request_id(
                org_id=self.org_id, request_id=request_id
            )
            self.assertEqual(refund_db.status, RefundRequestStatus.WAITING_SWEEP)

        with self.subTest("Create psp refund with Pending Commission Fee"):
            # 重置余额到初始状态
            self._reset_balances()
            request_id = str(uuid.uuid4())
            # 这里 psp 的 payment account 可用资金为 1，pending 为 0.2
            PaymentAccountManager.add_available_amount(
                payment_account=self.psp_payment_account, amount=Decimal("1")
            )
            PaymentAccountManager.add_pending_amount(
                payment_account=self.psp_payment_account, amount=Decimal("0.2")
            )
            AddressAccountManager.add_amount(
                address_account=self.address_account_1, amount=Decimal("1.2")
            )
            refund = RefundController.create_refund(
                org_id=self.org_id,
                request_id=request_id,
                amount=str(self.amount),
                refund_type=RefundType.PSP,
                token_id=self.token_id,
                initiator="test_initiator",
                to_address=self.to_address,
            )
            self.assertEqual(refund.status, RefundStatus.PENDING)

            refund_db = RefundDao.get_by_request_id(
                org_id=self.org_id, request_id=request_id
            )
            self.assertEqual(
                refund_db.status, RefundRequestStatus.PENDING_COMMISSION_FEE
            )
