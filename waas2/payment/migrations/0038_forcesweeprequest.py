# Generated by Django 4.2.16 on 2025-07-02 07:08

import custody.cobo.utils.models
from django.db import migrations, models
import waas2.payment.enums.sweep


class Migration(migrations.Migration):

    dependencies = [
        ("waas2_payment", "0037_alter_refund_status_alter_refund_to_address"),
    ]

    operations = [
        migrations.CreateModel(
            name="ForceSweepRequest",
            fields=[
                (
                    "id",
                    custody.cobo.utils.models.UnsignedBigIntegerField(
                        primary_key=True, serialize=False
                    ),
                ),
                ("uuid", models.Char<PERSON>ield(max_length=64, unique=True)),
                ("org_id", models.<PERSON>r<PERSON><PERSON>(max_length=64)),
                ("request_id", models.Char<PERSON>ield(db_index=True, max_length=128)),
                ("wallet_id", models.CharField(max_length=64)),
                ("token_id", models.CharField(max_length=24)),
                ("amount", models.DecimalField(decimal_places=18, max_digits=65)),
                (
                    "status",
                    models.Char<PERSON><PERSON>(
                        db_index=True,
                        default=waas2.payment.enums.sweep.ForceSweepRequestStatus[
                            "WAITING_SWEEP"
                        ],
                        help_text="force sweep request status",
                        max_length=24,
                        null=True,
                    ),
                ),
                ("extra", models.TextField()),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("modified_time", models.DateTimeField(auto_now=True)),
            ],
            options={
                "unique_together": {("org_id", "request_id")},
            },
        ),
    ]
