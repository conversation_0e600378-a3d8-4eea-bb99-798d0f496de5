import uuid
from decimal import Decimal
from typing import List, Optional

from cobo_libs.utils.dao import BaseDao, register_dao

from waas2.devapi.pagination import SortDirection, process_db_pagination
from waas2.payment.data.sweep import ForceSweepRequestExtra
from waas2.payment.enums.sweep import ForceSweepRequestStatus, ForceSweepSource
from waas2.payment.models.sweep import ForceSweepRequest


@register_dao(ForceSweepRequest)
class ForceSweepRequestDao(BaseDao):
    @classmethod
    def create(
        cls,
        org_id: str,
        request_id: str,
        wallet_id: str,
        token_id: str,
        amount: Decimal,
        source: ForceSweepSource,
        address: Optional[str] = None,
    ) -> ForceSweepRequest:
        return super(ForceSweepRequestDao, cls).create(
            uuid=str(uuid.uuid4()),
            request_id=request_id,
            org_id=org_id,
            wallet_id=wallet_id,
            token_id=token_id,
            amount=amount,
            source=source,
            status=ForceSweepRequestStatus.WAITING_SWEEP,
            address=address,
        )

    @classmethod
    def update_sweep_task_ids(
        cls,
        request_id: int,
        sweep_task_ids: List[str],
        to_status: ForceSweepRequestStatus,
    ) -> ForceSweepRequest:
        extra = ForceSweepRequestExtra(sweep_task_ids=sweep_task_ids).to_json()
        return cls.update_by_id_directly(_id=request_id, status=to_status, extra=extra)

    @classmethod
    def update_status(cls, request_id: int, to_status: ForceSweepRequestStatus):
        return cls.update_by_id_directly(_id=request_id, status=to_status)

    @classmethod
    def list_by_pending_status(cls) -> List[ForceSweepRequest]:
        return list(
            ForceSweepRequest.objects.filter(
                status__in=ForceSweepRequestStatus.list_pending_statuses()
            )
        )

    @classmethod
    def get_by_uuid(cls, uuid: str) -> Optional[ForceSweepRequest]:
        return ForceSweepRequest.objects.filter(uuid=uuid).first()

    @classmethod
    def get_by_org_id_and_request_id(
        cls, org_id: str, request_id: str
    ) -> Optional[ForceSweepRequest]:
        return ForceSweepRequest.objects.filter(
            org_id=org_id, request_id=request_id
        ).first()

    @classmethod
    def search_user_force_sweep_request(
        cls, org_id: str, request_id: str, limit: int, before: str, after: str
    ):
        queryset = ForceSweepRequest.objects.filter(
            org_id=org_id, source=ForceSweepSource.USER
        )
        if request_id:
            queryset = queryset.filter(request_id=request_id)
        if not limit:
            return list(queryset.order_by("-created_time")), None

        ret, pagination = process_db_pagination(
            record_manager=queryset,
            sort_by="id",
            direction=SortDirection("asc"),
            limit=limit,
            before=before,
            after=after,
        )
        return ret, pagination
