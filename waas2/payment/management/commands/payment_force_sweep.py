import logging
import signal

import gevent
from django.core.management.base import BaseCommand

from waas2.payment.services.force_sweep_service import ForceSweepService

logger = logging.getLogger("waas2.payment.management.commands.force_sweep")


class Command(BaseCommand):
    help = "Force Sweep Service"

    def __init__(self, *args, **kwargs):
        self._service_list = None
        super(Command, self).__init__(*args, **kwargs)

    def add_arguments(self, parser):
        parser.add_argument(
            "-i",
            "--interval",
            required=False,
            action="store",
            help="Interval time in seconds for main loop, default is 1s",
        )

    def _on_exit(self):
        logger.warning("\n\tStopped by CTRL-C, killing all services..")
        if self._service_list:
            gevent.killall(self._service_list)

    def handle(self, *args, **options):
        gevent.signal_handler(signal.SIGTERM, self._on_exit)
        gevent.signal_handler(signal.SIGINT, self._on_exit)

        if options["interval"]:
            interval = int(options["interval"])
        else:
            interval = 3

        logger.info(f"Start force sweep services. interval: {interval}")

        service = ForceSweepService(interval)
        service.start()

        self._service_list = [service]
        gevent.joinall(self._service_list)
