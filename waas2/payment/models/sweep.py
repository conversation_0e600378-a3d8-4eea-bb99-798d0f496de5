from cobo_libs.utils.enhanced_model import CoboModel
from django.conf import settings
from django.db import models

from custody.cobo.utils.models import UnsignedBigIntegerField
from waas2.payment.enums.sweep import ForceSweepRequestStatus, ForceSweepSource


class ChainSweepThreshold(CoboModel):
    id = UnsignedBigIntegerField(primary_key=True)
    chain_id = models.CharField(max_length=64, help_text="链ID")
    chain_identifier = models.CharField(max_length=64, help_text="链标识符")
    token_id = models.CharField(
        max_length=64, null=True, blank=True, help_text="代币ID，如果为空则适用于所有代币"
    )
    min_sweep_amount = models.DecimalField(
        max_digits=65, decimal_places=18, help_text="最小归集金额"
    )
    sweep_percent = models.DecimalField(
        max_digits=5, decimal_places=2, default=1.0, help_text="归集百分比"
    )

    class Meta:
        unique_together = (("chain_id", "chain_identifier", "token_id"),)

    def __str__(self):
        return f"ChainSweepThreshold(chain_id={self.chain_id}, token_id={self.token_id}, min_sweep_amount={self.min_sweep_amount})"

    def logger_extra(self, **kwargs) -> dict:
        return {
            "chain_sweep_threshold_chain_id": self.chain_id,
            "chain_sweep_threshold_token_id": self.token_id,
            "chain_sweep_threshold_min_sweep_amount": str(self.min_sweep_amount),
            **kwargs,
        }


class ForceSweepRequest(CoboModel):
    id = UnsignedBigIntegerField(primary_key=True)
    uuid = models.CharField(unique=True, null=False, max_length=64)

    org_id = models.CharField(null=False, max_length=64)
    request_id = models.CharField(max_length=128, null=False, db_index=True)
    wallet_id = models.CharField(null=False, max_length=64)
    address = models.CharField(null=True, max_length=settings.ADDR_LENGTH)
    token_id = models.CharField(null=False, max_length=settings.COIN_CODE_LENGTH)
    amount = models.DecimalField(max_digits=65, decimal_places=18, null=False)
    source = models.IntegerField(default=ForceSweepSource.USER, db_index=True)
    status = models.CharField(
        max_length=24,
        null=True,
        default=ForceSweepRequestStatus.WAITING_SWEEP,
        help_text="force sweep request status",
        db_index=True,
    )
    extra = models.TextField()

    created_time = models.DateTimeField(auto_now_add=True)
    modified_time = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = (("org_id", "request_id"),)

    def __str__(self):
        return (
            f"id:{self.id}, "
            f"uuid:{self.uuid}, "
            f"org_id:{self.org_id}, "
            f"wallet_id: {self.wallet_id}, "
            f"token_id: {self.token_id}, "
            f"amount: {self.amount}, "
            f"status: {self.status}, "
            f"created_time:{self.created_time}, "
        )

    def logger_extra(self) -> dict:
        return {
            "id": self.id,
            "org_id": self.org_id,
        }
