import logging
import time

import gevent
from cobo_libs.utils.logger import reset_sql_logging
from django.db import close_old_connections

from waas2.payment.dao.sweep import ForceSweepRequestDao
from waas2.payment.handlers.force_sweep import ForceSweepHandler

logger = logging.getLogger("waas2.payment.services.force_sweep_service")


class ForceSweepService(gevent.Greenlet):
    def __init__(self, interval: int = 10):
        super().__init__()
        self._interval = interval

    def _run(self):
        interval = self._interval
        logger.info(f"Start force sweep service. " f"interval: {interval}")

        try:
            while True:
                reset_sql_logging()
                self.handle_force_sweep_requests()
                close_old_connections()
                time.sleep(self._interval)
        except Exception as e:
            logger.warning(
                f"[payment.ForceSweepService._run] exception: {e}", exc_info=True
            )

    @classmethod
    def handle_force_sweep_requests(cls):
        requests = ForceSweepRequestDao.list_by_pending_status()
        for request in requests:
            try:
                ForceSweepHandler.process_force_sweep(request=request)
            except Exception as e:
                logger.error(
                    f"process force sweep error, request: {request} error: {e}"
                )
