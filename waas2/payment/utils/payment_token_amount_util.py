from decimal import ROUND_UP, Decimal
from typing import Union

from django.conf import settings


class PaymentTokenAmountUtil:
    @classmethod
    def get_token_decimals(cls, token_id: str) -> int:
        if token_id in getattr(settings, "PAYMENT_TOKEN_DECIMALS", {}):
            return settings.PAYMENT_TOKEN_DECIMALS[token_id]
        return getattr(settings, "DEFAULT_PAYMENT_TOKEN_DECIMALS", 2)

    @classmethod
    def get_minimum_effective_amount(cls, token_id: str) -> Decimal:
        """
        获取该 token 精度下最小有效单位（例如：精度 6 → "0.000001"）
        """
        decimals = cls.get_token_decimals(token_id)
        return Decimal("1") / Decimal(10**decimals)

    @classmethod
    def quantize_token_amount(
        cls, token_id: str, amount: Union[Decimal, int, str]
    ) -> Decimal:
        """
        按 token 精度向上进位保留有效位数。
        """
        if not isinstance(amount, Decimal):
            amount = Decimal(str(amount))
        decimals = cls.get_token_decimals(token_id)
        exp = Decimal(f"1e-{decimals}")
        return amount.quantize(exp, rounding=ROUND_UP)
