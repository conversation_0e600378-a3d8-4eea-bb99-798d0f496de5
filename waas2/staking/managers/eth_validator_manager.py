import datetime
import logging
from typing import Dict, List, Tuple

from waas2.staking.daos import ETHValidatorDao
from waas2.staking.data.data import ValidatorStatus
from waas2.staking.models import ETHValidator
from waas2.staking.protocols.eth_beacon_provider import EthBeaconProviderFactory
from waas2.staking.protocols.eth_staking.base import BaseValidatorAPI

logger = logging.getLogger("waas2.staking")


class ETHValidatorManager(object):
    @classmethod
    def _add_timeline(cls, v: ETHValidator, status: str, time: str) -> bool:
        if not v.timeline:
            v.timeline = {}

        if status not in v.timeline:  # Update only once for each status.
            v.timeline[status] = time
            return True

        return False

    @classmethod
    def sync_validator_status(cls, provider: BaseValidatorAPI, v: ETHValidator):
        info = provider.get_validator(v.pubkey)
        if not info:
            logger.info(f"ETHValidator not found. pubkey: {v.pubkey}")
            return

        if info.timeline:
            if info.status not in info.timeline:
                # If not time returned, set now as the updated time.
                info.timeline[info.status] = datetime.datetime.now().isoformat()

        ETHValidatorDao.update_by_id(
            v.id,
            index=info.index,
            status=info.status,
            timeline=info.timeline or {},
        )

    @classmethod
    def sync_rewards(cls, provider: BaseValidatorAPI, validators: List[ETHValidator]):
        pubkeys = [(v.pubkey, v.index) for v in validators]
        reward_infos = provider.get_rewards(pubkeys)
        assert len(validators) == len(reward_infos)
        for v, reward in zip(validators, reward_infos):
            consensus_rewards = reward.consensus
            if consensus_rewards > v.consensus_rewards:
                ETHValidatorDao.update_by_id(
                    v.id,
                    consensus_rewards=consensus_rewards,
                    execution_rewards=reward.execution,
                    apy=reward.apy,
                )

    @classmethod
    def _group_by_provider(
        cls, validators: List[ETHValidator]
    ) -> Dict[Tuple[str, str], List[ETHValidator]]:
        validators_map = {}  # (provider,chain) => validator
        for v in validators:
            map_key = (v.provider, v.chain_id)
            if map_key not in validators_map:
                validators_map[map_key] = []
            validators_map[map_key].append(v)
        return validators_map

    @classmethod
    def sync_validators_data(cls):
        validators = ETHValidatorDao.query_living()
        validators_map = cls._group_by_provider(validators)

        for provider_name, chain_id in validators_map:
            try:
                provider = EthBeaconProviderFactory.get_provider(
                    provider_name, chain_id
                )
                assert provider, f"unknown provider for {provider_name} {chain_id}"

                validators = validators_map[(provider_name, chain_id)]
                for v in validators:
                    cls.sync_validator_status(provider, v)

                validators = [
                    v for v in validators if v.status == ValidatorStatus.ACTIVE
                ]
                if validators:
                    cls.sync_rewards(provider, validators)
            except Exception as e:
                logger.exception(
                    f"ETHValidator {provider_name} {chain_id} sync error: {e}"
                )
