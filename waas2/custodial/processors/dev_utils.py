import logging
import uuid

from cobo_waas2 import (
    AddressTransferDestination,
    AddressTransferDestinationAccountOutput,
    CustodialTransferSource,
    TransferDestinationType,
    TransferParams,
    TransferSource,
)
from cobo_waas2.models import WalletSubtype as WAASWalletSubtype
from cobo_waas2.models.transfer_destination import TransferDestination

from custody.cobo.settings.constants.coin_codes import COIN_SETH
from custody.cobo.utils.precondition import check
from custody.custody.data.enums import SystemType
from custody.custody.managers.custody_wallet import CustodyWalletManager
from waas2.transactions.dev.controllers.transaction import TransactionController

logger = logging.getLogger("waas2.custodile.dev_utils")


class CustodialDevUtil:
    @classmethod
    def init_test_coins(cls, token_id: str, address: str):
        if token_id == COIN_SETH:
            cls.send_test_coin(COIN_SETH, address, "0.01")

    @classmethod
    def send_test_coin(cls, token_id: str, address: str, amount: str):
        logger.info(
            f"send_test_coin. token {token_id}, amount {amount}, to addr {address}"
        )
        from_system_asset_wallet = (
            CustodyWalletManager.get_custody_wallet_for_system_type(
                system_type=SystemType.TEST_ASSET,
            )
        )
        check(from_system_asset_wallet)

        TransactionController.transfer_for_dev_api(
            request_data=TransferParams(
                request_id=str(uuid.uuid4()),
                source=TransferSource(
                    CustodialTransferSource(
                        source_type=WAASWalletSubtype.ASSET,
                        wallet_id=from_system_asset_wallet.uuid,
                    )
                ),
                token_id=token_id,
                destination=TransferDestination(
                    AddressTransferDestination(
                        destination_type=TransferDestinationType.ADDRESS,
                        account_output=AddressTransferDestinationAccountOutput(
                            address=address, amount=amount
                        ),
                    )
                ),
            ),
            org_id=from_system_asset_wallet.org.uuid,
            biz_org_id=from_system_asset_wallet.org.id,
            api_request_info=None,
            sign_info=None,
        )
